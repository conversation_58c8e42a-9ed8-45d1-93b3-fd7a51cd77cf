<template>
  <div>
    <Card>
      <Row>
        <Col>
          <FilterTable
            :data="tableData"
            :columns="columns"
            :default-filter="
              this.$store.state.app.tableFilter.achieveTable ? this.$store.state.app.tableFilter.achieveTable : {}
            "
            @on-search="onFilterChanged"
          />
          <Table v-show="false" ref="table" />
        </Col>
      </Row>
      <br />
      <Row>
        <Col span="2">
          <Button style="width: 80%" type="primary" @click="refreshTable">刷新</Button>
        </Col>
        <Col span="2">
          <Button style="width: 80%" type="primary" @click="download">导出 CSV</Button>
        </Col>
        <Col offset="13">
          <Page
            :total="totalCnt"
            :current="curPage"
            :page-size="pageSize"
            show-elevator
            show-total
            @on-change="changePage"
          />
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import FilterTable from '@/view/filter-table/filter-table'
import { examAchieve, examAchieveCSV } from '@/api/achieve'
import { userProfileReq } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'
import { WhitePre } from '@/libs/render-item'
import _ from 'lodash'

export default {
  name: 'StudentAchieve',
  components: { FilterTable },
  data() {
    return {
      curCourse: null,
      tableData: [],
      filter: {},
      columns: [
        { title: 'id', key: 'id' },
        {
          title: '姓名',
          key: 'student_name',
          filter: {},
          render: (h, params) => WhitePre(h, params.row['student_name'])
        },
        {
          title: '学号',
          key: 'studentid',
          filter: {},
          render: (h, params) => WhitePre(h, params.row['studentid'])
        },
        { title: '系号', key: 'department', filter: {} },
        { title: '当前项目', key: 'current_project_name', filter: {} },
        { title: '教师', key: 'teacher_name', filter: {} },
        { title: '学生成绩成就', key: 'achieve' }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.achieveTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.achieveTable)
    }
    this.curPage = this.$store.state.app.tablePage.achieveTable ? this.$store.state.app.tablePage.achieveTable : 1
    this.loadCurCourse()
  },
  methods: {
    loadCurCourse() {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course === null) {
            this.$Modal.info({
              title: '请在课程信息/课程总览上选择当前课程'
            })
          } else {
            this.curCourse = res.data.course.id
            this.loadTable()
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    loadTable() {
      examAchieve(this.curCourse, 'get', {
        page: this.curPage,
        page_size: this.pageSize,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['achievements']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'achieveTable' })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    changePage(index) {
      this.curPage = index
      this.loadTable()
    },
    refreshTable() {
      this.loadTable()
    },
    onFilterChanged(search) {
      search = this.refactorSearchObject(search)
      this.$store.commit('setTableFilter', {
        filter: search,
        name: 'achieveTable'
      })
      if (this.curCourse) {
        this.curPage = 1 // 重置 page index
        this.loadTable()
      }
    },
    download() {
      examAchieveCSV(this.curCourse, {
        page: this.curPage,
        page_size: this.pageSize,
        ...this.filter
      })
        .then((res) => {
          const csvData = res.data.split('\n')
          csvData.shift() // delete header
          csvData.pop() // delete ""
          this.$refs['table'].exportCsv({
            filename: '学生成绩成就.csv',
            columns: [
              { key: '姓名' },
              { key: '学号' },
              { key: '系号' },
              { key: '当前项目' },
              { key: '教师' },
              { key: '学生成绩成就' }
            ],
            data: csvData.map((item) => {
              const splitItem = item.split(',')
              const result = {}
              result['姓名'] = splitItem[1]
              result['学号'] = splitItem[2]
              result['系号'] = splitItem[3]
              result['当前项目'] = splitItem[4]
              result['学生成绩成就'] = splitItem[5]
              result['教师'] = (splitItem.length > 6 ? splitItem[6] : '').replace(/\r/g, '')
              return result
            })
          })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {} // reset filter
      Object.keys(search).forEach((key) => {
        if (key === 'department') {
          this.filter[key + '__exact'] = search[key]
        } else {
          if (key === 'studentid') {
            this.filter['student_id__contains'] = search[key]
          } else {
            this.filter[key + '__contains'] = search[key]
          }
        }
      })
      return searchNew
    }
  }
}
</script>
