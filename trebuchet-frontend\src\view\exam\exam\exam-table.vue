<template>
  <Card>
    <Table :data="tableData" :columns="columns" />
    <div style="margin: 10px; overflow: hidden">
      <div style="float: left">
        <Button type="primary" @click="onClick">创建考试</Button>
      </div>
      <div style="float: right">
        <Page
          :total="totalCnt"
          :current="curPage"
          :page-size="pageSize"
          show-total
          show-elevator
          @on-change="changePage"
        />
      </div>
    </div>
  </Card>
</template>

<script>
import { examReq, examIdReq } from '@/api/exam'
import { userProfileReq } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'
import { ActionButton, LinkButton, Spacer, Tag } from '@/libs/render-item'

export default {
  name: 'ExamTable',
  data() {
    return {
      tableData: [],
      columns: [
        {
          title: 'id',
          key: 'id'
        },
        {
          title: 'status',
          key: 'active',
          render: (h, params) => (params.row.active === true ? Tag(h, 'green', '激活') : Tag(h, 'blue', '未激活'))
        },
        {
          title: 'date',
          key: 'date'
        },
        {
          title: 'Action',
          render: (h, params) =>
            h('div', [
              LinkButton(h, params.row.id, 'exam_detail', '查看', false),
              Spacer(h),
              ActionButton(h, () => this.onDelete(params.row.id), '删除', false)
            ])
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      order_by: '-date'
    }
  },
  mounted() {
    if (this.$store.state.user.userDefaultCourse === -1) {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
            this.loadData()
          } else {
            this.$Modal.info({
              title: '请在课程信息/课程总览选择当前课程'
            })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    } else {
      this.loadData()
    }
  },
  methods: {
    loadData() {
      examReq('get', {
        page: 1,
        page_size: this.pageSize,
        order_by: this.order_by,
        course__id__exact: this.$store.state.user.userDefaultCourse
      })
        .then((res) => {
          this.tableData = res.data['exams']
          this.totalCnt = res.data['total_count']
          this.curPage = 1
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
    },
    changePage(index) {
      examReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        course__id__exact: this.$store.state.user.userDefaultCourse
      })
        .then((res) => {
          this.tableData = res.data['exams']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onClick() {
      this.$router.push({ name: 'exam_create' })
    },
    onDelete(id) {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          examIdReq('delete', id, {})
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.loadData()
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    }
  }
}
</script>
