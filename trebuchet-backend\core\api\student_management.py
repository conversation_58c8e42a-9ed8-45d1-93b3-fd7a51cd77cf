"""
define student management's view-layer functions
"""
import io
import time
from copy import deepcopy

import pandas
from django.core.exceptions import FieldError
from django.contrib.auth import get_user_model
from django.core.files.base import ContentFile
from django.core.paginator import Paginator
from django.db import IntegrityError
from django.db.models import Q, QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)
from PIL import Image

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_STUDENT_CHANGE, CORE_STUDENT_CREATE,
                                  CORE_STUDENT_DELETE, CORE_STUDENT_VIEW)
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api,
                            require_course_permission)
from core.forms.student import StudentBasicInfo
from core.models.course import Course
from core.models.student import Student

_DEFAULT_EXCLUDE = ['photo']
_DEFAULT_GET = ['student_id', 'name', 'gender', 'department', 'official_class']
_FORBIDDEN_FIELD = ['photo']
_ALLOWED_GET_REQUEST_ATTR = {'want_list': list}
_GET_ATTR_DEFAULT_VALUE = {'want_list': []}
_ALLOWED_GET_LIST_REQUEST_ATTR = {'page_size': int, 'page_num': int}
_GET_LIST_DEFAULT_VALUE = {'page_size': 10, 'page_num': 1}
_DEFAULT_PAGE_SIZE = 10


def _get_standard_info(default_dict: dict, new_dict: dict) -> dict:
    re_dict = deepcopy(default_dict)
    if not isinstance(new_dict, dict):
        return re_dict
    for k in new_dict.keys():
        re_dict[k] = new_dict[k]
    return re_dict


def _validate_student_info(student_info: dict) -> bool:
    fields = [field.name for field in Student._meta.get_fields()] + ["email"]
    for key in student_info.keys():
        if key not in fields or key in _FORBIDDEN_FIELD:
            return False
    info = StudentBasicInfo(student_info)
    return info.is_valid()


def validate_get_request(request: HttpRequest) -> bool:
    """validate student post request

    Args:
        request (HttpRequest): get request

    Returns:
        bool: indicate the request is valid.
    """
    data: dict = parse_data(request)
    if data is None:
        return True
    if not isinstance(data, dict):
        return False
    for k in data:
        if k not in _ALLOWED_GET_REQUEST_ATTR \
                or not isinstance(data[k], _ALLOWED_GET_REQUEST_ATTR.get(k)):
            return False
    want_list = data.get('want_list', [])
    fields = [field.name for field in Student._meta.get_fields()]
    for key in want_list:
        if key not in fields:
            return False
    return True


def validate_put_request(request: HttpRequest) -> bool:
    """validate student put request

    Args:
        request (HttpRequest): put request

    Returns:
        bool: indicate the request is valid.
    """
    data: dict = parse_data(request)
    fields = [field.name for field in Student._meta.get_fields()] + ["password", "email"]
    for key in data.keys():
        if key not in fields or key in _FORBIDDEN_FIELD:
            return False
    return True


def _validate_student_list(func):
    def _inner(request: HttpRequest, *args, **kwargs):
        data: dict = parse_data(request)
        info = data.get('students_list')
        if info is None or not isinstance(info, list):
            response = JsonResponse(
                {'code': 400, 'error_msg': 'ErrorCode.INVALID_REQUEST_ARGS'})
            response.status_code = ErrorCode.INVALID_REQUEST_ARGS.value
            return response
        students_list = info
        wrong_format_students: list = []
        for student in students_list:
            if not _validate_student_info(student):
                wrong_format_students.append(student)
        if wrong_format_students:
            data = {'wrong_format_students': wrong_format_students}
            response = JsonResponse(data=data)
            response.status_code = ErrorCode.INVALID_REQUEST_ARGS.value
            return response
        response = func(request, *args, **kwargs)
        return response

    return _inner


def validate_get_list_request(request: HttpRequest) -> bool:
    """validate student get list request

    Args:
        request (HttpRequest): get request

    Returns:
        bool: indicate the request is valid.
    """
    data: dict = parse_data(request)
    print(data)
    if data is None:
        return True
    if not isinstance(data, dict):
        return False
    for k in data.keys():
        if k not in _ALLOWED_GET_LIST_REQUEST_ATTR \
                or not isinstance(data[k], _ALLOWED_GET_LIST_REQUEST_ATTR.get(k)):
            return False
    return True


def _validate_post_student_photo(request: HttpRequest) -> bool:
    if 'student_photo' not in request.FILES.keys():
        return False
    try:
        img = Image.open(request.FILES['student_photo'])
    except IOError:
        return False
    file_type: str = img.format.lower()
    if file_type not in ['jpg', 'jpeg', 'png', 'gif']:
        return False
    return True


def _transfer_and_get_content_file(img: Image, target_format: str) -> ContentFile:
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format=target_format)
    img_byte_arr = img_byte_arr.getvalue()
    return ContentFile(img_byte_arr)


def _create_student_user(stu: Student, ignore_duplicate=True):
    if stu.user is not None:
        if ignore_duplicate:
            return
        raise Exception("用户 {} 已经存在，不能创建".format(stu.student_id))
    user = get_user_model().objects.create_user(stu.student_id)
    user.first_name = stu.name
    user.last_name = "学生"
    user.save()
    stu.user = user
    stu.save()


def _change_student_password(stu: Student, pwd: str):
    if stu.user is None:
        _create_student_user(stu)
    stu.user.set_password(pwd)
    stu.user.save()


def _change_student_email(stu: Student, email: str):
    if stu.user is None:
        _create_student_user(stu)
    stu.user.email = email
    stu.user.save()


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_VIEW])
@require_GET
@require_item_exist(model=Student, field="student_id", item="student_id")
@validate_args(func=validate_get_request)
def retrieve_student_detail(request: HttpRequest, student_id: str) -> dict:
    """Deal with student get request

    [method]: GET

    [route]: /api/students/<int:id>
    """
    data: dict = parse_data(request)
    student = Student.objects.get(**{"student_id": student_id})
    if data is not None and 'want_list' in data.keys():
        student_info: dict = model_to_dict(student, fields=data['want_list'])
        if 'email' in data['want_list']:
            student_info.update({'email': None if student.user is None else student.user.email})
    else:
        student_info: dict = model_to_dict(student, exclude=_DEFAULT_EXCLUDE)
        student_info.update({'email': None if student.user is None else student.user.email})
    return success_api_response(student_info)


def course_id_filter_helper(course_key: str, course_value: int):
    """[summary]
    """
    key = "instructorclass__belong_to__" + course_key.split("__")[-1]
    return Q(**{key: course_value})


def user_filter(user_key: str, user_value: int):
    """[summary]
    """
    key = "user__" + user_key
    return Q(**{key: user_value})


def student_to_dic(student: Student):
    info = model_to_dict(student, exclude=['photo'])
    info.update({'email': None if student.user is None else student.user.email})
    return info


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_VIEW])
@require_GET
@require_course_permission
@query_filter(fields=[("name", str), ("student_id", str), ('gender', int), ('department', int), ('official_class', str),
                      ("email", str)],
              custom={"email": user_filter})
@query_distinct(fields=["name", 'student_id', 'gender', 'department', 'official_class'], model=Student)
@query_order_by(fields=["name", 'student_id', 'gender', 'department', 'official_class'])
@query_page(default=10)
def list_students(request: HttpRequest, course: Course, *args, **kwargs):
    """Deal with students get all request

    [route]: /api/students

    [method]: GET
    """
    students_all = Student.objects.count()
    students: QuerySet = Student.objects.all()
    # filter
    student_filter = kwargs.get('filter')

    try:
        students = students \
            .filter(student_filter) \
            .filter(instructorclass__belong_to=course.id)
        # 以上 filter 会产生重复数据，在下方使用 distinct() 去掉。
        # https://stackoverflow.com/questions/30487056/django-queryset-contains-duplicate-entries
        # 参见 https://git.cscore.net.cn/trebuchet/backend/-/merge_requests/442#note_9784
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")

    # order by
    order_by = kwargs.get('order_by')
    if order_by is None:
        order_by = ['student_id']

    students = students.order_by(*order_by)

    students = students.distinct()
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')

    paginator = Paginator(students, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        students_info = []
    else:
        students = paginator.get_page(page).object_list
        students_info = list(map(student_to_dic, students))
    data = {
        'students_all': students_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'students': students_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_VIEW])
@require_GET
@query_filter(fields=[("name", str), ("student_id", str), ('gender', int), ('department', int), ('official_class', str),
                      ("course_id", str), ("email", str)],
              custom={"course_id": course_id_filter_helper, "email": user_filter})
@query_distinct(fields=["name", 'student_id', 'gender', 'department', 'official_class'], model=Student)
@query_order_by(fields=["name", 'student_id', 'official_class', 'gender', 'department'])
def list_students_csv(request: HttpRequest, *args, **kwargs):
    """Deal with students get all request

    [route]: /api/students-csv

    [method]: GET
    """
    students: QuerySet = Student.objects.all()
    # filter
    student_filter = kwargs.get('filter')
    try:
        students = students.filter(student_filter)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")

    # order by
    order_by = kwargs.get('order_by')
    try:
        if order_by is not None:
            students = students.order_by(*order_by)
        else:
            students = students.order_by('student_id')
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Order Method.")

    students_list = map(student_to_dic, students)
    return HttpResponse(pandas.DataFrame(students_list).to_csv())


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_CREATE])
@_validate_student_list
@require_POST
def create_students(request: HttpRequest):
    """Deal with student post request

    [method]: POST

    [route]: /api/students
    """
    data: dict = parse_data(request)
    students_list: list = data['students_list']
    duplicate_list = []
    created = 0
    for student_dict in students_list:
        email = None if 'email' not in student_dict else student_dict['email']
        student_dict.pop('email', None)
        if Student.objects.filter(student_id=student_dict['student_id']).exists():
            student = Student.objects.filter(student_id=student_dict['student_id'])[0]
            duplicate_list.append(student_dict['student_id'])
            _create_student_user(student, True)
            Student.objects.filter(student_id=student_dict['student_id']).update(**student_dict)
        else:
            student = Student.objects.create(**student_dict)
            _create_student_user(student, False)
            created += 1
            if email is None:
                # TODO: 修改默认邮件地址的产生方式（使它变得比较可配置一点）
                email = "{}@buaa.edu.cn".format(student_dict["student_id"])
            _change_student_email(student, email)
    data = {'create_number': created, 'duplicate_list': duplicate_list}
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_CHANGE])
@require_http_methods(["PUT"])
@validate_args(func=validate_put_request)
@require_item_exist(model=Student, field="student_id", item="student_id")
def update_student(request: HttpRequest, student_id: str) -> dict:
    """Deal with student put request

    [method]: PUT

    [route]: /api/students/<int:id>
    """
    data: dict = parse_data(request)
    query = Student.objects.get(**{"student_id": student_id})
    for key in data.keys():
        if data[key] is None:
            continue
        if key == "password":
            _change_student_password(query, data[key])
        if key == "email":
            _change_student_email(query, data[key])
        else:
            setattr(query, key, data[key])
    try:
        query.save()
    except IntegrityError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGUMENT_ERROR, '属性名称错误')
    return success_api_response({"result": "Ok, student information updated."})


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_DELETE])
@require_http_methods(["DELETE"])
@require_item_exist(model=Student, field="student_id", item="student_id")
def remove_student(request: HttpRequest, student_id: str) -> dict:
    """Deal with student delete request

    [method]: DELETE

    [route]: /api/students/<int:id>
    """
    Student.objects.filter(**{"student_id": student_id}).delete()
    # refactor needed
    return success_api_response({'result': 'delete success'})


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_CHANGE])
@require_http_methods(["POST"])
@validate_args(func=_validate_post_student_photo)
@require_item_exist(model=Student, field="id", item="id")
def update_student_photo(request: HttpRequest, query_id: int):
    """Deal with update student photo

    [method]: POST

    [route]: /api/students/<int:id>/photo
    """
    student = Student.objects.get(id=query_id)
    file_name = str(student.student_id) + '-' + \
                time.strftime('%Y-%m-%d-%H-%M-%S', time.localtime(time.time()))

    file_name = file_name + '.'

    img: Image = Image.open(request.FILES['student_photo'])
    file_type: str = img.format.lower()
    content_file = _transfer_and_get_content_file(img, file_type.upper())
    student.photo.save(file_name + file_type, content_file, save=True)

    student.save()

    return success_api_response({'result': 'update photo success!'})


@response_wrapper
@jwt_auth(perms=[CORE_STUDENT_VIEW])
@require_http_methods(["GET"])
@require_item_exist(model=Student, field="id", item="id")
def get_student_photo(request: HttpRequest, query_id: int):
    """Deal with get student photo

    [method]: GET

    [route]: /api/students/<int:id>/photo
    """
    student = Student.objects.get(id=query_id)
    return HttpResponse(student.photo, content_type='image/png')


STUDENT_DETAIL_API = wrapped_api({
    "get": retrieve_student_detail,
    "put": update_student,
    "delete": remove_student
})

STUDENT_SET_API = wrapped_api({
    'get': list_students,
    'post': create_students
})

STUDENT_PHOTO_API = wrapped_api({
    'get': get_student_photo,
    'post': update_student_photo
})

STUDENT_SET_CSV_API = wrapped_api({
    'get': list_students_csv,
})
