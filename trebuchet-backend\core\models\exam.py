"""
declare Exam model
"""

from django.db import models

from core.models.course import Course
from core.models.permissions import (EXAM_CHANGE, EXAM_CREATE, EXAM_DELETE,
                                     EXAM_VIEW)
from core.models.project import Project


class Exam(models.Model):
    """This model describes an exam arrangement.

    此模型用于描述考试安排。

    Attributes:
        date: A DateField represents the date when the exam should be held.
        project: A ManyToManyField represents projects which exam holds.
        active: An <PERSON><PERSON> indicating if the exam is active.

    属性:
        date: DateField, 用于表示考试举行的日期
        project: ManyToManyField, 用于表示该考试所拥有的 Project
        active: BooleanField, 用于表示该考试是否处于活跃状态
    """

    date = models.DateField()
    project = models.ManyToManyField(to=Project, through='ProjectInExam')
    course = models.ForeignKey(to=Course, on_delete=models.CASCADE, null=True)
    active = models.BooleanField(default=False)

    class Meta:
        default_permissions = ()
        permissions = [
            (EXAM_CHANGE, EXAM_CHANGE),
            (EXAM_CREATE, EXAM_CREATE),
            (EXAM_DELETE, EXAM_DELETE),
            (EXAM_VIEW, EXAM_VIEW)
        ]
