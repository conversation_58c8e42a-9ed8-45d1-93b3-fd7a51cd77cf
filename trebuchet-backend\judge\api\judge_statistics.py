"""
show judge statistics
"""
import json
from collections import defaultdict
from datetime import datetime, timedelta, time

import pandas
import pytz
from django.db.models import Subquery, F, Func, Value, CharField, Count
from django.forms import model_to_dict
from django.http import HttpRequest, HttpResponse
from django.views.decorators.http import require_GET

from core.api.auth import jwt_auth
from core.api.student_progress import parse_requirement_with_dict_data
from core.api.utils import (ErrorCode, failed_api_response, require_item_exist,
                            response_wrapper, success_api_response,
                            wrapped_api)
from core.interface.judge_helper import (
    get_problems_in_project, get_projects_in_exam,
    get_total_student_ids_in_course, get_total_student_ids_in_project_in_exam)
from core.interface.student import map_student_ids_to_department
from core.models.exam import Exam
from core.models.exam_record import ExamRecord
from core.models.instructor_class import InstructorClass
from core.models.project import Project
from core.models.project_in_exam import ProjectInExam
from core.models.student import Student
from judge.api.permissions import (JUDGE_VIEW_EXAM_PASS, JUDGE_VIEW_PASS_RANK,
                                   JUDGE_VIEW_PROBLEM_PASS,
                                   JUDGE_VIEW_PROJECT_PASS,
                                   JUDGE_VIEW_TESTCASE_PASS,
                                   JUDGE_VIEW_PROJECT_SUBMIT)
from judge.constants import ACCEPTED
from judge.models import Problem, ProblemJudgeRecord, TestCaseJudgeRecord

timezone = pytz.timezone('Asia/Shanghai')


def get_delta(pie: ProjectInExam):
    if pie.exam_id == pie.project.course.under_class_exam_id:
        total_student = Student.objects.filter(instructorclass__belong_to=pie.project.course_id) \
            .values_list('student_id', flat=True) \
            .distinct()
        submit_query = ProblemJudgeRecord.objects.filter(
            project_in_exam=pie,
            edx_username__in=total_student).order_by('id')
        print(submit_query.query)
        first_submit = submit_query.first()
        if first_submit is None:
            begin_time = pie.exam.date
        else:
            begin_time = submit_query.first().submitted_at.date()
        begin_time = timezone.localize(datetime.combine(begin_time, time()))
        end_time = timezone.localize(datetime.now())
        delta = timedelta(days=1)
    else:
        total_student = get_total_student_ids_in_project_in_exam(pie.id)
        begin_time = timezone.localize(datetime.combine(pie.exam.date, pie.begin_time))
        end_time = timezone.localize(datetime.now())
        if end_time > begin_time + timedelta(minutes=pie.duration):
            end_time = begin_time + timedelta(minutes=pie.duration)
        delta = timedelta(minutes=1)
    return delta, begin_time, end_time, total_student


def real_time_project_pass(pie_id: int):
    """pie real time pass
    Arguments:
        pie_id {int} -- project in exam id
    Returns:
        dict -- begin time, pass num ....
    """
    pie = ProjectInExam.objects.get(pk=pie_id)

    (delta, begin_time, end_time, total_student) = get_delta(pie)

    total_pjr = ProblemJudgeRecord.objects \
        .filter(pk__in=Subquery(
        ProblemJudgeRecord.objects.filter(
            submitted_at__gte=begin_time,
            submitted_at__lte=end_time,
            project_in_exam=pie,
            judge_result=ACCEPTED,
            edx_username__in=total_student)
            .order_by('edx_username', 'problem')
            .distinct('edx_username', 'problem')
            .values('pk'))) \
        .order_by('id')

    response: dict = {}
    cnt: dict = {}
    student_dict: dict = {}
    student_pass: dict = {}
    problems = {p.id: p.name for p in pie.problems.all()}

    def get_problem_key(pid):
        return '%s_%s' % (pid, problems[pid])

    for problem in problems:
        key = get_problem_key(problem)
        response[key] = []
        cnt[key] = 0
    response[pie.project.name] = []

    now = begin_time
    for pjr in total_pjr:
        while pjr.submitted_at > now:
            for problem in problems:
                key = get_problem_key(problem)
                response[key].append(cnt[key])
            response[pie.project.name].append(len(student_pass))
            now += delta

        problem = pjr.problem_id
        edx_username = pjr.edx_username
        if problem in problems:
            cnt[get_problem_key(problem)] += 1
        if edx_username not in student_dict:
            student_dict[edx_username] = {}
        student_dict[edx_username][problem] = True
        if edx_username not in student_pass:
            if parse_requirement_with_dict_data(pie.pass_requirement, student_dict[edx_username]):
                student_pass[edx_username] = True

    while now <= end_time:
        for problem in problems:
            key = get_problem_key(problem)
            response[key].append(cnt[key])
        response[pie.project.name].append(len(student_pass))
        now += delta

    response['begin_time'] = begin_time
    response['delta'] = delta.total_seconds()
    response['total_student'] = len(total_student)

    return response


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROBLEM_PASS])
@require_GET
@require_item_exist(model=Problem, field="id", item="query_id")
def get_problem_statistics(request: HttpRequest, query_id: int):
    """
    get a problem's and its test cases' statistics
    Args:
        request:
        query_id:

    Returns:

    """
    course = request.GET.get('course', None)
    if course is None:
        failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Need course code")

    problem = Problem.objects.get(id=query_id)
    total_student = get_total_student_ids_in_course(course)
    data: dict = model_to_dict(problem)
    data['test_cases'] = []
    test_cases = problem.test_cases.all()
    for case in test_cases:
        test_case = model_to_dict(case)
        test_case['created_by__username'] = None if case.created_by is None else case.created_by.username
        test_case['judge_data__filename'] = None if case.judge_data is None else case.judge_data.filename
        total_test_case = TestCaseJudgeRecord.objects.filter(test_case=case,
                                                             problem_judge_record__problem=problem,
                                                             problem_judge_record__edx_username__in=total_student)
        passed_test_case = total_test_case.filter(judge_result=ACCEPTED)
        test_case['total_submission'] = total_test_case.count()
        test_case['passed_submission'] = passed_test_case.count()
        test_case['total_student'] = len(total_student)
        test_case['passed_student'] = passed_test_case.values('problem_judge_record__edx_username').distinct().count()
        data['test_cases'].append(test_case)
    data['problem_data__filename'] = None if problem.problem_data is None else problem.problem_data.filename
    data['created_by__username'] = None if problem.created_by is None else problem.created_by.username

    # TODO: improve naming
    total_test_problem = ProblemJudgeRecord.objects.filter(problem=problem, edx_username__in=total_student)
    passed_test_problem = total_test_problem.filter(judge_result=ACCEPTED)
    data['total_submission'] = total_test_problem.count()
    data['passed_submission'] = passed_test_problem.count()

    all_passed_students = passed_test_problem.values_list('edx_username', flat=True).distinct()

    data['total_student'] = len(total_student)
    data['passed_student'] = all_passed_students.count()
    data['passed_student_total_submission'] = total_test_problem \
            .filter(edx_username__in=all_passed_students) \
            .count()

    submitted_students_per_department = map_student_ids_to_department(total_student)
    passed_students_per_department = map_student_ids_to_department(all_passed_students)

    department_specific_count = defaultdict(dict)
    for department in submitted_students_per_department.keys():
        department_specific_count[department]['total_student'] = len(
            submitted_students_per_department[department])
        department_specific_count[department]['passed_student'] = len(
            passed_students_per_department.get(department, []))
    data['department_specific_count'] = dict(department_specific_count)

    submitted_students_per_class = InstructorClass.objects \
            .filter(
                student__student_id__in=total_student,
                belong_to__code=course
            ).annotate(count=Count('student')) \
            .values_list('teacher', 'count')
    passed_students_per_class = InstructorClass.objects \
            .filter(
                student__student_id__in=all_passed_students,
                belong_to__code=course
            ).annotate(count=Count('student')) \
            .values_list('teacher', 'count')
    submitted_students_per_class = dict(submitted_students_per_class)
    passed_students_per_class = dict(passed_students_per_class)
    class_specific_count = defaultdict(dict)
    for teacher, count in submitted_students_per_class.items():
        class_specific_count[teacher]['total_student'] = count
        class_specific_count[teacher]['passed_student'] = passed_students_per_class.get(teacher, 0)
    data['class_specific_count'] = dict(class_specific_count)

    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_EXAM_PASS])
@require_GET
@require_item_exist(model=Exam, field='id', item='query_id')
def get_exam_statistics(request: HttpRequest, query_id: int):
    """
    get a exam statistics
    Args:
        query_id:
        request:

    Returns:

    """
    include_retaker: bool = (request.GET.get('include_retaker') == 'True')
    project_list = get_projects_in_exam(query_id, include_retaker)
    return success_api_response(project_list)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_PASS])
@require_GET
def get_project_in_exam_statistics(request: HttpRequest, query_id: int):
    """
    get project in exam statistic including the problems passing rate
    Args:
        request:
        query_id:

    Returns:

    """
    pie = ProjectInExam.objects.get(pk=query_id)
    total_student = get_total_student_ids_in_project_in_exam(query_id)
    pjrs = ProblemJudgeRecord.objects.select_related('problem') \
        .filter(project_in_exam=pie, edx_username__in=total_student)
    # get problems list and filter
    problems = get_problems_in_project(query_id)
    problems_list: list = []
    for problem in problems:
        total_pjr = pjrs.filter(problem=problem).distinct().count()
        passed_pjr = pjrs.filter(problem=problem, judge_result=ACCEPTED).count()
        passed_student = pjrs.filter(problem=problem, judge_result=ACCEPTED) \
            .values_list('edx_username', flat=True).distinct()
        submitted_students_per_department = map_student_ids_to_department(total_student)
        passed_students_per_department = map_student_ids_to_department(passed_student)

        department_specific_count = defaultdict(dict)
        for department in submitted_students_per_department.keys():
            department_specific_count[department]['total_student'] = len(
                submitted_students_per_department[department])
            department_specific_count[department]['passed_student'] = len(
                passed_students_per_department.get(department, []))
        data: dict = {
            "id": problem.id,
            "name": problem.name,
            "total_submission": total_pjr,
            "passed_submission": passed_pjr,
            "total_student": len(total_student),
            "passed_student": passed_student.count(),
            "department_specific_count": dict(department_specific_count)
        }
        problems_list.append(data)
    res = {
        'data': problems_list
    }
    return success_api_response(res)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_PASS])
@require_GET
@require_item_exist(model=ProjectInExam, field='id', item='pie_id')
def passed_num_per_min(request: HttpRequest, pie_id: int):
    """
    Args:
        pie_id:
        request: GET
    Returns: line chart statistics
    """

    response = real_time_project_pass(pie_id)
    return success_api_response(response)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_TESTCASE_PASS])
@require_GET
def passed_test_case_per_min(request: HttpRequest, pie_id: int, pid: int):
    """
    Args:
        pid:
        pie_id:
        request: GET
    Returns: line chart statistics
    """
    pie = ProjectInExam.objects.get(pk=pie_id)
    problem = Problem.objects.get(pk=pid)
    test_cases = problem.test_cases.all()

    (delta, begin_time, end_time, total_student) = get_delta(pie)

    total_tcjr = TestCaseJudgeRecord.objects.filter(
        pk__in=Subquery(
            TestCaseJudgeRecord.objects.filter(
                problem_judge_record__submitted_at__gte=begin_time,
                problem_judge_record__submitted_at__lte=end_time,
                problem_judge_record__problem=problem,
                problem_judge_record__project_in_exam=pie,
                judge_result=ACCEPTED,
                problem_judge_record__edx_username__in=total_student,
            )
                .order_by('problem_judge_record__edx_username', 'test_case')
                .distinct('problem_judge_record__edx_username', 'test_case')
                .values('pk')
        )
    ).order_by('id')

    def get_test_case_key(testcase):
        return '%s_%s' % (testcase.id, testcase.name)

    response: dict = {}
    cnt: dict = {}
    for test_case in test_cases:
        key = get_test_case_key(test_case)
        response[key] = []
        cnt[key] = 0

    now = begin_time
    for tcjr in total_tcjr:
        while tcjr.problem_judge_record.submitted_at > now:
            for test_case in test_cases:
                key = get_test_case_key(test_case)
                response[key].append(cnt[key])
            now += delta
        test_case = tcjr.test_case
        cnt[get_test_case_key(test_case)] += 1
    while now <= end_time:
        for test_case in test_cases:
            key = get_test_case_key(test_case)
            response[key].append(cnt[key])
        now += delta

    response['begin_time'] = begin_time
    response['delta'] = delta.total_seconds()
    response['total_student'] = len(total_student)

    return success_api_response(response)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_PASS])
@require_GET
@require_item_exist(model=Project, field='id', item='project_id')
def get_project_history_chart(request: HttpRequest, project_id: int):
    """get project history pass rate in each history exam
    Arguments:
        request {HttpRequest} -- GET
        project_id {int} -- project id
    """
    # 1. 获取project
    project = Project.objects.get(pk=project_id)

    # 2. 获取一个project的所有课上
    pie_list = project.projectinexam_set.all()
    response_dict = {}
    for pie in pie_list:
        response_dict[str(pie.exam.date)] = real_time_project_pass(pie.id)

    return success_api_response(response_dict)


# 记录答案对应的分数
answer_map = {
    'A': 100,
    'B': 66,
    'C': 33,
    'F': 0
}


def parse_pie_question_raw(question_raw: str, date: str, questions: dict, question_map: dict):
    if question_raw is not None:
        question_data = json.loads(question_raw)
        # 新版本的字典形式
        if isinstance(question_data, dict):
            for question_type, question_type_info in question_data.items():
                if question_type not in questions:
                    questions[question_type] = {}
                for question in question_type_info:
                    if question not in questions[question_type]:
                        questions[question_type][question] = {}
                        question_map[question] = question_type
                    questions[question_type][question][date] = []
        # 旧版本的列表形式
        elif isinstance(question_data, list):
            questions_list = question_data
            for i, question_list in enumerate(questions_list, start=1):
                # 使用 "题目类型" 加上当前计数 i 作为字典的键
                question_type = f"题目类型 {i}"
                if question_type not in questions:
                    questions[question_type] = {}
                for question in question_list:
                    if question not in questions[question_type]:
                        questions[question_type][question] = {}
                        question_map[question] = question_type
                    questions[question_type][question][date] = []


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_PASS])
@require_GET
@require_item_exist(model=Project, field='id', item='project_id')
def get_project_history_answer_chart(request: HttpRequest, project_id: int):
    """get average answer score in each history exam
    Arguments:
        request {HttpRequest} -- GET
        project_id {int} -- project id
    Returns:
        {
          "category": ["problem1", "problem2", "problem3"],
          "data": [
            {"name": "date1", "data": [score1, score2, score3]},
            {"name": "date2", "data": [score1, score2, score3]},
            {"name": "date3", "data": [score1, score2, score3]}
          ]
        }

    """

    # 多封装一层为类型统计留下扩展
    # 储存形式使用
    # {
    #   type1: {
    #     "problem1": { "date1": [scores...], ... }
    #   }
    # }
    questions = {}

    pies = ProjectInExam.objects.filter(project__id=project_id)
    all_dates = set()

    # 解析题目
    questions = {}
    question_map = {}

    for pie in pies:
        question_raw = pie.question
        date = pie.exam.date.strftime("%Y-%m-%d")
        all_dates.add(date)
        parse_pie_question_raw(question_raw, date, questions, question_map)

    # 获取考试记录
    exam_records = ExamRecord.objects.filter(project_in_exam__project__id=project_id)
    for record in exam_records:
        date = record.project_in_exam.exam.date.strftime("%Y-%m-%d")
        # 填入相应的数据
        question_answer_record = json.loads(record.question_answer_record) \
            if record.question_answer_record else {}
        for answer_record in question_answer_record:
            record_question = answer_record.get('q')
            record_answer = answer_record.get('a')
            # 通过遍历类型寻找题目
            question_type = question_map.get(record_question, None)
            if question_type is not None \
                and question_type in questions \
                and record_question in questions[question_type] \
                and len(record_answer) != 0 \
                and record_answer[0] in answer_map:
                questions[question_type][record_question][date].append(answer_map[record_answer[0]])

    # 转换为需要的格式
    response = {"category": [], "data": []}
    question_num = len(question_map.keys())

    # 初始化数据结构，并填充数据
    # 初始化index
    question_map_index = {}
    index = 0
    for question_type in questions:
        question_map_index[question_type] = index
        index += 1
    date_map_index = {}
    index = 0
    for date in all_dates:
        date_map_index[date] = index
        index += 1

    response["data"] = [{} for _ in range(len(all_dates))]
    for date in sorted(all_dates, key=lambda x: x):
        response["data"][date_map_index[date]] = {
            "name": date,
            "data": [0 for _ in range(question_num)]
        }
    response["category"] = list(questions.keys())

    # 转换为需要的格式
    for date in sorted(all_dates, key=lambda x: x):
        for question_type in questions:
            sum_score = 0
            sum_count = 0
            for question in questions[question_type]:
                if date in questions[question_type][question] \
                    and len(questions[question_type][question][date]) != 0:
                    sum_score += sum(questions[question_type][question][date])
                    sum_count += len(questions[question_type][question][date])

            average_score = 0 if sum_count == 0 else sum_score / sum_count
            response["data"][date_map_index[date]]["data"][question_map_index[question_type]] = average_score
    return success_api_response(response)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_PASS])
@require_GET
def get_student_score_data(request: HttpRequest, student_id: int):
    """获取特定学生在项目中的历史成绩数
    Returns:
        按题目分组的学生历史成绩数据，包含每次考试的时间戳和分数
        [{
            "name": "题目1",
            "data": [85, 90, 88, 95],
            "timestamps": ["2024-01-01", "2024-01-15", "2024-02-01", "2024-02-15"]
        },...]
    """

    questions = {}
    question_map = {}
    all_dates = set()

    # 获取该学生在这次考试中的答题记录
    exam_records = list(ExamRecord.objects.filter(student__student_id=student_id))

    for record in exam_records:
        pie = record.project_in_exam
        date = pie.exam.date.strftime("%Y-%m-%d") + " " + pie.project.name
        all_dates.add(date)
        parse_pie_question_raw(pie.question, date, questions, question_map)

        question_answer_record = json.loads(record.question_answer_record) \
            if record.question_answer_record else {}
        for answer_record in question_answer_record:
            record_question = answer_record.get('q')
            record_answer = answer_record.get('a')
            # 通过遍历类型寻找题目
            question_type = question_map.get(record_question, None)
            if question_type is not None \
                and question_type in questions \
                and record_question in questions[question_type] \
                and len(record_answer) != 0 \
                and record_answer[0] in answer_map:
                questions[question_type][record_question][date].append(answer_map[record_answer[0]])

    # 转换为需要的格式
    response = {"data": []}
    question_num = len(questions.keys())

    # 初始化数据结构，并填充数据
    # 初始化index
    question_map_index = {}
    index = 0
    for question_type in questions:
        question_map_index[question_type] = index
        index += 1
    date_map_index = {}
    index = 0
    for date in list(all_dates):
        date_map_index[date] = index
        index += 1

    response["data"] = [{} for _ in range(question_num)]
    for question_type in questions:
        response["data"][question_map_index[question_type]] = {
            "name": question_type,
            "data": [0 for _ in range(len(all_dates))],
            "timestamps": list(all_dates)
        }

    # 转换为需要的格式
    for date in sorted(all_dates, key=lambda x: x):
        for question_type in questions:
            sum_score = 0
            sum_count = 0
            for question in questions[question_type]:
                if date in questions[question_type][question] and \
                    len(questions[question_type][question][date]) != 0:
                    sum_score += sum(questions[question_type][question][date])
                    sum_count += len(questions[question_type][question][date])

            question_index = question_map_index[question_type]
            average_score = 0 if sum_count == 0 else sum_score / sum_count
            response["data"][question_index]["data"][date_map_index[date]] = average_score
    return success_api_response(response)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PASS_RANK])
@require_GET
@require_item_exist(model=ProjectInExam, field='id', item='pie_id')
def get_rank(request: HttpRequest, pie_id: int):
    """get passed student rank
    Arguments:
        request {HttpRequest} -- GET
        pie_id {int} -- project in exam id
    """
    pie = ProjectInExam.objects.get(pk=pie_id)

    (_, begin_time, end_time, total_student) = get_delta(pie)

    total_pjr = ProblemJudgeRecord.objects \
        .filter(pk__in=Subquery(
        ProblemJudgeRecord.objects.filter(
            submitted_at__gte=begin_time,
            submitted_at__lte=end_time,
            project_in_exam=pie,
            judge_result=ACCEPTED,
            edx_username__in=total_student)
            .order_by('edx_username', 'problem')
            .distinct('edx_username', 'problem')
            .values('pk'))) \
        .order_by('id')

    student_dict: dict = {}
    student_time: dict = {}
    student_pass: dict = {}
    problems = {p.id: p.name for p in pie.problems.all()}

    for pjr in total_pjr:
        problem_id = pjr.problem_id
        edx_username = pjr.edx_username
        if edx_username not in student_dict:
            student_dict[edx_username] = {}
            student_time[edx_username] = {}
        student_dict[edx_username][problem_id] = True
        student_time[edx_username][problem_id] = pjr.submitted_at.timestamp()

    for edx_username in student_dict:
        if parse_requirement_with_dict_data(pie.pass_requirement, student_dict[edx_username]):
            student_pass[edx_username] = []
            for problem_id in student_time[edx_username]:
                if problem_id not in problems:
                    continue
                element = {
                    'passed_time': (int)(student_time[edx_username][problem_id]),
                    'problem_id': problem_id,
                    'problem_name': problems[problem_id]
                }
                student_pass[edx_username].append(element)

    return success_api_response(student_pass)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_SUBMIT])
@require_GET
@require_item_exist(model=ProjectInExam, field='id', item='pie_id')
def get_submit(request: HttpRequest, pie_id: int):
    """get submit per min
    Arguments:
        request {HttpRequest} -- get
        pie_id {int} -- project in exam id
    """
    pie = ProjectInExam.objects.get(pk=pie_id)

    (delta, begin_time, end_time, total_student) = get_delta(pie)

    total_pjr = ProblemJudgeRecord.objects.filter(
        submitted_at__gte=begin_time,
        submitted_at__lte=end_time,
        project_in_exam=pie,
        edx_username__in=total_student) \
        .order_by('id')

    response: dict = {}
    cnt: dict = {}
    total_cnt: int = 0
    problems = {p.id: p.name for p in pie.problems.all()}

    def get_problem_key(pid):
        return '%s_%s' % (pid, problems[pid])

    for problem in problems:
        key = get_problem_key(problem)
        response[key] = []
        cnt[key] = 0
    response[pie.project.name] = []

    now = begin_time
    for pjr in total_pjr:
        while pjr.submitted_at > now:
            for problem in problems:
                key = get_problem_key(problem)
                response[key].append(cnt[key])
            response[pie.project.name].append(total_cnt)
            now += delta

        if pjr.problem_id in problems:
            cnt[get_problem_key(pjr.problem_id)] += 1
        total_cnt += 1

    while now <= end_time:
        for problem in problems:
            key = get_problem_key(problem)
            response[key].append(cnt[key])
        response[pie.project.name].append(total_cnt)
        now += delta

    response['begin_time'] = begin_time
    response['delta'] = delta.total_seconds()

    return success_api_response(response)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_SUBMIT])
@require_GET
def problem_judge_detail_all(request: HttpRequest, pie_id: int, pid: int):
    problem = Problem.objects.get(pk=pid)
    pie = ProjectInExam.objects.get(pk=pie_id)
    judge_records = ProblemJudgeRecord.objects \
        .filter(project_in_exam=pie, problem_id=problem) \
        .exclude(judge_result=-1) \
        .distinct('edx_username') \
        .order_by('edx_username', '-submitted_at')

    case_records = TestCaseJudgeRecord.objects \
        .filter(problem_judge_record__in=judge_records) \
        .values(edx_username=F("problem_judge_record__edx_username"),
                submitted_at=Func(
                    F("problem_judge_record__submitted_at"),
                    Value('yyyy.MM.dd hh:mm:ss'),
                    function='to_char',
                    output_field=CharField()
                ),
                problem_id=F("problem_judge_record__problem_id"),
                problem_judge_id=F("problem_judge_record_id"),
                case_id=F("test_case_id"),
                case_judge_id=F("id"),
                case_judge_result=F("judge_result"),
                case_comment=F("comment"),
                )

    dat_string = pandas.DataFrame(case_records).to_csv()

    return HttpResponse(dat_string)


# pylint: disable=R0912
@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROJECT_PASS])
@require_GET
@require_item_exist(model=Project, field='id', item='project_id')
def get_project_history_question(request: HttpRequest, project_id: int):
    """get an specified project history question statistics
    [method]: GET
    [route]: /api/judge/chart/project_history_question/<int:project_id>
    """
    exam_records = ExamRecord.objects.filter(project_in_exam__project__id=project_id)
    statistics = defaultdict(lambda: defaultdict(lambda: [0, 0, 0, 0, 0]))
    i = 0
    # pylint: disable=R1702
    for record in exam_records:
        if (record.project_in_exam.question is not None) \
            and isinstance(json.loads(record.project_in_exam.question), dict) \
            and i == 0:
            question_data = json.loads(record.project_in_exam.question) if record.project_in_exam.question else {}
            for question_id, question_info in question_data.items():
                if question_id not in statistics.keys():
                    statistics[question_id] = {}
                for single_question in question_info:
                    if single_question not in statistics[question_id].keys():
                        statistics[question_id][single_question] = [0, 0, 0, 0, 0]
            i = 1
        elif i == 0:
            # 假设 list_data 是从 JSON 中解析得到的嵌套列表
            list_data = json.loads(record.project_in_exam.question)
            result = {}

            for i, sublist in enumerate(list_data, start=1):
                # 使用 "题目类型" 加上当前计数 i 作为字典的键
                key = f"题目类型{i}"
                result[key] = {item: [0, 0, 0, 0, 0] for item in sublist}
            statistics = result
            i = 1

        # pylint: disable=R1702
        question_answer_record = json.loads(record.question_answer_record) if record.question_answer_record else {}
        for single_question in question_answer_record:
            question_content = single_question.get('q')
            question_answer = single_question.get('a')
            for first_key in statistics:
                if question_content in statistics[first_key]:
                    question_answer_temp = str(question_answer)
                    if len(question_answer_temp) != 0:
                        if question_answer_temp[0] >= 'A' and question_answer_temp[0] <= 'F':
                            if question_answer_temp[0] == 'A':
                                statistics[first_key][question_content][0] += 1
                            elif question_answer_temp[0] == 'B':
                                statistics[first_key][question_content][1] += 1
                            elif question_answer_temp[0] == 'C':
                                statistics[first_key][question_content][2] += 1
                            elif question_answer_temp[0] == 'F':
                                statistics[first_key][question_content][3] += 1
                            statistics[first_key][question_content][4] += 1
                    break
    return success_api_response(statistics)


STATISTICS_PROBLEM_API = wrapped_api({
    "get": get_problem_statistics,
})

STATISTICS_EXAM_API = wrapped_api({
    "get": get_exam_statistics,
})

STATISTICS_PROJECT_API = wrapped_api({
    "get": get_project_in_exam_statistics,
})

STATISTICS_PROJECT_CHART_API = wrapped_api({
    "get": passed_num_per_min,
})

STATISTICS_PROBLEM_CHART_API = wrapped_api({
    "get": passed_test_case_per_min,
})

STATISTICS_PROBLEM_DETAIL_API = wrapped_api({
    "get": problem_judge_detail_all,
})

STATISTICS_PROJECT_HISTORY_CHART_API = wrapped_api({
    "get": get_project_history_chart,
})

STATISTICS_PROJECT_HISTORY_ANSWER_CHART_API = wrapped_api({
    "get": get_project_history_answer_chart,
})

STATISTICS_STUDENT_SCORE_API = wrapped_api({
    "get": get_student_score_data,
})

STATISTICS_RANK_API = wrapped_api({
    "get": get_rank,
})

STATISTICS_SUBMIT_API = wrapped_api({
    "get": get_submit,
})
