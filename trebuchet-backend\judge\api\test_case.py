"""
define test case's view-layer functions
"""
from django.core.exceptions import Field<PERSON>rror
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from judge.api.file import generate_oss_token, s3_upload, _validate_upload_file_without_filename
from judge.api.permissions import (JUDGE_CHANGE_TESTCASE,
                                   JUDGE_CREATE_TESTCASE,
                                   JUDGE_DELETE_TESTCASE, JUDGE_VIEW_TESTCASE)
from judge.forms.test_case import Test<PERSON>aseInfo
from judge.models import AdminUploadedFile, TestCase, Problem
from judge.models.user_submitted_file import FileTypes
from trebuchet.settings import GIT_TOKEN


def _validate_create_test_case(request: HttpRequest) -> bool:
    """
    validate create test case
    Args:
        request:

    Returns:

    """
    fields = [field.name for field in TestCase._meta.get_fields()]
    data: dict = parse_data(request)
    if data is None:
        return False
    for key in data.keys():
        if key not in fields or key in ['created_by', 'created_at']:
            return False
    info: TestCaseInfo = TestCaseInfo(data)
    if not info.is_valid():
        return False
    query_id: int = data.get('judge_data', None)
    if query_id is None:
        return True
    model = AdminUploadedFile.objects.filter(id=query_id)
    return model.exists()


def _validate_update_test_case(request: HttpRequest) -> bool:
    """
    validate update test case
    Args:
        request:

    Returns:

    """
    data: dict = parse_data(request)
    query_id = data.get('judge_data', None)
    if query_id is not None:
        if not AdminUploadedFile.objects.filter(id=query_id).exists():
            return False
    if data.get('judge_parameter', None) is not None:
        if data.get('judge_parameter').isspace():
            return False
    fields = [field.name for field in TestCase._meta.get_fields()]
    for key in data.keys():
        if key not in fields or key in ['created_by', 'created_at']:
            return False

    return True


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_TESTCASE])
@require_GET
@require_item_exist(model=TestCase, field='id', item='id')
def get_test_case(request: HttpRequest, query_id: int):
    """
    get test case
    :param request:
    :param query_id:
    :return:
    """
    test_case = TestCase.objects.get(**{'id': query_id})
    data: dict = model_to_dict(test_case)
    data['created_at'] = test_case.created_at
    data['created_by__username'] = None if test_case.created_by is None else test_case.created_by.username
    data['judge_data__filename'] = None if test_case.judge_data is None else test_case.judge_data.filename
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[JUDGE_CREATE_TESTCASE])
@require_POST
@validate_args(func=_validate_create_test_case)
def create_test_case(request: HttpRequest):
    """
    create test case
    :param request:
    :return:
    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # data: dict = parse_data(request)
    # model_id = data.get('judge_data', None)
    # if model_id is not None:
    #     data['judge_data'] = AdminUploadedFile.objects.get(id=model_id)
    # data['created_by'] = request.user
    # TestCase.objects.create(**data)
    # return success_api_response({'success': True})


# pylint: disable=W0613
@response_wrapper
@jwt_auth(perms=[JUDGE_CHANGE_TESTCASE])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_test_case)
@require_item_exist(model=TestCase, field='id', item='id')
def update_test_case(request: HttpRequest, query_id: int):
    """
    update test case
    :param request:
    :param query_id:
    :return:
    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # test_case = TestCase.objects.get(**{'id': query_id})
    # data: dict = parse_data(request)
    #
    # judge_data = data.get('judge_data', None)
    # if judge_data is not None:
    #     judge_data = AdminUploadedFile.objects.get(id=judge_data)
    #     test_case.problem_data = judge_data
    #     del data['judge_data']
    # for key in data.keys():
    #     if data[key] is None:
    #         continue
    #     setattr(test_case, key, data[key])
    # test_case.save()
    # return success_api_response({'success': True})


# pylint: disable=W0613
@response_wrapper
@jwt_auth(perms=[JUDGE_DELETE_TESTCASE])
@require_http_methods(['DELETE'])
@require_item_exist(model=TestCase, field='id', item='id')
def delete_test_case(request: HttpRequest, query_id: int) -> dict:
    """
    delete test case
    :param request:
    :param query_id:
    :return:
    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # model = TestCase.objects.get(**{'id': query_id})
    # info = model_to_dict(model)
    # info['created_at'] = model.created_at
    # model.delete()
    # return success_api_response(info)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_TESTCASE])
@require_GET
@query_filter(fields=[("id", int), ("name", str), ("description", str)])
@query_distinct(fields=["name", 'description'], model=TestCase)
@query_order_by(fields=["name", 'created_at'])
@query_page(default=10)
def list_test_cases(request: HttpRequest, *args, **kwargs):
    """
    list test cases
    :param request:
    :param args:
    :param kwargs:
    :return:
    """
    models_all = TestCase.objects.count()
    models: QuerySet = TestCase.objects.all()
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    # order by
    order_by = kwargs.get('order_by')
    if order_by is not None:
        models = models.order_by(*order_by)
    else:
        models = models.order_by('-id')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'name', 'description', 'created_by', 'created_by__username', 'created_at', 'judge_parameter',
                'judge_data__filename', 'judge_data__id'
            )
        )
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


@response_wrapper
@require_POST
def git_update_testcase(request: HttpRequest):
    """
    [POST] /api/judge/git/testcase/update
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")
    data: dict = parse_data(request)
    if not TestCase.objects.filter(name=data["name"]).exists():
        testcase = TestCase.objects.create(name=data["name"], description=data["description"],
                                           judge_parameter=data["judge_parameter"])
    else:
        TestCase.objects.filter(name=data["name"]).update(description=data["description"],
                                                          judge_parameter=data["judge_parameter"])
        testcase = TestCase.objects.filter(name=data["name"]).first()
    return success_api_response({"id": testcase.id})


@response_wrapper
@require_POST
def git_delete_testcase(request: HttpRequest):
    """
    [POST] /api/judge/git/testcase/delete
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")
    data: dict = parse_data(request)
    if TestCase.objects.filter(name=data["name"]).exists():
        TestCase.objects.filter(name=data["name"]).delete()
        return success_api_response({"delete": True})
    return success_api_response({"delete": False})


def replace_name(name: str) -> str:
    ret = ""
    i = 0
    while i < len(name):
        if name[i] == "*":
            if i + 1 < len(name) and name[i + 1] == "*":
                ret += "*"
                i += 2
            else:
                ret += "/"
                i += 1
        else:
            ret += name[i]
            i += 1
    return ret


@response_wrapper
@require_POST
@validate_args(func=_validate_upload_file_without_filename)
def git_update_testcase_file(request: HttpRequest, testcase_name):
    """
    [POST] /api/judge/git/testcase/file/<str:testcase_name>
    因为 url 里 / 有特殊含义，这里传过来的 testcase_name 将 / 替换为了 *（* 替换为 **），需要先替换得到原始字符串
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")

    testcase_name = replace_name(testcase_name)
    if not TestCase.objects.filter(name=testcase_name).exists():
        testcase = TestCase.objects.create(name=testcase_name, judge_parameter="{}")
    else:
        testcase = TestCase.objects.filter(name=testcase_name).first()

    filename = request.FILES.get("file").name
    data = {
        "filename": filename,
        "oss_token": generate_oss_token(testcase.id, filename),
        "type": FileTypes.JUDGE_DATA
    }
    if testcase.judge_data is None:
        new_file = AdminUploadedFile.objects.create(**data)
        testcase.judge_data = new_file
        testcase.save()
    else:
        file_id = testcase.judge_data.id
        AdminUploadedFile.objects.filter(pk=file_id).update(**data)
    try:
        # testcase.judge_data 在内存中没有改变，只在数据库里改变了
        # 因此这里的 oss_token 不能传 testcase.judge_data.oss_token
        s3_upload(data["oss_token"], request, FileTypes.JUDGE_DATA)
    except Exception as exception:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, str(exception))
    return success_api_response({"id": testcase.judge_data_id})


@response_wrapper
@require_POST
def git_update_problem_testcases(request: HttpRequest):
    """
    [POST] /api/judge/git/problem/testcases
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")
    data = parse_data(request)
    if not Problem.objects.filter(name=data["problem_name"]).exists():
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "can't find problem")
    for testcase_name in data["testcases"]:
        if not TestCase.objects.filter(name=testcase_name).exists():
            return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "can't find testcase")
    problem = Problem.objects.filter(name=data["problem_name"]).first()
    new_testcase_set = set(data["testcases"])
    for testcase in problem.test_cases.all():
        if testcase.name not in new_testcase_set:
            problem.test_cases.remove(testcase)
    for testcase_name in data["testcases"]:
        if not problem.test_cases.filter(name=testcase_name).exists():
            problem.test_cases.add(TestCase.objects.filter(name=testcase_name).first())
    return success_api_response({"id": problem.id})


TEST_CASE_SET_API = wrapped_api({
    "post": create_test_case,
    "get": list_test_cases,
})

TEST_CASE_DETAIL_API = wrapped_api({
    "put": update_test_case,
    "get": get_test_case,
    "delete": delete_test_case,
})
