"""
define the url routes of judge api
"""
from django.urls import path

from judge.api.file import PROBLEM_FILE_API, TEST_CASE_FILE_API, PROBLEM_JUDGE_RECORD_FILE_API
from judge.api.judge_statistics import STATISTICS_PROBLEM_API, STATISTICS_PROJECT_API, \
    STATISTICS_EXAM_API, STATISTICS_PROJECT_CHART_API, STATISTICS_PROBLEM_CHART_API, \
    STATISTICS_PROJECT_HISTORY_CHART_API, STATISTICS_RANK_API, STATISTICS_SUBMIT_API, \
    STATISTICS_PROBLEM_DETAIL_API, get_project_history_question, STATISTICS_PROJECT_HISTORY_ANSWER_CHART_API, \
    STATISTICS_STUDENT_SCORE_API
from judge.api.problem import PROBLEM_SET_API, PROBLEM_DETAIL_API, CHOICE_ADD_API, git_update_problem, \
    git_update_problem_file, GIT_HASH_API, git_delete_problem
from judge.api.problem_judge_record import PROBLEM_JUDGE_RECORD_SET_API, PROBLEM_JUDGE_RECORD_DETAIL_API
from judge.api.test_case import TEST_CASE_SET_API, TEST_CASE_DETAIL_API, git_update_testcase, \
    git_update_testcase_file, git_update_problem_testcases, git_delete_testcase
from judge.api.test_case_judge_record import TEST_CASE_JUDGE_RECORD_SET_API, TEST_CASE_JUDGE_RECORD_DETAIL_API
from judge.api.rejudge import REJUDGE_RECORD_API, BATCH_REJUDGE_RECORD_API, list_batch_id
from judge.api.exam_event_record import EXAM_EVENT_RECORD_SET_API, EXAM_EVENT_RECORD_DETAIL_API

urlpatterns = [
    path('problem', PROBLEM_SET_API),
    path('problem/<int:id>', PROBLEM_DETAIL_API),
    path('test_case', TEST_CASE_SET_API),
    path('test_case/<int:id>', TEST_CASE_DETAIL_API),
    path('problem_judge_record', PROBLEM_JUDGE_RECORD_SET_API),
    path('problem_judge_record/<int:id>', PROBLEM_JUDGE_RECORD_DETAIL_API),
    path('test_case_judge_record', TEST_CASE_JUDGE_RECORD_SET_API),
    path('test_case_judge_record/<int:id>', TEST_CASE_JUDGE_RECORD_DETAIL_API),
    path('file/problem/<int:query_id>', PROBLEM_FILE_API),
    path('file/test_case/<int:query_id>', TEST_CASE_FILE_API),
    path('file/problem_judge_record/<int:query_id>', PROBLEM_JUDGE_RECORD_FILE_API),
    path('statistics_problem/<int:query_id>', STATISTICS_PROBLEM_API),
    path('statistics_project/<int:query_id>', STATISTICS_PROJECT_API),
    path('statistics_exam/<int:query_id>', STATISTICS_EXAM_API),
    path('chart/project/<int:pie_id>', STATISTICS_PROJECT_CHART_API),
    path('chart/project_history/<int:project_id>', STATISTICS_PROJECT_HISTORY_CHART_API),
    path('chart/project_history_answer/<int:project_id>', STATISTICS_PROJECT_HISTORY_ANSWER_CHART_API),
    path('chart/student-score/<int:student_id>', STATISTICS_STUDENT_SCORE_API),
    path('chart/project_history_question/<int:project_id>', get_project_history_question),
    path('chart/problem/<int:pie_id>/<int:pid>', STATISTICS_PROBLEM_CHART_API),
    path('details/problem/<int:pie_id>/<int:pid>', STATISTICS_PROBLEM_DETAIL_API),
    path('rejudge', REJUDGE_RECORD_API),
    path('rejudge/batch-rejudge', BATCH_REJUDGE_RECORD_API),
    path('rejudge/batch-rejudge/batch_id', list_batch_id),
    path('rank/<int:pie_id>', STATISTICS_RANK_API),
    path('chart/project_submit/<int:pie_id>', STATISTICS_SUBMIT_API),
    path('exam_event_record', EXAM_EVENT_RECORD_SET_API),
    path('exam_event_record/<int:id>', EXAM_EVENT_RECORD_DETAIL_API),
    path('choice', CHOICE_ADD_API),

    # git化
    path("git/problem/update", git_update_problem),
    path("git/problem/delete", git_delete_problem),
    path("git/problem/file/<str:problem_name>", git_update_problem_file),
    path("git/hash/<str:branch>", GIT_HASH_API),
    path("git/testcase/update", git_update_testcase),
    path("git/testcase/delete", git_delete_testcase),
    path("git/testcase/file/<str:testcase_name>", git_update_testcase_file),
    path("git/problem/testcases", git_update_problem_testcases),
]
