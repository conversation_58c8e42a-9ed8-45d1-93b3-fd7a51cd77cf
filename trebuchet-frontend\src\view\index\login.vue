<template>
  <div class="login">
    <div class="login-con">
      <Card class="card" :bordered="false" icon="log-in" :title="title">
        <div class="form-con">
          <login-form @on-success-valid="handleSubmit" />
        </div>
      </Card>
    </div>
  </div>
</template>

<script>
import './login.less'
import { mapActions } from 'vuex'
import LoginForm from './login-form'
import { title } from '@/router/routers'
import { getErrModalOptions } from '@/libs/util'

export default {
  components: {
    LoginForm
  },
  data() {
    return {
      title: '欢迎登录 ' + title
    }
  },
  methods: {
    ...mapActions({
      handleLogin: 'user/handleLogin'
    }),
    handleSubmit({ userName, password }) {
      this.handleLogin({ userName, password })
        .then(() => {
          this.$router.push(this.$route.query.redirect ? this.$route.query.redirect : '/')
        })
        .catch((err) => {
          let error_msg = String(err)
          if (err.response && err.response.data) {
            error_msg = err.response.data['error_msg']
          }
          this.$Modal.error(getErrModalOptions(error_msg))
        })
    }
  }
}
</script>
