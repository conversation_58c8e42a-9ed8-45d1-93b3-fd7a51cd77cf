"""
define the api to student checkout exam.
"""
from django.db import transaction
from django.db.models import QuerySet
from django.http import HttpRequest
from django.utils import timezone
from django.views.decorators.http import require_http_methods

from core.api.auth import jwt_auth
from core.api.permissions import CORE_EXAM_RECORD_CHANGE
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, wrapped_api)
from core.interface.student_progress import query_student_passed
from core.models.exam_queue import ExamQueue
from core.models.exam_record import (STATUS_CHECKED_OUT, STATUS_IN_PROGRESS,
                                     STATUS_NOT_CHECKED_IN, ExamRecord, STATUS_WAITING_IN_QUEUE)
from core.models.room import Room
from core.models.exam import Exam
from core.models.student import Student
from core.models.student_seat_record import StudentSeatRecord

UNDER_CLASS_COHORT_NAME = "UnderClass"


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CHANGE])
@require_http_methods(['PUT'])
@require_item_exist(model=Student, field='student_id', item='student_id')
def student_check_in_exam(request: HttpRequest, student_id: str):
    """student check in the exam

    [method]: PUT

    [route]: /api/exam-check-in/<str:student_id>
    """
    student: Student = Student.objects.get(student_id=student_id)
    exam_record: ExamRecord = student.examrecord_set.all().order_by('-id').first()
    if exam_record is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'Student does not have this exam record')
    if exam_record.status is not STATUS_NOT_CHECKED_IN:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Already Check In")

    # 2019/10/11 02:54 updated by Dailan He
    # need update checked_in_at
    exam_record.status = STATUS_IN_PROGRESS
    exam_record.checked_in_at = timezone.now()
    exam_record.save()

    return success_api_response({'success': True})


def check_out_exam_record(exam_record: ExamRecord):
    """check out 时修改 ExamRecord 的 status 与移动 edx 用户组
    """
    if exam_record.status == STATUS_CHECKED_OUT:
        return True

    # 2019/10/11 03:02 updated by Dailan He
    # need update checked_out_at
    exam_record.status = STATUS_CHECKED_OUT
    exam_record.checked_out_at = timezone.now()
    exam_record.save()
    return True


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CHANGE])
@require_http_methods(["PUT"])
@require_item_exist(model=Student, field="student_id", item="student_id")
def student_check_out_exam_without_quiz(request: HttpRequest, student_id: str):
    """check out a student without quiz

    [method]: PUT

    [route]: /api/exam-check-out/<str:student_id>
    """
    data: dict = parse_data(request)
    student: Student = Student.objects.get(student_id=student_id)
    exam_record: ExamRecord = student.examrecord_set.all().filter(
        status=STATUS_IN_PROGRESS).order_by("-id").first()
    if exam_record is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "Student not in progress.")
    if exam_record.status is STATUS_CHECKED_OUT:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Already checked out.")
    if query_student_passed(student_id, exam_record.project_in_exam.id):
        if data is None or not data.get("force"):
            return failed_api_response(ErrorCode.CONFIRM_CHECK_OUT, "The student has passed the project.")
    exam_record.check_result = -1
    exam_record.check_comment = "统一签退"
    exam_record.examinant = request.user
    if not check_out_exam_record(exam_record):
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Check out failed")
    return success_api_response({"result": "Ok, student {} has been checked out.".format(student_id)})


def _check_exam_over(exam_record: ExamRecord):
    now_second = int(timezone.now().timestamp()) % 86400
    begin_time = exam_record.project_in_exam.begin_time
    begin_second = begin_time.hour * 3600 + begin_time.minute * 60 + begin_time.second - 28800
    duration_second = exam_record.extend_time * 60 + exam_record.project_in_exam.duration * 60
    if now_second - begin_second <= duration_second:
        return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CHANGE])
@require_http_methods(["PUT"])
def clear_room(request: HttpRequest):
    """clear room:
    Students who passed this exam will automatically raise their hands
    Students who failed to pass will be automatically checked out

    [method]: PUT

    [route]: /api/exam-clear-room
    """
    data: dict = parse_data(request)
    res = {"exam_not_over": [], "check_out_failed": [], "raise_hand_failed": [],
           "success_check_out": [], "success_raise_hand": []}
    room_id = data["room_id"]
    if not Room.objects.filter(pk=room_id).exists():
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "没有考场信息")
    room = Room.objects.get(pk=room_id)
    exam_id = data["exam_id"]
    if not Exam.objects.filter(pk=exam_id).exists():
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "没有考试信息")
    exam = Exam.objects.get(pk=exam_id)
    if not exam.active:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "考试未激活")
    student_pks = StudentSeatRecord.objects \
        .filter(exam=exam, seat__room=room) \
        .values_list("student__id", flat=True)
    # 使用悲观锁解决并发竞争问题
    exam_records = ExamRecord.objects.select_for_update().filter(student__id__in=student_pks,
                                                                 project_in_exam__exam=exam,
                                                                 status=STATUS_IN_PROGRESS)
    with transaction.atomic():
        for exam_record in exam_records:
            if not _check_exam_over(exam_record):
                res["exam_not_over"].append(exam_record.student.student_id)
                continue
            if query_student_passed(exam_record.student.student_id, exam_record.project_in_exam.id):
                exam_queue, _ = ExamQueue.objects.get_or_create(
                    student=exam_record.student,
                    room=room,
                    exam=exam_record.project_in_exam.exam,
                    project_in_exam=exam_record.project_in_exam
                )
                if not exam_queue.valid:
                    res["raise_hand_failed"].append(exam_record.student.student_id)
                    continue
                exam_record.status = STATUS_WAITING_IN_QUEUE
                exam_record.save()
                res["success_raise_hand"].append(exam_record.student.student_id)
            else:
                exam_record.check_result = -1
                exam_record.check_comment = "统一签退"
                exam_record.examinant = request.user
                if not check_out_exam_record(exam_record):
                    res["check_out_failed"].append(exam_record.student.student_id)
                    continue
                exam_record.save()
                res["success_check_out"].append(exam_record.student.student_id)
    return success_api_response(res)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CHANGE])
@require_http_methods(['PUT'])
@require_item_exist(model=Student, field='student_id', item='student_id')
def roll_back_student_check_in_exam(request: HttpRequest, student_id: str):
    """student check out the exam

    [method]: PUT

    [route]: /api/roll-back-check-in/<str:student_id>
    """
    student: Student = Student.objects.get(student_id=student_id)
    exam_record: ExamRecord = student.examrecord_set.all().order_by('-id').first()
    if exam_record is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'Student does not have this exam record')
    if exam_record.status is STATUS_NOT_CHECKED_IN:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Did not Check In")
    if exam_record.status is STATUS_CHECKED_OUT:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "has checked out")
    exam_record.status = STATUS_NOT_CHECKED_IN
    exam_record.checked_in_at = None
    exam_record.save()

    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CHANGE])
@require_http_methods(['PUT'])
@require_item_exist(model=Student, field='student_id', item='student_id')
def roll_back_student_check_out_exam(request: HttpRequest, student_id: str):
    """student check out the exam

    [method]: PUT

    [route]: /api/roll-back-check-out/<str:student_id>
    """
    student: Student = Student.objects.get(student_id=student_id)
    exam_record: ExamRecord = student.examrecord_set.all().order_by('-id').first()
    if exam_record is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'Student does not have this exam record')
    if exam_record.status is STATUS_NOT_CHECKED_IN:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Did not Check In")
    if exam_record.status is STATUS_IN_PROGRESS:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Did not checked out")
    query_set: QuerySet = ExamQueue.objects.filter(student=student, project_in_exam=exam_record.project_in_exam)
    if query_set.exists():
        exam_queue: ExamQueue = query_set.first()
        # 放回到未取出队列
        exam_queue.valid = True
        exam_queue.save()
        # 设置状态为等待中
        exam_record.status = STATUS_WAITING_IN_QUEUE
    else:
        exam_record.status = STATUS_IN_PROGRESS
    exam_record.checked_out_at = None
    exam_record.save()

    return success_api_response({'success': True})


EXAM_CHECK_IN_API = wrapped_api({
    "put": student_check_in_exam,
})

EXAM_CHECK_OUT_API = wrapped_api({
    "put": student_check_out_exam_without_quiz
})

EXAM_CLEAR_ROOM_API = wrapped_api({
    "put": clear_room
})

EXAM_ROLL_BACK_CHECK_IN_API = wrapped_api({
    'put': roll_back_student_check_in_exam
})

EXAM_ROLL_BACK_CHECK_OUT_API = wrapped_api({
    'put': roll_back_student_check_out_exam
})
