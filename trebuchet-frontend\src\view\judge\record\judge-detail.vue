<template>
  <Row>
    <Col offset="1" span="8">
      <Card>
        <p slot="title" style="font-size: 20px">Judge Detail</p>
        <a slot="extra" @click="handleDownload">下载附件</a>
        <Row v-for="(value, key) in judgeDetail" :key="key">
          <Col span="5" style="font-size: 16px">
            <strong>{{ key }}</strong>
          </Col>
          <Col offset="12">
            {{ value }}
          </Col>
        </Row>
        <Row>
          <Col style="float: right">
            <Button type="primary" @click="showConfirm = true">重测</Button>
          </Col>
        </Row>
      </Card>
    </Col>
    <Col span="12" offset="1">
      <Table :columns="testCaseColumns" :data="testCase" />
    </Col>
    <Col>
      <modal v-model="showConfirm" title="Rejudge Confirm" @on-ok="rejudge" @on-cancel="showConfirm = false">
        <p>是否确定进行重测</p>
      </modal>
    </Col>
  </Row>
</template>

<script>
import _ from 'lodash'
import { Tag, Link, ActionButton, Spacer } from '@/libs/render-item'
import { judgeIdReq, judgeRecordFileReq, rejudgeReq } from '@/api/judge'
import { getErrModalOptions, getLocalTime, processRemoteDownload } from '@/libs/util'

const resultMap = {
  '-1': 'NONE',
  0: 'ACCEPTED',
  1: 'WRONG_ANSWER',
  2: 'RUNTIME_ERROR',
  3: 'MEMORY_LIMIT_EXCEEDED',
  4: 'TIME_LIMIT_EXCEEDED',
  5: 'FUNCTION_LIMIT_VIOLATION',
  6: 'COMPILATION_ERROR',
  7: 'PRESENTATION_ERROR',
  8: 'UNKNOWN_ERROR'
}

export default {
  name: 'JudgeDetail',
  data() {
    return {
      testCase: [],
      judgeDetail: {},
      testCaseColumns: [
        {
          title: 'Test Case ID',
          key: 'test_case_id',
          render: (h, params) => Link(h, params.row['test_case_id'], 'testcase_detail')
        },
        {
          title: 'Result',
          key: 'judge_result',
          render: (h, params) => Tag(h, params.row.judge_result ? 'red' : 'green', resultMap[params.row.judge_result])
        },
        {
          title: '查看',
          render: (h, params) =>
            h('div', [
              ActionButton(h, () => this.onShow('评测反馈', 30, params.row.comment), '评测反馈', false),
              Spacer(h),
              ActionButton(
                h,
                () => this.onShow('原始输出', 80, this.EscapeInvisibleChar(params.row['raw_output'])),
                '原始输出',
                false
              )
            ])
        }
      ],
      showConfirm: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      judgeIdReq('get', this.$route.params.id, {})
        .then((res) => {
          this.testCase = res.data['test_case_judge_record']
          this.judgeDetail = _.omit(res.data, 'test_case_judge_record')
          this.judgeDetail.submitted_at = getLocalTime(this.judgeDetail.submitted_at)
          this.judgeDetail.started_at = getLocalTime(this.judgeDetail.started_at)
          this.judgeDetail.finished_at = getLocalTime(this.judgeDetail.finished_at)
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onShow(title, width, text) {
      this.$Modal.info({
        closable: true,
        width: width + '%',
        title: title,
        content: text.replace(/\n/gm, '<br/>')
      })
    },
    async handleDownload() {
      try {
        const res = await judgeRecordFileReq('get', this.$route.params.id)
        processRemoteDownload(res)
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    rejudge() {
      rejudgeReq(this.$route.params.id)
        .then(() => {
          this.$Notice.success({ title: '重测成功' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    EscapeInvisibleChar(str) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
      }
      return str.replace(/[&<>"']/g, function (m) {
        return map[m]
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-collapse-header {
  font-size: 16px;
}

.ivu-modal-confirm-body {
  word-wrap: break-word;
}
</style>
