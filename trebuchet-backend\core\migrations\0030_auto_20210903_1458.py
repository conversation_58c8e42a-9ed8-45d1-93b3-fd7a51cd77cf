# Generated by Django 3.1.7 on 2021-09-03 06:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0029_student_gender'),
    ]

    operations = [
        migrations.CreateModel(
            name='PushMessage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.IntegerField(choices=[(1, '文本通知')], default=1)),
                ('title', models.CharField(default='计算机组成原理课程通知', max_length=50)),
                ('content', models.TextField()),
                ('comment', models.CharField(blank=True, max_length=150, null=True)),
                ('with_email', models.BooleanField(default=False)),
                ('status', models.IntegerField(choices=[(0, '等待推送'), (1, '推送成功'), (2, '(部分)推送出错')], default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pushed_at', models.DateTimeField(null=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.course')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                 to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'permissions': [('查看推送消息', '查看推送消息'), ('编辑推送消息', '编辑推送消息'), ('正式发布推送消息', '正式发布推送消息')],
                'default_permissions': '查看推送消息',
            },
        ),
        migrations.CreateModel(
            name='PushMessageRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_status',
                 models.IntegerField(choices=[(0, '等待推送'), (1, '收到确认'), (2, '推送成功'), (3, '推送出错')], default=0)),
                ('notification_confirmed_at', models.DateTimeField(null=True)),
                ('email_status',
                 models.IntegerField(choices=[(0, '等待推送'), (1, '收到确认'), (2, '推送成功'), (3, '推送出错')], default=0)),
                ('email_confirmed_at', models.DateTimeField(null=True)),
                ('push_message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.pushmessage')),
                ('related_notification', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                           to='core.notificationinbox')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.student')),
            ],
        ),
        migrations.AddField(
            model_name='pushmessage',
            name='recipients',
            field=models.ManyToManyField(through='core.PushMessageRecord', to='core.Student'),
        ),
    ]
