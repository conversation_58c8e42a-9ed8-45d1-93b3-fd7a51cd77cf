{"ast": null, "code": "import { slowDetectionReq, repeatedFailuresReq, qualificationFailuresReq } from '@/api/progress';\nimport { courseReq } from '@/api/course';\nimport { getErrModalOptions } from '@/libs/util';\nimport { WhitePre, Tag } from '@/libs/render-item';\nexport default {\n  name: 'ProgressDetection',\n  data() {\n    return {\n      activeTab: 'slow-detection',\n      selectedCourse: null,\n      courseList: [],\n      // 进度慢检测\n      slowDetectionParams: {\n        submission_threshold: 5,\n        days_range: 7\n      },\n      slowDetectionLoading: false,\n      slowStudents: [],\n      slowStudentsColumns: [{\n        title: '学号',\n        key: 'student_id',\n        render: (h, params) => WhitePre(h, params.row.student_id)\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        render: (h, params) => WhitePre(h, params.row.student_name)\n      }, {\n        title: '班级',\n        key: 'class_name'\n      }, {\n        title: '提交次数',\n        key: 'submission_count',\n        render: (h, params) => Tag(h, 'red', params.row.submission_count)\n      }, {\n        title: '最后提交时间',\n        key: 'last_submission_time'\n      }],\n      // 多次挂在同一个P检测\n      repeatedFailuresParams: {\n        failure_threshold: 3\n      },\n      repeatedFailuresLoading: false,\n      repeatedFailureStudents: [],\n      repeatedFailureStudentsColumns: [{\n        title: '学号',\n        key: 'student_id',\n        render: (h, params) => WhitePre(h, params.row.student_id)\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        render: (h, params) => WhitePre(h, params.row.student_name)\n      }, {\n        title: '当前P',\n        key: 'current_project',\n        render: (h, params) => Tag(h, 'blue', params.row.current_project)\n      }, {\n        title: '失败次数',\n        key: 'failure_count',\n        render: (h, params) => Tag(h, 'red', params.row.failure_count)\n      }, {\n        title: '最后失败时间',\n        key: 'last_failure_time'\n      }],\n      // 多次没有课上资格检测\n      qualificationFailuresParams: {\n        failure_threshold: 2\n      },\n      qualificationFailuresLoading: false,\n      qualificationFailureStudents: [],\n      qualificationFailureStudentsColumns: [{\n        title: '学号',\n        key: 'student_id',\n        render: (h, params) => WhitePre(h, params.row.student_id)\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        render: (h, params) => WhitePre(h, params.row.student_name)\n      }, {\n        title: '当前P',\n        key: 'current_project',\n        render: (h, params) => Tag(h, 'blue', params.row.current_project)\n      }, {\n        title: '缺席次数',\n        key: 'absence_count',\n        render: (h, params) => Tag(h, 'orange', params.row.absence_count)\n      }, {\n        title: '最近缺席考试',\n        key: 'last_absence_exam'\n      }]\n    };\n  },\n  mounted() {\n    this.loadCourses();\n  },\n  methods: {\n    async loadCourses() {\n      try {\n        const res = await courseReq('get');\n        this.courseList = res.data.courses || [];\n        if (this.courseList.length > 0) {\n          this.selectedCourse = this.courseList[0].id;\n        }\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n      }\n    },\n    onCourseChange() {\n      // 清空之前的检测结果\n      this.slowStudents = [];\n      this.repeatedFailureStudents = [];\n      this.qualificationFailureStudents = [];\n    },\n    onTabChange(name) {\n      this.activeTab = name;\n    },\n    async detectSlowProgress() {\n      if (!this.selectedCourse) {\n        this.$Message.warning('请先选择课程');\n        return;\n      }\n      this.slowDetectionLoading = true;\n      try {\n        // 模拟API调用，返回示例数据\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        this.slowStudents = [{\n          student_id: '2021001',\n          student_name: '张三',\n          class_name: '计科21-1',\n          submission_count: 2,\n          last_submission_time: '2024-01-15 14:30:00'\n        }, {\n          student_id: '2021002',\n          student_name: '李四',\n          class_name: '计科21-2',\n          submission_count: 1,\n          last_submission_time: '2024-01-12 09:15:00'\n        }];\n        this.$Message.success('检测完成');\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n      } finally {\n        this.slowDetectionLoading = false;\n      }\n    },\n    exportSlowStudents() {\n      // 模拟导出功能\n      const csvContent = this.generateCSV(this.slowStudents, this.slowStudentsColumns);\n      this.downloadCSV(csvContent, '进度慢学生名单.csv');\n      this.$Message.success('导出成功');\n    },\n    async detectRepeatedFailures() {\n      if (!this.selectedCourse) {\n        this.$Message.warning('请先选择课程');\n        return;\n      }\n      this.repeatedFailuresLoading = true;\n      try {\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        this.repeatedFailureStudents = [{\n          student_id: '2021003',\n          student_name: '王五',\n          current_project: 'P3-数据结构',\n          failure_count: 4,\n          last_failure_time: '2024-01-16 16:45:00'\n        }];\n        this.$Message.success('检测完成');\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n      } finally {\n        this.repeatedFailuresLoading = false;\n      }\n    },\n    async detectQualificationFailures() {\n      if (!this.selectedCourse) {\n        this.$Message.warning('请先选择课程');\n        return;\n      }\n      this.qualificationFailuresLoading = true;\n      try {\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        this.qualificationFailureStudents = [{\n          student_id: '2021004',\n          student_name: '赵六',\n          current_project: 'P2-算法基础',\n          absence_count: 3,\n          last_absence_exam: '2024-01-14 考试'\n        }];\n        this.$Message.success('检测完成');\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n      } finally {\n        this.qualificationFailuresLoading = false;\n      }\n    },\n    generateCSV(data, columns) {\n      const headers = columns.map(col => col.title).join(',');\n      const rows = data.map(row => columns.map(col => row[col.key] || '').join(',')).join('\\n');\n      return headers + '\\n' + rows;\n    },\n    downloadCSV(content, filename) {\n      const blob = new Blob([content], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  }\n};", "map": {"version": 3, "mappings": "AA8GA;AACA;AACA;AACA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC,sBACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;MACA,GACA;QACAD;QACAC;QACAC;MACA,GACA;QACAF;QACAC;MACA,EACA;MAEA;MACAE;QACAC;MACA;MACAC;MACAC;MACAC,iCACA;QACAP;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;MACA,EACA;MAEA;MACAO;QACAJ;MACA;MACAK;MACAC;MACAC,sCACA;QACAX;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;MACA;IAEA;EACA;EACAW;IACA;EACA;EACAC;IACA;MACA;QACA;QACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;MACA;QACA;QACA;MACA;MAEA;MACA;QACA;QACA;QACA,qBACA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA,GACA;UACAJ;UACAC;UACAC;UACAC;UACAC;QACA,EACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEA;MACA;QACA;QACA;MACA;MAEA;MACA;QACA;QACA,gCACA;UACAL;UACAC;UACAK;UACAC;UACAC;QACA,EACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;MACA;QACA;QACA;MACA;MAEA;MACA;QACA;QACA,qCACA;UACAR;UACAC;UACAK;UACAG;UACAC;QACA,EACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA,6BACAC,iDACA;MACA;IACA;IAEAC;MACA;QAAAC;MAAA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;MACAD;MACAC;IACA;EACA;AACA", "names": ["name", "data", "activeTab", "selectedCourse", "courseList", "slowDetectionParams", "submission_threshold", "days_range", "slowDetectionLoading", "slowStudents", "slowStudentsColumns", "title", "key", "render", "repeatedFailuresParams", "failure_threshold", "repeatedFailuresLoading", "repeatedFailureStudents", "repeatedFailureStudentsColumns", "qualificationFailuresParams", "qualificationFailuresLoading", "qualificationFailureStudents", "qualificationFailureStudentsColumns", "mounted", "methods", "onCourseChange", "onTabChange", "student_id", "student_name", "class_name", "submission_count", "last_submission_time", "exportSlowStudents", "current_project", "failure_count", "last_failure_time", "absence_count", "last_absence_exam", "generateCSV", "columns", "downloadCSV", "type", "link", "document"], "sourceRoot": "src/view/exam/progress", "sources": ["progress-detection.vue"], "sourcesContent": ["<template>\n  <div>\n    <Card>\n      <h2 slot=\"title\">课程进度检测</h2>\n      <div slot=\"extra\">\n        <Select v-model=\"selectedCourse\" style=\"width: 200px\" @on-change=\"onCourseChange\">\n          <Option v-for=\"course in courseList\" :key=\"course.id\" :value=\"course.id\">\n            {{ course.name }}\n          </Option>\n        </Select>\n      </div>\n\n      <Tabs :value=\"activeTab\" @on-click=\"onTabChange\">\n        <!-- 功能1：进度慢检测 -->\n        <TabPane label=\"进度慢检测\" name=\"slow-detection\">\n          <Card>\n            <Form ref=\"slowDetectionForm\" :model=\"slowDetectionParams\" :label-width=\"120\" inline>\n              <FormItem label=\"提交次数阈值\">\n                <InputNumber v-model=\"slowDetectionParams.submission_threshold\" :min=\"1\" :max=\"100\" />\n              </FormItem>\n              <FormItem label=\"统计天数范围\">\n                <InputNumber v-model=\"slowDetectionParams.days_range\" :min=\"1\" :max=\"30\" />\n              </FormItem>\n              <FormItem>\n                <Button type=\"primary\" @click=\"detectSlowProgress\" :loading=\"slowDetectionLoading\">\n                  开始检测\n                </Button>\n                <Button \n                  type=\"success\" \n                  @click=\"exportSlowStudents\" \n                  :disabled=\"slowStudents.length === 0\"\n                  style=\"margin-left: 8px\"\n                >\n                  一键导出\n                </Button>\n              </FormItem>\n            </Form>\n            \n            <Alert v-if=\"slowStudents.length > 0\" type=\"warning\" style=\"margin: 16px 0\">\n              检测到 {{ slowStudents.length }} 名学生进度较慢\n            </Alert>\n            \n            <Table \n              :data=\"slowStudents\" \n              :columns=\"slowStudentsColumns\" \n              :loading=\"slowDetectionLoading\"\n              style=\"margin-top: 16px\"\n            />\n          </Card>\n        </TabPane>\n\n        <!-- 功能3：多次挂在同一个P检测 -->\n        <TabPane label=\"同一P多次失败检测\" name=\"repeated-failures\">\n          <Card>\n            <Form ref=\"repeatedFailuresForm\" :model=\"repeatedFailuresParams\" :label-width=\"120\" inline>\n              <FormItem label=\"失败次数阈值\">\n                <InputNumber v-model=\"repeatedFailuresParams.failure_threshold\" :min=\"1\" :max=\"20\" />\n              </FormItem>\n              <FormItem>\n                <Button type=\"primary\" @click=\"detectRepeatedFailures\" :loading=\"repeatedFailuresLoading\">\n                  开始检测\n                </Button>\n              </FormItem>\n            </Form>\n            \n            <Alert v-if=\"repeatedFailureStudents.length > 0\" type=\"error\" style=\"margin: 16px 0\">\n              检测到 {{ repeatedFailureStudents.length }} 名学生在同一P上多次失败\n            </Alert>\n            \n            <Table \n              :data=\"repeatedFailureStudents\" \n              :columns=\"repeatedFailureStudentsColumns\" \n              :loading=\"repeatedFailuresLoading\"\n              style=\"margin-top: 16px\"\n            />\n          </Card>\n        </TabPane>\n\n        <!-- 功能4：多次没有课上资格检测 -->\n        <TabPane label=\"课上资格失败检测\" name=\"qualification-failures\">\n          <Card>\n            <Form ref=\"qualificationFailuresForm\" :model=\"qualificationFailuresParams\" :label-width=\"120\" inline>\n              <FormItem label=\"失败次数阈值\">\n                <InputNumber v-model=\"qualificationFailuresParams.failure_threshold\" :min=\"1\" :max=\"10\" />\n              </FormItem>\n              <FormItem>\n                <Button type=\"primary\" @click=\"detectQualificationFailures\" :loading=\"qualificationFailuresLoading\">\n                  开始检测\n                </Button>\n              </FormItem>\n            </Form>\n            \n            <Alert v-if=\"qualificationFailureStudents.length > 0\" type=\"warning\" style=\"margin: 16px 0\">\n              检测到 {{ qualificationFailureStudents.length }} 名学生多次失去课上资格\n            </Alert>\n            \n            <Table \n              :data=\"qualificationFailureStudents\" \n              :columns=\"qualificationFailureStudentsColumns\" \n              :loading=\"qualificationFailuresLoading\"\n              style=\"margin-top: 16px\"\n            />\n          </Card>\n        </TabPane>\n      </Tabs>\n    </Card>\n  </div>\n</template>\n\n<script>\nimport { slowDetectionReq, repeatedFailuresReq, qualificationFailuresReq } from '@/api/progress'\nimport { courseReq } from '@/api/course'\nimport { getErrModalOptions } from '@/libs/util'\nimport { WhitePre, Tag } from '@/libs/render-item'\n\nexport default {\n  name: 'ProgressDetection',\n  data() {\n    return {\n      activeTab: 'slow-detection',\n      selectedCourse: null,\n      courseList: [],\n      \n      // 进度慢检测\n      slowDetectionParams: {\n        submission_threshold: 5,\n        days_range: 7\n      },\n      slowDetectionLoading: false,\n      slowStudents: [],\n      slowStudentsColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          render: (h, params) => WhitePre(h, params.row.student_id)\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          render: (h, params) => WhitePre(h, params.row.student_name)\n        },\n        {\n          title: '班级',\n          key: 'class_name'\n        },\n        {\n          title: '提交次数',\n          key: 'submission_count',\n          render: (h, params) => Tag(h, 'red', params.row.submission_count)\n        },\n        {\n          title: '最后提交时间',\n          key: 'last_submission_time'\n        }\n      ],\n      \n      // 多次挂在同一个P检测\n      repeatedFailuresParams: {\n        failure_threshold: 3\n      },\n      repeatedFailuresLoading: false,\n      repeatedFailureStudents: [],\n      repeatedFailureStudentsColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          render: (h, params) => WhitePre(h, params.row.student_id)\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          render: (h, params) => WhitePre(h, params.row.student_name)\n        },\n        {\n          title: '当前P',\n          key: 'current_project',\n          render: (h, params) => Tag(h, 'blue', params.row.current_project)\n        },\n        {\n          title: '失败次数',\n          key: 'failure_count',\n          render: (h, params) => Tag(h, 'red', params.row.failure_count)\n        },\n        {\n          title: '最后失败时间',\n          key: 'last_failure_time'\n        }\n      ],\n      \n      // 多次没有课上资格检测\n      qualificationFailuresParams: {\n        failure_threshold: 2\n      },\n      qualificationFailuresLoading: false,\n      qualificationFailureStudents: [],\n      qualificationFailureStudentsColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          render: (h, params) => WhitePre(h, params.row.student_id)\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          render: (h, params) => WhitePre(h, params.row.student_name)\n        },\n        {\n          title: '当前P',\n          key: 'current_project',\n          render: (h, params) => Tag(h, 'blue', params.row.current_project)\n        },\n        {\n          title: '缺席次数',\n          key: 'absence_count',\n          render: (h, params) => Tag(h, 'orange', params.row.absence_count)\n        },\n        {\n          title: '最近缺席考试',\n          key: 'last_absence_exam'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadCourses()\n  },\n  methods: {\n    async loadCourses() {\n      try {\n        const res = await courseReq('get')\n        this.courseList = res.data.courses || []\n        if (this.courseList.length > 0) {\n          this.selectedCourse = this.courseList[0].id\n        }\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error))\n      }\n    },\n    \n    onCourseChange() {\n      // 清空之前的检测结果\n      this.slowStudents = []\n      this.repeatedFailureStudents = []\n      this.qualificationFailureStudents = []\n    },\n    \n    onTabChange(name) {\n      this.activeTab = name\n    },\n    \n    async detectSlowProgress() {\n      if (!this.selectedCourse) {\n        this.$Message.warning('请先选择课程')\n        return\n      }\n      \n      this.slowDetectionLoading = true\n      try {\n        // 模拟API调用，返回示例数据\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        this.slowStudents = [\n          {\n            student_id: '2021001',\n            student_name: '张三',\n            class_name: '计科21-1',\n            submission_count: 2,\n            last_submission_time: '2024-01-15 14:30:00'\n          },\n          {\n            student_id: '2021002', \n            student_name: '李四',\n            class_name: '计科21-2',\n            submission_count: 1,\n            last_submission_time: '2024-01-12 09:15:00'\n          }\n        ]\n        this.$Message.success('检测完成')\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error))\n      } finally {\n        this.slowDetectionLoading = false\n      }\n    },\n    \n    exportSlowStudents() {\n      // 模拟导出功能\n      const csvContent = this.generateCSV(this.slowStudents, this.slowStudentsColumns)\n      this.downloadCSV(csvContent, '进度慢学生名单.csv')\n      this.$Message.success('导出成功')\n    },\n    \n    async detectRepeatedFailures() {\n      if (!this.selectedCourse) {\n        this.$Message.warning('请先选择课程')\n        return\n      }\n      \n      this.repeatedFailuresLoading = true\n      try {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        this.repeatedFailureStudents = [\n          {\n            student_id: '2021003',\n            student_name: '王五',\n            current_project: 'P3-数据结构',\n            failure_count: 4,\n            last_failure_time: '2024-01-16 16:45:00'\n          }\n        ]\n        this.$Message.success('检测完成')\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error))\n      } finally {\n        this.repeatedFailuresLoading = false\n      }\n    },\n    \n    async detectQualificationFailures() {\n      if (!this.selectedCourse) {\n        this.$Message.warning('请先选择课程')\n        return\n      }\n      \n      this.qualificationFailuresLoading = true\n      try {\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        this.qualificationFailureStudents = [\n          {\n            student_id: '2021004',\n            student_name: '赵六',\n            current_project: 'P2-算法基础',\n            absence_count: 3,\n            last_absence_exam: '2024-01-14 考试'\n          }\n        ]\n        this.$Message.success('检测完成')\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error))\n      } finally {\n        this.qualificationFailuresLoading = false\n      }\n    },\n    \n    generateCSV(data, columns) {\n      const headers = columns.map(col => col.title).join(',')\n      const rows = data.map(row => \n        columns.map(col => row[col.key] || '').join(',')\n      ).join('\\n')\n      return headers + '\\n' + rows\n    },\n    \n    downloadCSV(content, filename) {\n      const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })\n      const link = document.createElement('a')\n      const url = URL.createObjectURL(blob)\n      link.setAttribute('href', url)\n      link.setAttribute('download', filename)\n      link.style.visibility = 'hidden'\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ivu-card {\n  margin-bottom: 16px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}