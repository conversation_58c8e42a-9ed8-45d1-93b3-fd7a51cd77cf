<template>
  <div class="fail-conclusion">
    <Alert type="warning" show-icon>
      <span slot="desc" class="conclusion-text">{{ conclusion }}</span>
    </Alert>
  </div>
</template>

<script>
export default {
  name: 'FailConclusion',
  props: {
    conclusion: {
      type: String,
      default: '暂无分析结论'
    }
  }
}
</script>

<style scoped>
.fail-conclusion {
  margin: 20px 0;
}
.conclusion-text {
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}
</style>
