"""
为前端提供便于测试的接口
"""
from datetime import datetime

from django.conf import settings
from django.http import HttpRequest
from django.views.decorators.http import require_POST
from django.core.exceptions import ObjectDoesNotExist

from core.api.auth import jwt_auth
from core.api.permissions import CORE_EXAM_QUEUE_CHANGE, CORE_EXAM_QUEUE_TEST
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            response_wrapper, success_api_response,
                            wrapped_api)
from core.models import ProjectInExam
from core.models.course import Course
from core.models.exam import Exam
from core.models.exam_queue import ExamQueue
from core.models.exam_record import STATUS_IN_PROGRESS, ExamRecord, STATUS_WAITING_IN_QUEUE
from core.models.project import Project
from core.models.room import Room
from core.models.seat import Seat
from core.models.student import Student
from core.models.student_progress import StudentProgress
from core.models.student_seat_record import StudentSeatRecord
from core.models.user_profile import UserProfile


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_CHANGE])
@require_POST
# @require_item_exist(Student, "student_id", "student_id")
# 2019-11-06 by Dailan He
# here's a bug inside require_item_exist
# when required item is not the first arg in arg list
# the order of args will be wrong which causes an error
def test_hand_up(request: HttpRequest, room_pk: int, student_id: str):
    """
    测试exam-queue

    arguments:

    - exam: int, optional, exam pk.
        - If null, use current active exam or create one.
        - If negative, create a new Exam anyway

    :param request:
    :param room_pk:
    :param student_id:
    :return:
    """
    if not settings.DEBUG:
        return failed_api_response(ErrorCode.REFUSE_ACCESS)
    student: Student = Student.objects.filter(student_id=student_id).first()

    data: dict = parse_data(request)
    exam_id = data.get("exam")

    room: Room = Room.objects.filter(id=room_pk).first()
    if room is None or student is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND)
    user = request.user
    course: Course = UserProfile.objects.filter(user=user).first().course
    if course is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND)
    time_str: str = str(datetime.now())
    project: Project = Project.objects.create(**{
        'course': course,
        'name': '测试用临时项目' + time_str,
        'depth': 0,
    })

    # try to use existing exam
    if exam_id is not None and exam_id >= 0:
        exam = Exam.objects.filter(id=exam_id).first()
        if exam is None:
            return failed_api_response(ErrorCode.NOT_FOUND_ERROR)
    elif exam_id is None:
        exam = Exam.objects.filter(active=True).first()
        # may be None, then create a new Exam below
    else:
        exam = None

    # if exam is None here, create a new one
    if exam is None:
        exam = Exam.objects.create(active=True, course=course, date=datetime.now())

    project_in_exam: ProjectInExam = ProjectInExam.objects.create(
        exam=exam,
        project=project,
        begin_time=datetime.now(),
        duration=20000000,
        pass_requirement='()'
    )
    project.student_whitelist.add(student)

    StudentProgress.objects.update_or_create(student=student,
                                             course=course,
                                             defaults={'current_project': project_in_exam, 'qualified': True})

    ExamRecord.objects.create(
        student=student,
        project_in_exam=project_in_exam,
        status=STATUS_WAITING_IN_QUEUE
    )
    seat: Seat = room.seat_set.all().first()
    StudentSeatRecord.objects.update_or_create(
        student=student, exam=exam, defaults={'seat': seat})
    exam_queue, _ = ExamQueue.objects.get_or_create(
        student=student,
        room=room,
        exam=project_in_exam.exam,
        popped_at=datetime.now(),
        defaults={'project_in_exam': project_in_exam},
    )
    return success_api_response({'exam_queue_id': exam_queue.id, 'project_in_exam_id': project_in_exam.id})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_CHANGE, CORE_EXAM_QUEUE_TEST])
@require_POST
def test_hand_up_in_exam(request: HttpRequest):
    """Test hand up function in exam

    [method]: POST

    [route]: /api/test-hand-up-in-exam
    """
    request_dict: dict = parse_data(request)
    student_id = request_dict['student_id']
    room_id = request_dict['room_id']
    seat_id = request_dict['seat_id']
    pie_id = request_dict['pie_id']

    try:
        student = Student.objects.get(student_id=student_id)
        room = Room.objects.get(id=room_id)
        seat = room.seat_set.get(pk=seat_id)
        pie = ProjectInExam.objects.get(pk=pie_id)
        course = pie.project.course
    except ObjectDoesNotExist:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND)

    pie.project.student_whitelist.add(student)

    StudentProgress.objects.update_or_create(student=student,
                                             course=course,
                                             defaults={'current_project': pie, 'qualified': True})
    ExamRecord.objects.create(
        student=student,
        project_in_exam=pie,
        status=STATUS_IN_PROGRESS
    )
    StudentSeatRecord.objects.update_or_create(student=student,
                                               exam=pie.exam,
                                               defaults={'seat': seat})
    return success_api_response({
        "Success": "The student is ready to put up hand on edX."
    })


HAND_UP_TEST_API = wrapped_api({
    'post': test_hand_up
})
