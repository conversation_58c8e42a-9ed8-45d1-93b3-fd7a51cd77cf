"""
Class APIs Test
| method | 路径                       | 描述               | Success Code | Success Response   |
| GET    | /api/classes/:class_id    | 查询单个班级信息     | 200          | 班级基础信息       |
| POST   | /api/classes              | 添加单个班级         | 201         | 创建的班级对象的id |
| PUT    | /api/classes/:class_id    | 修改单个班级信息      | 200         | 修改后的班级对象   |
| GET    | /api/classes?params       | 获得符合条件的班级信息 | 200         | 班级信息组        |
| DELETE | /api/classes/:class_id    | 删除一个班级         | 200         | Success Message  |

import json
from django.test import Client, TestCase
from core.models.instructor_class import InstructorClass
from core.models.student import Student
from core.models.course import Course
BASE_URL = "http://localhost:8000/api/classes"


def get_class(id):
    return Client().get(BASE_URL + '/' + 'id')


def create_class(data):
    return Client().post(BASE_URL, json.dumps(data), content_type="application/json")


class TestClassAPI(TestCase):
    def setUp(self) -> None:
        init_student = [
            {'id': 1, 'student_id': '17373503', 'name': 'lmc', 'department': 6},
            {'id': 2, 'student_id': '17373502', 'name': 'ltt', 'department': 6},
            {'id': 3, 'student_id': '17373532', 'name': 'llj', 'department': 6}
        ]
        for student in init_student:
            Student.objects.create(**student)
        lmc = Student.objects.get(name='lmc')
        ltt = Student.objects.get(name='ltt')
        llj = Student.objects.get(name='llj')
        init_course = [
            {'code': 'B3I062310', 'name': '2018秋计算机组成课程设计'}
        ]
        for course in init_course:
            Course.objects.create(**course)
        course = Course.objects.get(pk=1)
        init_class = [
            {'id': 1, 'name': '2018秋gxp老师班', 'teacher': 'gxp'},
            {'id': 2, 'name': '2018秋lxd老师班', 'teacher': 'lxd'}
        ]
        q = InstructorClass(**init_class[0])
        q.student.add(lmc, ltt)
        q.belong_to = course
        q.save()
        q = InstructorClass(**init_class[1])
        q.student.add(llj)
        q.belong_to = course
        q.save()
        self.init_class = init_class

    def assert_code(self, code: int, response):
        try:
            self.assertEqual(code, response.status_code)
        except AssertionError as err:
            print("Expected status code: {}, but actual status code: {}".format(
                code, response.status_code))
            raise err

    def test_all(self):
        self.list_class()
        self.create_class_success()
        self.create_class_failed()

    def list_class(param):
        return Client().get(BASE_URL + 'param')

    def create_class_success(self):
        post_data_1 = {
            "name": "lxdclass1",
            "teacher": "lxd",
            "student": ["17373503"],
            "belong_to": 1
        }
        post_data_2 = {
            "name": "njwclass1",
            "teacher": "njw",
            "student": ["17373503", "17373532"],
            "belong_to": 1
        }
        response = create_class(data=post_data_1)
        self.assert_code(200, response)
        response = create_class(data=post_data_2)
        self.assert_code(200, response)
        self.check_post(3, 'lxdclass1', 'lxd', 1)
        self.check_post(4, 'njwclass1', 'njw', 1)

    def create_class_failed(self):
        post_data_1 = {
            "name": "lxdclass1",
            "teacher": "lxd",
            "student": ["17373503"],
            "belong_to": 1
        }
        post_data_2 = {
            "name": "njwclass",
            "teacher": "njw",
            "student": ["17373503", "17373532"],
            "belong_to": 1,
            "???": "???"
        }
        response = create_class(data=post_data_1)
        self.assert_code(409, response)
        response = create_class(data=post_data_2)
        self.assert_code(403, response)

    def check_post(self, idx, name, teacher, belong_to):
        response = get_class(idx)
        self.assert_code(200, response)
        data = response.json()
        self.assertDictEqual(data, {
            "id": idx,
            "name": name,
            "teacher": teacher,
            "belong_to": belong_to
        })
"""
