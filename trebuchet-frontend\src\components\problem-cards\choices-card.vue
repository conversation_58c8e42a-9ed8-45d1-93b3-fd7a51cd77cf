<template>
  <Card class="problem-cards">
    <p slot="title">Choice 列表</p>
    <form>
      <div v-for="(choice, i) in innerChoices" :key="i" class="flex-line">
        <input
          v-model="choice.selected"
          :type="type === '1' ? 'radio' : 'checkbox'"
          @click="type === '1' ? innerChoices.forEach((item, index) => (item.selected = i === index)) : ''"
          name="g-radioValue"
          value="true"
          autosize
        />
        <span class="horizontal-margin" style="white-space: nowrap"> {{ choice.name }} </span>
        <Input v-model="choice.description" class="horizontal-margin" type="textarea" :autosize="true" />
      </div>
    </form>
  </Card>
</template>

<script>
import './cards.less'

export default {
  name: 'ChoicesCard',
  props: {
    type: String,
    choices: Array,
    answer: String
  },
  data() {
    return {
      innerChoices: []
    }
  },
  mounted() {
    let answer = JSON.parse(this.answer || '[]')
    this.innerChoices = (this.choices || [])
      .map((item) => ({
        description: item.description,
        name: item.answer,
        selected: answer.includes(item.answer)
      }))
      .sort((a, b) => a.name.localeCompare(b.name))
  }
}
</script>
