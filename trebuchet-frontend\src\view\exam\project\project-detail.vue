<template>
  <div>
    <Row>
      <Col span="8" offset="6">
        <Card>
          <Form :ref="formName" :model="newProject" :rules="projectRule" :label-width="100">
            <form-item prop="name" label="Project 名称">
              <Input v-model="newProject.name" type="text" :disabled="updateId !== null" />
            </form-item>
            <form-item prop="course" label="课程编号">
              <Select v-model="newProject.course">
                <Option
                  v-for="course in currentCourses"
                  :key="course.id"
                  :value="String(course.id)"
                  :label="course.name"
                />
              </Select>
            </form-item>
            <form-item prop="parent" label="前驱项目(可选)">
              <Select v-model="newProject.parent">
                <Option
                  v-for="project in currentProjects"
                  :key="project.id"
                  :value="String(project.id)"
                  :label="project.name"
                />
              </Select>
            </form-item>
            <form-item>
              <Button v-if="!updateId" type="primary" @click="handleSubmit">确认创建</Button>
              <Button v-if="updateId" type="primary" @click="handleSubmit">确认上传</Button>
            </form-item>
          </Form>
        </Card>
      </Col>
    </Row>
    <br />
    <Row>
      <Col offset="6" span="8">
        <Card v-if="updateId !== null">
          <p slot="title">白名单修改</p>
          <template v-if="!isCreate">
            <Row>
              <Table :data="newList.whitelist" :columns="columns" />
              <br />
              <Button type="primary" style="margin-right: 5px" @click="onListDelete">删除白名单</Button>
            </Row>
          </template>
          <template v-else>
            <Form ref="listNew" :model="newList" :label-width="100">
              <form-item label="白名单">
                <div style="display: flex; align-items: center">
                  <Upload :before-upload="beforeUpload" action="">
                    <Button icon="ios-cloud-upload-outline">上传 CSV 文件</Button>
                  </Upload>
                  <label style="margin-left: 10px">(表头: student_id)</label>
                </div>
                <strong>
                  <span style="font-size: small">请确保 CSV 文件的编码格式为 UTF-8</span>
                </strong>
              </form-item>
              <form-item>
                <Button :disabled="!uploadFileReady" type="primary" @click="handleUpload">
                  {{ uploadBtnMsg }}
                </Button>
              </form-item>
            </Form>
          </template>
        </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
import { projectReq, projectReqWithId, projectWhiteReq } from '@/api/project'
import { getArrayFromFile, getErrModalOptions, getTableDataFromArray } from '@/libs/util'
import { userProfileReq } from '@/api/user'
import { courseReq } from '@/api/course'
import _ from 'lodash'

export default {
  name: 'ProjectCreate',
  data() {
    return {
      formName: 'projectForm',
      newProject: {
        name: '',
        course: 0,
        parent: null
      },
      projectRule: {
        name: [{ required: true, message: '请填写项目名称', trigger: 'blur' }],
        course: [{ required: true, message: '请填写课程编号', trigger: 'blur' }],
        parent: [{ required: false, message: '请填写前驱项目编号', trigger: 'blur' }]
      },
      defaultCourse: '0',
      currentProjects: [],
      currentCourses: [],
      isCreate: true,
      newList: {
        whitelist: []
      },
      csvColumns: [],
      uploadBtnMsg: '确认上传',
      expectedColumnNames: ['student_id'],
      columns: [
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: 'Student ID',
          key: 'student_id'
        },
        {
          title: 'Name',
          key: 'name'
        }
      ]
    }
  },
  computed: {
    computedProject() {
      const { name, course, parent } = this.newProject
      const computed = { name, course }
      if (parent) {
        computed['parent_project'] = parent
      }
      return computed
    },
    updateId() {
      return this.$route.params.id || null
    },
    columnNames() {
      return this.csvColumns.map((item) => item.title)
    },
    uploadFileReady() {
      if (!this.columnNames || this.columnNames.length !== this.expectedColumnNames.length) {
        return false
      }
      return this.columnNames.every((item, index) => item === this.expectedColumnNames[index])
    }
  },
  async mounted() {
    try {
      if (this.updateId) {
        const projectBody = await projectReqWithId('get', this.updateId)
        const project = projectBody.data
        const { name, course, parent_project } = project
        this.newProject = { name, course: String(course), parent: String(parent_project) }
      } else {
        const res = await userProfileReq('get')
        if (res.data.course === null) {
          this.$Modal.info({
            title: '请在课程信息/课程总览选择当前课程'
          })
        } else {
          this.defaultCourse = String(res.data.course.id)
          this.newProject.course = this.defaultCourse
        }
      }
    } catch (error) {
      this.$Modal.error(getErrModalOptions(error))
    }
    let projectFilter = {
      course__exact: this.defaultCourse
    }
    // 默认一次请求10条数据
    const projectBodyDefault = await projectReq('get', projectFilter)
    const total_count = projectBodyDefault.data['total_count']
    // 修改page_size，再请求一次
    projectFilter.page_size = total_count + 1
    projectReq('get', projectFilter)
      .then((res) => {
        this.currentProjects = _.filter(res.data.models, (item) => item['id'] !== this.updateId)
      })
      .catch((error) => {
        this.$Modal.error(getErrModalOptions(error))
      })
    courseReq('get')
      .then((res) => {
        this.currentCourses = res.data.data
        this.currentCourses.forEach((item) => {
          if (String(item.id) === this.defaultCourse) item.name += '(当前课程)'
        })
      })
      .catch((error) => {
        this.$Modal.error(getErrModalOptions(error))
      })
    if (this.updateId !== null) {
      projectWhiteReq('get', this.updateId, {})
        .then((res) => {
          this.newList.whitelist = res.data.whitelist
          this.isCreate = this.newList.whitelist.length === 0
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    }
  },
  methods: {
    upload(data) {
      if (this.updateId) {
        const { name, course, parent_project } = data
        const params = {
          name,
          course: Number(course),
          parent_project: parent_project ? (parent_project === 'null' ? -1 : Number(parent_project)) : -1
        }
        return projectReqWithId('put', this.updateId, params)
      }
      return projectReq('post', data)
    },
    handleSubmit() {
      const name = this.formName
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.upload(this.computedProject)
            .then(() => {
              this.$Notice.success({ title: '提交成功' })
              this.$router.push({ name: 'project_tree' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    },
    onListDelete() {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          projectWhiteReq('delete', this.updateId, {})
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.isCreate = true
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    },
    beforeUpload(file) {
      getArrayFromFile(file)
        .then((data) => {
          const { columns, tableData } = getTableDataFromArray(data)
          this.newList.whitelist = tableData.map((item) => item.student_id)
          this.csvColumns = columns
          if (!this.uploadFileReady) {
            this.uploadBtnMsg = '格式不符'
            this.$Notice.warning({ title: '格式不符' })
          } else {
            this.uploadBtnMsg = '确认上传'
          }
        })
        .catch((err) => {
          getErrModalOptions(err)
          this.$Notice.warning({ title: '只能上传 CSV 文件' })
        })
      return false
    },
    handleUpload() {
      projectWhiteReq('post', this.updateId, this.newList)
        .then(() => {
          this.$Notice.success({ title: '创建成功' })
          projectWhiteReq('get', this.updateId, {})
            .then((res) => {
              this.newList.whitelist = res.data.whitelist
              this.isCreate = this.newList.whitelist.length === 0
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    }
  }
}
</script>
