from django.db.models import QuerySet, F
from django.http import HttpRequest
from django.views.decorators.http import require_http_methods

from core.models.exam import Exam
from core.models.exam_record import ExamRecord
from core.models.news import News
from core.models.project_in_exam import ProjectInExam
from core.api.auth import jwt_auth
from core.api.query_utils import (query_filter, query_order_by)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            response_wrapper, success_api_response, wrapped_api, add_notification_for_user)

from core.api.permissions import CORE_NEWS_CHANGE, CORE_NEWS_DELETE, CORE_NEWS_CREATE, CORE_NEWS_VIEW


@response_wrapper
@jwt_auth(perms=[CORE_NEWS_VIEW])
@query_filter(fields=[('id', int), ('active', bool), ('pie_id', int), ('created_by__id', int), ('star', bool),
                      ('exam_id', int)])
@query_order_by(fields=['id', 'pie', 'exam'])
def news_list(request: HttpRequest, *args, **kwargs):
    models: QuerySet = News.objects
    # filter
    filter_ordered = kwargs.get('filter')
    models = models.filter(filter_ordered)

    # order by
    order_by = kwargs.get('order_by')
    if order_by is not None:
        models = models.order_by(*order_by)
    else:
        models = models.order_by('id')

    models_info = list(
        models.values(
            'id', 'content', 'pie_id', 'pie__project__name',
            'star', 'active', 'created_at', 'created_by__username', 'exam_id'
        )
    )
    return success_api_response({"news": models_info})


@response_wrapper
@jwt_auth(perms=[CORE_NEWS_CREATE])
@require_http_methods(["POST"])
def create_news(request: HttpRequest):
    data: dict = parse_data(request)
    if not data["pie_id"]:
        try:
            exam = Exam.objects.filter(id=data["exam_id"]).get()
        except ProjectInExam.DoesNotExist:
            return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到该exam")
        pie_id = None
        exam_id = exam.id
        examrecords = ExamRecord.objects.filter(project_in_exam__exam_id=exam_id)
    else:
        pie = ProjectInExam.objects.filter(id=data["pie_id"]).first()
        if pie is None and data['pie_id'] is not None:
            return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到该pie")
        pie_id = pie.id
        exam_id = pie.exam_id
        examrecords = ExamRecord.objects.filter(project_in_exam_id=pie_id)

    obj = News.objects.create(active=data["active"],
                              content=data["content"],
                              star=data["star"],
                              created_by=request.user,
                              pie_id=pie_id,
                              exam_id=exam_id)

    if data["star"]:
        for record in examrecords.annotate(user_id=F("student__user_id")).all():
            if record.user_id is None:
                continue
            title = "有新的考试通知"
            detail = {"notificationType": "exam-news",
                      "newsId": obj.id,
                      "content": obj.content,
                      "pieId": record.project_in_exam_id}
            add_notification_for_user(record.user_id, title, detail, False)

    return success_api_response({"id": obj.id})


@response_wrapper
@jwt_auth(perms=[CORE_NEWS_CHANGE])
@require_http_methods(["PUT"])
def edit_news(request: HttpRequest):
    data: dict = parse_data(request)

    pie = ProjectInExam.objects.filter(id=data["pie_id"]).first()
    if pie is None and data['pie_id'] is not None:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到该pie")

    try:
        exam = Exam.objects.filter(id=data["exam_id"]).get()
    except ProjectInExam.DoesNotExist:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到该exam")

    if pie is not None and exam.id != pie.exam.id:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "exam与pie不匹配，请重新尝试")
    cnt = News.objects.filter(id=data["id"]) \
        .update(content=data["content"], star=data["star"], active=data["active"], pie=pie, exam=exam)
    if cnt <= 0:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到该通知")
    return success_api_response({"msg": "修改成功"})


@response_wrapper
@jwt_auth(perms=[CORE_NEWS_DELETE])
@require_http_methods(['DELETE'])
def delete_news(request: HttpRequest):
    """
    delete problem
    :param id:
    :param request:
    :return:
    """
    data: dict = parse_data(request)
    model = News.objects.filter(id=data["id"]).delete()
    if model[0] <= 0:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到指定的推送")
    return success_api_response({"msg": "删除成功"})


NEWS_API = wrapped_api({
    'get': news_list,
    'post': create_news,
    'put': edit_news,
    'delete': delete_news
})
