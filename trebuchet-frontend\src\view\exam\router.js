import Main from '@/view/index/main'

export const examRouter = {
  path: '/exam',
  name: 'exam',
  component: Main,
  meta: {
    title: '考试管理',
    icon: 'ios-paper',
    jumpRoute: '/exam/exam-table'
  },
  children: [
    {
      path: 'exam-table',
      name: 'exam_table',
      meta: {
        title: '考试列表'
      },
      component: () => import('@/view/exam/exam/exam-table')
    },
    {
      path: 'exam-create',
      name: 'exam_create',
      meta: {
        title: '考试创建',
        hideInMenu: true
      },
      component: () => import('@/view/exam/exam/exam-create')
    },
    {
      path: 'exam-detail/:id',
      name: 'exam_detail',
      meta: {
        title: '考试详细信息',
        hideInMenu: true
      },
      component: () => import('@/view/exam/exam/exam-detail')
    },
    {
      path: 'progress-push',
      name: 'progress_push',
      meta: {
        title: '进度推进'
      },
      component: () => import('@/view/exam/progress/progress-push')
    },
    {
      path: 'project-tree',
      name: 'project_tree',
      meta: {
        title: '项目管理'
      },
      component: () => import('@/view/exam/project/project-tree')
    },
    {
      path: 'project-detail/:id',
      name: 'project_detail',
      meta: {
        title: '项目创建',
        hideInMenu: true
      },
      component: () => import('@/view/exam/project/project-detail')
    },
    {
      path: 'project-in-exam/:id',
      name: 'project_in_exam',
      meta: {
        title: 'PIE 详情',
        hideInMenu: true
      },
      component: () => import('@/view/exam/project/project-in-exam')
    },
    {
      path: 'student-achieve',
      name: 'student_achieve',
      meta: {
        title: '学生成绩成就'
      },
      component: () => import('@/view/exam/achieve/student-achieve')
    },
    {
      path: 'class-achieve',
      name: 'class_achieve',
      meta: {
        title: '班级成就图表'
      },
      component: () => import('@/view/exam/achieve/class-achieve')
    },
    {
      path: 'fail-analysis',
      name: 'fail_analysis',
      meta: {
        title: '错误分析'
      },
      component: () => import('@/view/exam/analysis/fail-analysis')
    },
    {
      path: 'progress-detection',
      name: 'progress_detection',
      meta: {
        title: '进度检测'
      },
      component: () => import('@/view/exam/progress/progress-detection')
    }
  ]
}
