<template>
  <Launchpad :span="span" :value="value" :font-size="fontSize" :width="width" :height="height" @on-click="onClick" />
</template>

<script>
import _ from 'lodash'
import Launchpad from '@/components/launchpad'

export default {
  name: 'OrderedSelectLaunchpad',
  components: { Launchpad },
  props: {
    fontSize: {
      type: Number,
      default: -1
    },
    row: {
      type: Number,
      default: 3
    },
    col: {
      type: Number,
      default: 3
    },
    span: {
      type: Number,
      default: -1
    },
    blankColorType: {
      type: String,
      default: 'default'
    },
    selectedColorType: {
      type: String,
      default: 'primary'
    },
    width: {
      type: Number,
      default: -1
    },
    height: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      selQueue: []
    }
  },
  computed: {
    valueLength() {
      return this.row * this.col
    },
    value() {
      const data = _.times(this.valueLength, _.constant({ text: '', type: this.blankColorType }))
      this.selQueue.forEach((item, order) => {
        data[item.index] = { text: String(order + 1), type: this.selectedColorType }
      })
      const row = this.row
      const col = this.col
      return { row, col, data }
    }
  },
  watch: {
    col() {
      this.reload()
    },
    row() {
      this.reload()
    }
  },
  methods: {
    onClick(data) {
      const { index } = data
      const { type } = this.value.data[index]
      const selected = type === this.selectedColorType
      if (selected) {
        this.selQueue = this.selQueue.filter((item) => item.index !== index)
      } else {
        this.selQueue = [...this.selQueue, data]
      }
      this.$emit('on-select', this.selQueue)
    },
    reload() {
      this.selQueue = []
    }
  }
}
</script>
