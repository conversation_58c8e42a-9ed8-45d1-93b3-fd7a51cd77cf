# Generated by Django 2.2.4 on 2019-08-31 04:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=100)),
                ('name', models.CharField(max_length=50)),
                ('inherited_from', models.ManyToManyField(related_name='inherited_to', to='core.Course')),
            ],
        ),
        migrations.CreateModel(
            name='Exam',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('active', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('depth', models.IntegerField()),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.Course')),
                ('parent_project',
                 models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.Project')),
            ],
        ),
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('available', models.BooleanField(default=True)),
                ('comment', models.TextField(default='')),
            ],
        ),
        migrations.CreateModel(
            name='Seat',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=25)),
                ('pos_x', models.IntegerField()),
                ('pos_y', models.IntegerField()),
                ('available', models.BooleanField(default=True)),
                ('comment', models.TextField(default='')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Room')),
            ],
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.CharField(max_length=25, unique=True)),
                ('name', models.CharField(max_length=50)),
                ('department', models.IntegerField()),
                ('email', models.EmailField(max_length=254)),
                ('photo', models.ImageField(null=True, upload_to='student/photo')),
            ],
        ),
        migrations.CreateModel(
            name='StudentSeatRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('exam', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Exam')),
                ('seat', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Seat')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Student')),
            ],
        ),
        migrations.CreateModel(
            name='ProjectInExam',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('begin_time', models.TimeField()),
                ('duration', models.IntegerField(default=0)),
                ('exam', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Exam')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Project')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='student_whitelist',
            field=models.ManyToManyField(null=True, to='core.Student'),
        ),
        migrations.CreateModel(
            name='InstructorClass',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('teacher', models.CharField(max_length=50)),
                ('belong_to',
                 models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.Course')),
                ('student', models.ManyToManyField(to='core.Student')),
            ],
        ),
        migrations.AddField(
            model_name='exam',
            name='project',
            field=models.ManyToManyField(through='core.ProjectInExam', to='core.Project'),
        ),
        migrations.CreateModel(
            name='AuthRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('login_at', models.DateTimeField()),
                ('expires_by', models.DateTimeField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
