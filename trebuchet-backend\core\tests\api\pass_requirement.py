"""pass requirement test
"""
from django.test import TestCase

from core.api.student_progress import RequirementParser


class TestParseRequirement(TestCase):
    """TestCase class for parsing requirement
    """

    def setUp(self):
        """initialize
        """
        self.record = {
            "1": True,
            "2": <PERSON>alse,
            "3": <PERSON>als<PERSON>,
            "4": <PERSON>alse,
            "5": True,
            "6": True,
            "7": <PERSON>als<PERSON>,
            "114514": True
        }

    def get_single_result(self, problem_id):
        """helper function
        """
        return self.record.get(str(problem_id), False)

    def parse_requirement(self, requirement: str) -> bool:
        """test helper function
        """
        lexer = RequirementParser(requirement, self.get_single_result)
        return lexer.parse_expr()

    def test_true(self):
        """regular testcase
        """
        self.assertEqual(self.parse_requirement("()"), True)
        self.assertEqual(self.parse_requirement("(1)"), True)
        self.assertEqual(self.parse_requirement("(&(114514)(6))"), True)
        self.assertEqual(self.parse_requirement("(|(114514)(7))"), True)
        self.assertEqual(self.parse_requirement("(&(4)(1))"), False)
        self.assertEqual(self.parse_requirement("(|(1)(2))"), True)
        self.assertEqual(self.parse_requirement("(|(2)(3))"), False)
        self.assertEqual(self.parse_requirement("(V 3 2 (1)(2)(3))"), False)
        self.assertEqual(self.parse_requirement(
            "(V 3 2 (1)(2)(114514))"), True)
        self.assertEqual(self.parse_requirement(
            "(V 3 2 (1)(2)(|(3)(5)))"), True)

    def assert_exception(self, requirement: str):
        """handler function
        """
        try:
            self.parse_requirement(requirement)
        except ValueError:
            return False
        return True

    def test_exception(self):
        """bad testcase
        """
        self.assertFalse(self.assert_exception(""))
        self.assertFalse(self.assert_exception("(& (1) )"))
        self.assertFalse(self.assert_exception("(| (1) )"))
        self.assertFalse(self.assert_exception("(V 3 2 (1) (2))"))
        self.assertFalse(self.assert_exception("& (1) (2)"))
        self.assertFalse(self.assert_exception("(a)"))
        self.assertFalse(self.assert_exception("(Va 3 2 (1) (2) (3))"))
        self.assertFalse(self.assert_exception("(&a (1) (2))"))
        self.assertFalse(self.assert_exception("(|a (1) (2))"))
        self.assertFalse(self.assert_exception("(()"))
        self.assertFalse(self.assert_exception("(& 1 2)"))
        self.assertFalse(self.assert_exception("(| 1 (2))"))
        self.assertFalse(self.assert_exception("(V 3 2 1 2 3)"))
