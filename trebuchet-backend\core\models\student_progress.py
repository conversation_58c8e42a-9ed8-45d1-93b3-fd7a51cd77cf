"""declare StudentProgress Model
"""
from django.db import models

from core.models.course import Course
from core.models.permissions import (PROGRESS_CHANGE, PROGRESS_CREATE,
                                     PROGRESS_DELETE, PROGRESS_VIEW)
from core.models.project_in_exam import ProjectInExam
from core.models.student import Student


class StudentProgress(models.Model):
    """Relationship table between Student and Course

    Attributes:
        student -- A ForeignKey to Student
        course -- A <PERSON><PERSON><PERSON> to Course
        current_project -- A <PERSON><PERSON><PERSON> to ProjectInExam
        passed_project -- A ForeignKey to ProjectInExam
        next_project -- A ForeignKey to ProjectInExam
        qualified -- A <PERSON><PERSON><PERSON><PERSON><PERSON> indicating if the student is qualified for next project
    """
    student = models.ForeignKey(to=Student, on_delete=models.CASCADE)
    course = models.ForeignKey(to=Course, on_delete=models.CASCADE)

    # TODO rename to pie...
    current_project = models.ForeignKey(
        to=ProjectInExam, on_delete=models.CASCADE, related_name="current_project")
    qualified = models.<PERSON><PERSON><PERSON><PERSON>ield(default=False)
    under_class_mark = models.Bo<PERSON>anField(default=False)


    class Meta:
        default_permissions = ()
        permissions = [
            (PROGRESS_CHANGE, PROGRESS_CHANGE),
            (PROGRESS_CREATE, PROGRESS_CREATE),
            (PROGRESS_DELETE, PROGRESS_DELETE),
            (PROGRESS_VIEW, PROGRESS_VIEW)
        ]
        constraints = [
            models.UniqueConstraint(fields=['course', 'student'], name='no duplicate student progress in a course')
        ]
