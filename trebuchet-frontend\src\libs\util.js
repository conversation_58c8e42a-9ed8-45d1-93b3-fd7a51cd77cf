import { home, title } from '@/router/routers'
import { doCustomTimes, hasChild, hasOneOf, objEqual } from '@/libs/tools'

export const getMenuByRouter = (list, access) => {
  const getMenuWithPath = (list, access, path) => {
    let res = []
    const showThisMenuEle = (item, access) => {
      if (item.meta && item.meta.access && item.meta.access.length) {
        return !!hasOneOf(item.meta.access, access)
      } else {
        return true
      }
    }
    list.forEach((item) => {
      if (!item.meta || (item.meta && !item.meta.hideInMenu)) {
        let obj = {
          icon: (item.meta && item.meta.icon) || '',
          name: item.name,
          meta: item.meta,
          fullPath: [...path, item.path].join('/')
        }
        if (hasChild(item) && showThisMenuEle(item, access)) {
          path.push(item.path)
          obj.children = getMenuWithPath(item.children, access, path)
          path.pop()
        }
        if (item.meta && item.meta.href) {
          obj.href = item.meta.href
        }
        if (showThisMenuEle(item, access)) {
          res.push(obj)
        }
      }
    })
    return res
  }
  return getMenuWithPath(list, access, [])
}

export const getBreadCrumbList = (route, homeRoute) => {
  let homeItem = { ...homeRoute, icon: homeRoute.meta.icon }
  let routeMatched = route.matched
  if (routeMatched.some((item) => item.name === homeRoute.name)) return [homeItem]
  let res = routeMatched.map((item) => {
    let meta = { ...item.meta }
    if (meta.title && typeof meta.title === 'function') {
      meta.__titleIsFunction__ = true
      meta.title = meta.title(route)
    }
    let obj = {
      icon: (item.meta && item.meta.icon) || '',
      name: item.name,
      meta: meta
    }
    if (item.name === route.name) {
      return { ...obj, to: route.path }
    } else if (item.meta.jumpRoute) {
      return { ...obj, to: item.meta.jumpRoute }
    }
    return obj
  })
  return [{ meta: { title: home.text }, to: home.path }, ...res]
}

export const getRouteTitleHandled = (route) => {
  let router = { ...route }
  router.meta = { ...route.meta }
  return router
}

export const showTitle = (item) => {
  let { title } = item.meta
  if (!title) return
  title = (item.meta && item.meta.title) || item.name
  return title
}

export const setTagNavListInLocalstorage = (list) => {
  localStorage.tagNaveList = JSON.stringify(list)
}

export const getHomeRoute = (routers, homeName) => {
  let i = -1
  let len = routers.length
  let homeRoute = {}
  while (++i < len) {
    let item = routers[i]
    if (item.children && item.children.length) {
      let res = getHomeRoute(item.children, homeName)
      if (res.name) {
        return res
      }
    } else {
      if (item.name === homeName) homeRoute = item
    }
  }
  return homeRoute
}

export const canTurnTo = (name, access, routes) => {
  const hasAccess = (access, route) => {
    if (route.meta && route.meta.access) {
      return hasOneOf(access, route.meta.access)
    } else {
      return true
    }
  }
  const routePermissionJudge = (list) => {
    return list.some((item) => {
      if (item.children && item.children.length) {
        return routePermissionJudge(item.children)
      } else if (item.name === name) {
        return hasAccess(access, item)
      }
    })
  }
  return routePermissionJudge(routes)
}

export const getArrayFromFile = (file) => {
  let nameSplit = file.name.split('.')
  let format = nameSplit[nameSplit.length - 1]
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.readAsText(file)
    let arr = []
    reader.onload = (evt) => {
      let pasteData = evt.target.result.trim()
      arr = pasteData
        .split(/[\n\u0085\u2028\u2029]|\r\n?/g)
        .map((row) => {
          return row.split('\t')
        })
        .map((item) => {
          return item[0].split(',')
        })
      if (format === 'csv') {
        resolve(arr)
      } else {
        reject(new Error('[Format Error]: 你上传的不是 CSV 文件'))
      }
    }
  })
}

export const getTableDataFromArray = (array) => {
  let columns = []
  let tableData = []
  if (array.length > 1) {
    let titles = array.shift()
    columns = titles.map((item) => {
      return { title: item, key: item }
    })
    tableData = array.map((item) => {
      let res = {}
      item.forEach((col, i) => {
        res[titles[i]] = col
      })
      return res
    })
  }
  return { columns, tableData }
}

export const findNodeUpperByClasses = (ele, classes) => {
  let parentNode = ele.parentNode
  if (parentNode) {
    let classList = parentNode.classList
    if (classList && classes.every((className) => classList.contains(className))) {
      return parentNode
    } else {
      return findNodeUpperByClasses(parentNode, classes)
    }
  }
}

export const routeHasExist = (tagNavList, routeItem) => {
  let len = tagNavList.length
  let res = false
  const routeEqual = (route1, route2) => {
    const params1 = route1.params || {}
    const params2 = route2.params || {}
    const query1 = route1.query || {}
    const query2 = route2.query || {}
    return route1.name === route2.name && objEqual(params1, params2) && objEqual(query1, query2)
  }
  doCustomTimes(len, (index) => {
    if (routeEqual(tagNavList[index], routeItem)) res = true
  })
  return res
}

export const scrollTop = (el, from = 0, to, duration = 500, endCallback) => {
  if (!window.requestAnimationFrame) {
    window.requestAnimationFrame = (callback) => window.setTimeout(callback, 1000 / 60)
  }
  const difference = Math.abs(from - to)
  const step = Math.ceil((difference / duration) * 50)
  const scroll = (start, end, step) => {
    let dist = start + step > end ? end : start + step
    if (start === end) {
      endCallback && endCallback()
      return
    }
    if (start > end) {
      dist = start - step < end ? end : start - step
    }
    if (el === window) {
      window.scrollTo(dist, dist)
    } else {
      el.scrollTop = dist
    }
    window.requestAnimationFrame(() => scroll(dist, end, step))
  }
  scroll(from, to, step)
}

export const setTitle = (routeItem) => {
  const handledRoute = getRouteTitleHandled(routeItem)
  const pageTitle = showTitle(handledRoute)
  window.document.title = pageTitle ? `${title} - ${pageTitle}` : title
}

export const getErrModalOptions = (err) => {
  let content
  let title
  if (!err.response) {
    if (err.toString) {
      content = err.toString()
      title = '错误'
    } else {
      content = err
    }
  } else {
    title = '服务器错误 ' + (err.status || '')
    const errData = err.response ? err.response.data || err : err
    if (errData['detailed_error_code'] === 40401) {
      return { title: `Error`, content: '当前没有激活的考试' }
    } else if (errData['detailed_error_code'] === 40300) {
      return { title: `拒绝访问`, content: errData['error_msg'] }
    }
    content = typeof errData === 'string' ? errData : JSON.stringify(errData, null, 2)
  }
  return { title, content }
}

export const getLocalTime = (utcTime, isHour, isTimeStamp) => {
  if (utcTime === null || utcTime === undefined) {
    return '无'
  }
  const date = isTimeStamp ? new Date(utcTime * 1000) : new Date(utcTime)
  const second = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  const min = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  const hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  if (isHour) {
    return `${hour}:${min}:${second}`
  } else {
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${hour}:${min}:${second}`
  }
}

export const getPercent = (pass, total) => {
  return total === 0 ? '无' : Math.round((pass / total) * 100) + '%'
}

export const processDownload = (data, name) => {
  const blob = new Blob([data], { type: 'application/octet-stream' })
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = URL.createObjectURL(blob)
  link.download = name
  link.click()
}

export const processRemoteDownload = (res) => {
  const index = res.headers['content-disposition'].indexOf('filename=') + 'filename'.length
  processDownload(res.data, decodeURI(res.headers['content-disposition'].substring(index + 1)))
}
