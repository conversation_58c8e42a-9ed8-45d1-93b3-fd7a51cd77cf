"""
declare Announcement model
"""

from django.db import models

from core.models.course import Course


class Announcement(models.Model):
    """This model describes a course.

        此模型用于描述教学课程通知。

        Modeling announcement in SQL is more detailed.

        Announcement 主体为文本, 可以再加入发布时间等。

        Attribute:
            date: charField, store date of the announcement
            context: Char<PERSON>ield, store context of the announcement

        属性:
            date: <PERSON>r<PERSON><PERSON>, 用于存储通知日期
            context: CharField, 用于存储通知内容
    """
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    content = models.TextField()
    course = models.ForeignKey(to=Course, on_delete=models.CASCADE)

    class Meta:
        # no permissions needed
        default_permissions = ()
