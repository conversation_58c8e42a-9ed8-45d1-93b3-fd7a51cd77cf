"""
define exam-record api
"""
from collections import defaultdict
from datetime import date, datetime, timedelta

import json
import pandas

from django.core.exceptions import FieldError, ValidationError
from django.core.paginator import Paginator
from django.db.models import (Bo<PERSON>anField, Case, F, ObjectDoesNotExist, When,
                              OuterRef, Q, QuerySet, Subquery, Value, TextField)
from django.forms import model_to_dict
from django.http import HttpRequest, HttpResponse
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.exam_check_in_and_out import check_out_exam_record
from core.api.permissions import (CORE_EXAM_RECORD_CHANGE,
                                  CORE_EXAM_RECORD_CREATE,
                                  CORE_EXAM_RECORD_DELETE,
                                  CORE_EXAM_RECORD_OLD_CHANGE,
                                  CORE_EXAM_RECORD_VIEW,
                                  CORE_EXAM_RECORD_EXPORT)
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from core.forms.exam_record import ExamRecordInfo, ExamRecordUpdateInfo
from core.interface.student_progress import query_student_passed

from core.models.course import Course
from core.models.exam import Exam
from core.models.exam_record import (GRADE_A, GRADE_A_PLUS, GRADE_B, GRADE_C,
                                     GRADE_D, GRADE_F, STATUS_CHECKED_OUT,
                                     ExamRecord, STATUS_IN_PROGRESS)
from core.models.instructor_class import InstructorClass
from core.models.project import Project
from core.models.project_in_exam import ProjectInExam
from core.models.room import Room
from core.models.student_seat_record import StudentSeatRecord
from core.models.student import Student
from core.models.user_profile import UserProfile

from judge.interface.problem_judge_record import \
    (get_final_judge_result_of_student_in_project_in_exam,
     get_problem_final_judge_result_for_core)


def _validate_create_exam_record(request: HttpRequest) -> bool:
    data: dict = parse_data(request)
    exam_record_info: ExamRecordInfo = ExamRecordInfo(data)
    if not exam_record_info.is_valid():
        return False
    query_id = exam_record_info.cleaned_data['student_id']
    if not Student.objects.filter(id=query_id).exists():
        return False
    query_id = exam_record_info.cleaned_data['project_in_exam_id']
    if not ProjectInExam.objects.filter(id=query_id).exists():
        return False
    return True


def _validate_update_exam_record(request: HttpRequest) -> bool:
    data: dict = parse_data(request)
    exam_record_update_info: ExamRecordUpdateInfo = ExamRecordUpdateInfo(data)
    if not exam_record_update_info.is_valid():
        return False
    query_id = data.get('student_id', None)
    if query_id is not None and not Student.objects.filter(id=query_id).exists():
        return False
    query_id = data.get('project_in_exam_id', None)
    if query_id is not None and not ProjectInExam.objects.filter(id=query_id).exists():
        return False
    return True


def _check_in_update_time_range(exam_record: ExamRecord) -> bool:
    exam: Exam = exam_record.project_in_exam.exam
    exam_date: date = exam.date
    deadline = exam_date + timedelta(days=2)
    today = date.today()
    return today < deadline


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CREATE])
@require_POST
@validate_args(func=_validate_create_exam_record)
def create_exam_record(request: HttpRequest):
    """create an exam-record

    [method]: POST

    [route]: /api/exam-record
    """
    data: dict = parse_data(request)
    student_id = data.get('student_id')
    data['student'] = Student.objects.get(id=student_id)
    project_in_exam_id = data.get('project_in_exam_id')
    data['project_in_exam'] = ProjectInExam.objects.get(id=project_in_exam_id)
    del data['project_in_exam_id'], data['student_id']
    data['examinant'] = request.user
    exam_record: ExamRecord = ExamRecord.objects.create(**data)
    return success_api_response({'id': exam_record.id})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_VIEW])
@require_GET
@require_item_exist(model=ExamRecord, field='id', item='query_id')
def get_exam_record(request: HttpRequest, query_id: int):
    """get an specified exam_record

    [method]: GET

    [route]: /api/exam-record/<int:query_id>
    """
    exam_record = ExamRecord.objects.get(id=query_id)
    student_id = exam_record.student.student_id
    project_in_exam_id = exam_record.project_in_exam.id
    try:
        passed = query_student_passed(
            student_id, project_in_exam_id)
    except ObjectDoesNotExist:
        passed = False

    examinant_username = None
    if exam_record.examinant:
        examinant_username = exam_record.examinant.username

    if exam_record.question_answer_record is None:
        question_answer_record = None
    else:
        question_answer_record = json.loads(exam_record.question_answer_record)

    if exam_record.project_in_exam.question is None:
        question = None
    else:
        question = json.loads(exam_record.project_in_exam.question)
    data = {
        "id": exam_record.id,
        "student__student_id": exam_record.student.student_id,
        "student__name": exam_record.student.name,
        "project_in_exam__project__name": exam_record.project_in_exam.project.name,
        "project_in_exam__project__id": exam_record.project_in_exam.project.id,
        "status": exam_record.status,
        "check_result": exam_record.check_result,
        "checked_in_at": exam_record.checked_in_at,
        "checked_out_at": exam_record.checked_out_at,
        "examinant__username": examinant_username,
        "check_comment": exam_record.check_comment,
        'project_in_exam__exam__id': exam_record.project_in_exam.exam.id,
        'project_in_exam__exam__date': exam_record.project_in_exam.exam.date,
        'extend_time': exam_record.extend_time,
        "passed": passed,
        'question_answer_record': question_answer_record,
        'question': question
    }
    return success_api_response(data)


def _validate_question_answer_record(question_answer_record: list) -> bool:
    if not isinstance(question_answer_record, list) or len(question_answer_record) == 0:
        return False
    for record in question_answer_record:
        if not isinstance(record, dict) or record.keys() <= {"q", "a", "note"}:
            return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CHANGE])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_exam_record)
@require_item_exist(model=ExamRecord, field='id', item='query_id')
def update_exam_record(request: HttpRequest, query_id: int):
    """update an specified exam-record

    [method]: PUT

    [route]: /api/exam-record/<int:query_id>
    """
    exam_record: ExamRecord = ExamRecord.objects.get(id=query_id)
    user = request.user
    if not user.has_perms([CORE_EXAM_RECORD_OLD_CHANGE]) and not _check_in_update_time_range(exam_record):
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "out time limit")
    data: dict = parse_data(request)

    student_id = data.get('student_id', None)
    if student_id is not None:
        data['student'] = Student.objects.get(id=student_id)
        del data['student_id']

    project_in_exam_id = data.get('project_in_exam_id', None)
    if project_in_exam_id is not None:
        data['project_in_exam'] = ProjectInExam.objects.get(id=student_id)
        del data['project_in_exam_id']
    next_status = data.get('status', None)
    if next_status is STATUS_CHECKED_OUT:
        if not check_out_exam_record(exam_record):
            return failed_api_response(ErrorCode.REFUSE_ACCESS, "Checkout failed")

    question_answer_record = data.get("question_answer_record", None)
    if question_answer_record is not None:
        if _validate_question_answer_record(question_answer_record):
            return failed_api_response(ErrorCode.REFUSE_ACCESS, "question answer record format error")
        question_answer_record = json.dumps(question_answer_record)
    exam_record.question_answer_record = question_answer_record

    try:
        for key in data.keys():
            if data[key] is None or key == "question_answer_record":
                continue
            setattr(exam_record, key, data[key])
        exam_record.examinant = request.user
        exam_record.save()
    except TypeError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
    except ValidationError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)

    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_DELETE])
@require_http_methods(['DELETE'])
@require_item_exist(model=ExamRecord, field='id', item='query_id')
def delete_exam_record(request: HttpRequest, query_id: int) -> dict:
    """delete an specified exam-record

    [method]: DELETE

    [route]: /api/exam-record/<int:query_id>
    """
    model = ExamRecord.objects.get(id=query_id)
    info = model_to_dict(model)
    model.delete()
    return success_api_response(info)


def student_id_filter_helper(key: str, value: str):
    """helper function for list_exam_records filter
    """
    key = "student__student_id__" + key.split("__")[-1]
    return Q(**{key: value})


def exam_filter_helper(key: str, value: int):
    """helper function for list_exam_records filter
    """
    key = "project_in_exam__exam__id__" + key.split("__")[-1]
    return Q(**{key: value})


def project_name_filter_helper(key: str, value: str):
    """helper function for list_exam_records filter
    """
    key = "project_in_exam__project__name__" + key.split("__")[-1]
    return Q(**{key: value})


def date_filter_helper(key: str, value: str):
    """helper function for list_exam_records filter
    """
    key = "project_in_exam__exam__date"
    return Q(**{key: datetime.strptime(value, "%Y-%m-%d")})


def student_name_filter_helper(key: str, value: str):
    """helper function for list_exam_records filter
    """
    key = "student__name__" + key.split("__")[-1]
    return Q(**{key: value})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_VIEW])
@require_GET
@query_filter(fields=[("status", int), ('check_result', int), ('student_id', str), ('date', str),
                      ('exam', int), ('project_name', str), ('examinant__username', str), ('name', str),
                      ('course_name', str), ('teacher_name', str)],
              custom={'student_id': student_id_filter_helper, 'project_name': project_name_filter_helper,
                      'exam': exam_filter_helper, 'date': date_filter_helper, 'name': student_name_filter_helper})
@query_distinct(fields=['status', 'check_result'], model=ExamRecord)
@query_order_by(fields=['status', 'check_result', 'project_in_exam__exam__date'])
@query_page(default=10)
def list_exam_records(request: HttpRequest, *args, **kwargs):
    """list exam-records

    [method]: GET

    [route]: /api/exam-record
    """
    models_all = ExamRecord.objects.count()
    models: QuerySet = ExamRecord.objects.all() \
        .annotate(course_name=Subquery(Course.objects
                                       .filter(pk=OuterRef("project_in_exam__project__course"))
                                       .values("name"))) \
        .annotate(teacher_name=Subquery(InstructorClass.objects
                                        .filter(belong_to=OuterRef("project_in_exam__project__course"),
                                                student__id__exact=OuterRef("student__id"))
                                        .values("teacher")))
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    # order by
    order_by = kwargs.get('order_by')
    try:
        if order_by is not None:
            order_by.append('-id')
            models = models.order_by(*order_by)
        else:
            models = models.order_by('-id')
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   'Unsupported Order Method')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'student__student_id', 'student__name', 'project_in_exam__project__name',
                'status', 'check_result', 'checked_in_at', 'checked_out_at', 'examinant__username',
                'examinant__first_name', 'examinant__last_name',
                'check_comment', 'project_in_exam__exam__id', 'project_in_exam__exam__date', 'teacher_name',
                'course_name', 'extend_time'
            ))
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_VIEW])
@require_GET
@require_item_exist(model=Student, field='student_id', item='student_id')
def get_student_all_exam_result(request: HttpRequest, student_id: str):
    """微信获取学生成绩列表

    [method]: GET

    [route]: /api/wechat-get-student-exam-grade-list/<str:student_id>
    """
    student: Student = Student.objects.filter(student_id=student_id).first()
    exam_record_set: QuerySet = ExamRecord.objects.filter(student=student) \
        .annotate(date=F("project_in_exam__exam__date")) \
        .annotate(project_name=F("project_in_exam__project__name")) \
        .annotate(exam_result=(Case(When(check_result__gt=-1,
                                         then=Value(True)),
                                    default=Value(False),
                                    output_field=BooleanField())))

    data = {
        "score_info": list(exam_record_set.values("date", "project_name", "exam_result"))
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_VIEW])
@require_GET
@require_item_exist(model=ExamRecord, field='id', item='query_id')
def get_exam_record_problem_last_judge_result(request: HttpRequest, query_id: int):
    """获取 ExamRecord 各个题目最近一次提交的评测结果

    [method]: GET

    [route]: /api/exam-record-problem-detail/<int:query_id>
    """
    exam_record: ExamRecord = ExamRecord.objects.get(id=query_id)

    answered_problems = get_final_judge_result_of_student_in_project_in_exam(
        exam_record.project_in_exam.id,
        exam_record.student.student_id
    )

    for problem in exam_record.project_in_exam.problems.all():
        if problem.id not in answered_problems:
            answered_problems[problem.id] = {
                'problem_name': problem.name,
                'record_id': None,
                'judge_result': 'Failed',
            }

    return success_api_response({
        'student_id': exam_record.student.student_id,
        'problems': answered_problems,
    })


def _get_project_in_exam_info(course: Course, projects: QuerySet):
    project_in_exams = {}
    for project in projects:
        pies = ProjectInExam.objects.filter(project_id=project['id']) \
            .exclude(exam=course.under_class_exam) \
            .order_by('exam__date')
        batch = 1
        for pie in pies:
            project_in_exams[pie.id] = {
                'project_id': pie.project.id,
                'project_name': pie.project.name,
                'date': pie.exam.date,
                'batch': batch,
                'pass_requirement': pie.pass_requirement,
                'problems': pie.problems.all()
            }
            batch += 1
    return project_in_exams


def _get_all_passed_exam_records(course: Course):
    all_passed_records = ExamRecord.objects.filter(
        project_in_exam__exam__course=course,
        status=STATUS_CHECKED_OUT,
        check_result__gt=GRADE_F) \
        .order_by("student__id", "-project_in_exam__id") \
        .annotate(grade=(Case(When(check_result=GRADE_A_PLUS, then=Value("A+")),
                              When(check_result=GRADE_A, then=Value("A")),
                              When(check_result=GRADE_B, then=Value("B")),
                              When(check_result=GRADE_C, then=Value("C")),
                              When(check_result=GRADE_D, then=Value("D")),
                              default=Value("F"), output_field=TextField()))) \
        .values("student__student_id", "project_in_exam__id",
                "grade", "check_comment", "examinant__username")
    return all_passed_records


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_EXPORT])
def export_all_exam_records(request: HttpRequest, course_id: int):
    """All exam records collection
    """
    course = Course.objects.get(pk=course_id)
    projects = Project.objects.filter(course=course).order_by('depth').values('id', 'name', 'depth')
    project_in_exams = _get_project_in_exam_info(course, projects)

    all_passed_records = _get_all_passed_exam_records(course)

    result = defaultdict(dict)
    for student in Student.objects.filter(instructorclass__belong_to=course):
        result[student.student_id]['student_name'] = student.name
        result[student.student_id]['official_class'] = student.official_class
        result[student.student_id]['department'] = student.department
        result[student.student_id]['teacher'] = InstructorClass.objects.filter(student=student).first().teacher
    for retake_student in course.retake_students.all():
        result[retake_student.student_id]['retake'] = True

    for record in all_passed_records:
        student_id = record['student__student_id']
        pie = project_in_exams[record['project_in_exam__id']]

        passed_problem_count = sum(get_problem_final_judge_result_for_core(
            student_id,
            problem.id,
            record['project_in_exam__id']) for problem in pie['problems'])

        result[student_id]['{}_grade'.format(pie['project_name'])] = record['grade']
        result[student_id]['{}_batch'.format(pie['project_name'])] = pie['batch']
        result[student_id]['{}_passed_problems'.format(pie['project_name'])] = passed_problem_count
        result[student_id]['{}_total_problems'.format(pie['project_name'])] = len(pie['problems'])
        result[student_id]['{}_comment'.format(pie['project_name'])] = record['check_comment'] \
            .replace(",", "，").replace("\n", "；")

    columns = ['student_name', 'official_class', 'department', 'teacher', 'retake']
    for project in projects:
        columns.extend([
            '{}_grade'.format(project['name']),
            '{}_batch'.format(project['name']),
            '{}_passed_problems'.format(project['name']),
            '{}_total_problems'.format(project['name']),
            '{}_comment'.format(project['name'])
        ])

    data_frame = pandas.DataFrame.from_dict(result, columns=columns, orient='index')
    data_frame.to_csv("nice.csv", quoting=None)
    return HttpResponse(data_frame.to_csv())


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_EXPORT])
def export_final_exam_records(request: HttpRequest, course_id: int):
    """Final project exam records
    """
    course = Course.objects.get(pk=course_id)
    projects = Project.objects.filter(course=course).order_by('depth').values('id', 'name', 'depth')
    project_in_exams = _get_project_in_exam_info(course, projects)

    all_passed_records = _get_all_passed_exam_records(course)

    result = defaultdict(dict)
    for student in Student.objects.filter(instructorclass__belong_to=course):
        result[student.student_id]['student_name'] = student.name
        result[student.student_id]['official_class'] = student.official_class
        result[student.student_id]['department'] = student.department
        result[student.student_id]['teacher'] = InstructorClass.objects.filter(student=student).first().teacher
    for retake_student in course.retake_students.all():
        result[retake_student.student_id]['retake'] = True

    for record in all_passed_records:
        student_id = record['student__student_id']
        pie = project_in_exams[record['project_in_exam__id']]
        if not result[student_id].get('depth') or \
                result[student_id].get('depth') < projects.get(id=pie['project_id'])['depth']:
            passed_problem_count = sum(get_problem_final_judge_result_for_core(
                student_id,
                problem.id,
                record['project_in_exam__id']) for problem in pie['problems'])
            result[student_id]['depth'] = projects.get(id=pie['project_id'])['depth']
            result[student_id]['final_project_name'] = pie['project_name']
            result[student_id]['final_project_grade'] = record['grade']
            result[student_id]['final_project_batch'] = pie['batch']
            result[student_id]['final_project_passed_problem_count'] = passed_problem_count
            result[student_id]['final_project_total_problem_count'] = len(pie['problems'])
            result[student_id]['final_project_comment'] = record['check_comment'].replace(",", "，").replace("\n", "；")

    columns = [
        'student_name',
        'official_class',
        'department',
        'teacher',
        'retake',
        'final_project_name',
        'final_project_grade',
        'final_project_batch',
        'final_project_passed_problem_count',
        'final_project_total_problem_count',
        'final_project_comment',
    ]

    data_frame = pandas.DataFrame.from_dict(result, columns=columns, orient='index')
    data_frame.to_csv("nice.csv", quoting=None)
    return HttpResponse(data_frame.to_csv())


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_CHANGE])
@require_http_methods(['PUT'])
def delay_exam(request: HttpRequest):
    """Batch delay students by room_id and project_id

    [method]: PUT

    [route]: /api/exam-record/delay_minute
    """
    data: dict = parse_data(request)
    room_id = data["room_id"]
    if not Room.objects.filter(pk=room_id).exists():
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND)
    room = Room.objects.get(pk=room_id)
    course_id = UserProfile.objects.filter(user=request.user).first().course_id
    exam: Exam = Exam.objects.filter(course_id=course_id, active=True).first()
    if exam is None:
        return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)
    student_pks = StudentSeatRecord.objects \
        .filter(exam=exam, seat__room=room) \
        .values_list("student__id", flat=True)
    if data.get("project_id") is None:
        ExamRecord.objects.filter(student__id__in=student_pks, project_in_exam__exam=exam,
                                  status=STATUS_IN_PROGRESS).update(extend_time=F("extend_time") + data["minute"])
    else:
        project_id = data["project_id"]
        if not Project.objects.filter(pk=project_id).exists():
            return failed_api_response(ErrorCode.ITEM_NOT_FOUND)
        project = Project.objects.get(pk=project_id)
        ExamRecord.objects.filter(student__id__in=student_pks, project_in_exam__exam=exam,
                                  project_in_exam__project=project, status=STATUS_IN_PROGRESS).update(
            extend_time=F("extend_time") + data["minute"])
    return success_api_response({'success': True})


EXAM_RECORD_PROBLEM_DETAIL_API = wrapped_api({
    "get": get_exam_record_problem_last_judge_result
})

EXAM_RECORD_DETAIL_API = wrapped_api({
    "get": get_exam_record,
    "put": update_exam_record,
    "delete": delete_exam_record
})

EXAM_RECORD_SET_API = wrapped_api({
    'get': list_exam_records,
    'post': create_exam_record
})

WECHAT_GET_GRADE_LIST_API = wrapped_api({
    'get': get_student_all_exam_result
})

EXAM_RECORD_DELAY_API = wrapped_api(({
    'put': delay_exam
}))
