import { getRequest } from '@/api/util'

export const modifyProgressReq = (method, cid, params) => {
  return getRequest(`/api/progress/${cid}`, method, params)
}

// 进度检测相关API
export const slowDetectionReq = (coursePk, params) => {
  return getRequest(`/api/progress/slow-detection/${coursePk}`, 'get', params)
}

export const examPassDetectionReq = (examPk) => {
  return getRequest(`/api/progress/exam-pass-detection/${examPk}`, 'get')
}

export const repeatedFailuresReq = (coursePk, params) => {
  return getRequest(`/api/progress/repeated-failures/${coursePk}`, 'get', params)
}

export const qualificationFailuresReq = (coursePk, params) => {
  return getRequest(`/api/progress/qualification-failures/${coursePk}`, 'get', params)
}
