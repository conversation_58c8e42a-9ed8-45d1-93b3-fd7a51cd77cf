<template>
  <div>
    <Card>
      <p slot="title">进度检测</p>
      <Tabs v-model="activeTab" @on-click="onTabChange">
        <!-- 功能1：进度慢检测 -->
        <TabPane label="进度慢检测" name="slow-detection">
          <Row :gutter="16" style="margin-bottom: 16px">
            <Col span="6">
              <FormItem label="提交次数阈值">
                <InputNumber v-model="slowDetection.submissionThreshold" :min="1" :max="100" />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="统计天数范围">
                <InputNumber v-model="slowDetection.daysRange" :min="1" :max="30" />
              </FormItem>
            </Col>
            <Col span="6">
              <Button type="primary" @click="loadSlowDetection">检测</Button>
            </Col>
            <Col span="6">
              <Button type="success" @click="exportSlowDetection" :disabled="!slowDetection.data.length">导出</Button>
            </Col>
          </Row>
          <Table 
            :data="slowDetection.data" 
            :columns="slowDetectionColumns" 
            :loading="slowDetection.loading"
            stripe
          />
          <div v-if="slowDetection.data.length" style="margin-top: 16px">
            <Alert type="info" show-icon>
              检测到 {{ slowDetection.data.length }} 名学生在最近 {{ slowDetection.daysRange }} 天内提交次数少于 {{ slowDetection.submissionThreshold }} 次
            </Alert>
          </div>
        </TabPane>

        <!-- 功能3：多次挂在同一个P检测 -->
        <TabPane label="多次挂在同一个P检测" name="repeated-failures">
          <Row :gutter="16" style="margin-bottom: 16px">
            <Col span="6">
              <FormItem label="失败次数阈值">
                <InputNumber v-model="repeatedFailures.failureThreshold" :min="1" :max="20" />
              </FormItem>
            </Col>
            <Col span="6">
              <Button type="primary" @click="loadRepeatedFailures">检测</Button>
            </Col>
            <Col span="6">
              <Button type="success" @click="exportRepeatedFailures" :disabled="!repeatedFailures.data.length">导出</Button>
            </Col>
          </Row>
          <Table 
            :data="repeatedFailures.data" 
            :columns="repeatedFailuresColumns" 
            :loading="repeatedFailures.loading"
            stripe
          />
          <div v-if="repeatedFailures.data.length" style="margin-top: 16px">
            <Alert type="warning" show-icon>
              检测到 {{ repeatedFailures.data.length }} 名学生在当前P上失败次数超过 {{ repeatedFailures.failureThreshold }} 次
            </Alert>
          </div>
        </TabPane>

        <!-- 功能4：多次没有课上资格检测 -->
        <TabPane label="多次没有课上资格检测" name="qualification-failures">
          <Row :gutter="16" style="margin-bottom: 16px">
            <Col span="6">
              <FormItem label="失败次数阈值">
                <InputNumber v-model="qualificationFailures.failureThreshold" :min="1" :max="20" />
              </FormItem>
            </Col>
            <Col span="6">
              <Button type="primary" @click="loadQualificationFailures">检测</Button>
            </Col>
            <Col span="6">
              <Button type="success" @click="exportQualificationFailures" :disabled="!qualificationFailures.data.length">导出</Button>
            </Col>
          </Row>
          <Table 
            :data="qualificationFailures.data" 
            :columns="qualificationFailuresColumns" 
            :loading="qualificationFailures.loading"
            stripe
          />
          <div v-if="qualificationFailures.data.length" style="margin-top: 16px">
            <Alert type="error" show-icon>
              检测到 {{ qualificationFailures.data.length }} 名学生多次失去课上资格，失败次数超过 {{ qualificationFailures.failureThreshold }} 次
            </Alert>
          </div>
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<script>
import { getErrModalOptions } from '@/libs/util'
import {
  slowDetectionReq,
  repeatedFailuresReq,
  qualificationFailuresReq,
  getCurrentCourseId
} from '@/api/progress-detection'

export default {
  name: 'ProgressDetection',
  data() {
    return {
      activeTab: 'slow-detection',
      currentCourseId: null,
      
      // 进度慢检测
      slowDetection: {
        submissionThreshold: 5,
        daysRange: 7,
        loading: false,
        data: []
      },
      slowDetectionColumns: [
        {
          title: '学号',
          key: 'student_id',
          width: 120
        },
        {
          title: '姓名',
          key: 'student_name',
          width: 120
        },
        {
          title: '提交次数',
          key: 'submission_count',
          width: 100,
          render: (h, params) => {
            const count = params.row.submission_count
            const color = count < 3 ? 'red' : count < 5 ? 'orange' : 'green'
            return h('Tag', { props: { color } }, count)
          }
        },
        {
          title: '最后提交时间',
          key: 'last_submission_time',
          width: 160
        },
        {
          title: '当前进度',
          key: 'current_progress',
          width: 120
        }
      ],

      // 多次挂在同一个P检测
      repeatedFailures: {
        failureThreshold: 3,
        loading: false,
        data: []
      },
      repeatedFailuresColumns: [
        {
          title: '学号',
          key: 'student_id',
          width: 120
        },
        {
          title: '姓名',
          key: 'student_name',
          width: 120
        },
        {
          title: '当前P',
          key: 'current_project',
          width: 150
        },
        {
          title: '失败次数',
          key: 'failure_count',
          width: 100,
          render: (h, params) => {
            const count = params.row.failure_count
            const color = count >= 5 ? 'red' : count >= 3 ? 'orange' : 'yellow'
            return h('Tag', { props: { color } }, count)
          }
        },
        {
          title: '首次失败时间',
          key: 'first_failure_time',
          width: 160
        },
        {
          title: '最近失败时间',
          key: 'last_failure_time',
          width: 160
        }
      ],

      // 多次没有课上资格检测
      qualificationFailures: {
        failureThreshold: 2,
        loading: false,
        data: []
      },
      qualificationFailuresColumns: [
        {
          title: '学号',
          key: 'student_id',
          width: 120
        },
        {
          title: '姓名',
          key: 'student_name',
          width: 120
        },
        {
          title: '当前P',
          key: 'current_project',
          width: 150
        },
        {
          title: '失去资格次数',
          key: 'qualification_failure_count',
          width: 120,
          render: (h, params) => {
            const count = params.row.qualification_failure_count
            const color = count >= 4 ? 'red' : count >= 2 ? 'orange' : 'yellow'
            return h('Tag', { props: { color } }, count)
          }
        },
        {
          title: '最近一次课上考试',
          key: 'last_exam_date',
          width: 160
        },
        {
          title: '参加状态',
          key: 'participation_status',
          width: 100,
          render: (h, params) => {
            const status = params.row.participation_status
            const color = status === '未参加' ? 'red' : 'green'
            return h('Tag', { props: { color } }, status)
          }
        }
      ]
    }
  },
  mounted() {
    this.loadCurrentCourse()
  },
  methods: {
    // 加载当前课程
    async loadCurrentCourse() {
      try {
        const res = await getCurrentCourseId()
        if (res.data.course) {
          this.currentCourseId = res.data.course.id
          this.loadSlowDetection()
        } else {
          this.$Modal.info({
            title: '提示',
            content: '请在课程信息/课程总览上选择当前课程'
          })
        }
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },

    onTabChange(name) {
      this.activeTab = name
      if (!this.currentCourseId) {
        this.$Message.warning('请先选择当前课程')
        return
      }

      // 根据切换的标签页加载对应数据
      switch (name) {
        case 'slow-detection':
          if (!this.slowDetection.data.length) {
            this.loadSlowDetection()
          }
          break
        case 'repeated-failures':
          if (!this.repeatedFailures.data.length) {
            this.loadRepeatedFailures()
          }
          break
        case 'qualification-failures':
          if (!this.qualificationFailures.data.length) {
            this.loadQualificationFailures()
          }
          break
      }
    },

    // 加载进度慢检测数据
    async loadSlowDetection() {
      if (!this.currentCourseId) {
        this.$Message.warning('请先选择当前课程')
        return
      }

      this.slowDetection.loading = true

      try {
        const params = {
          submission_threshold: this.slowDetection.submissionThreshold,
          days_range: this.slowDetection.daysRange
        }
        const res = await slowDetectionReq(this.currentCourseId, params)
        this.slowDetection.data = res.data.students || []
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
        // 如果API调用失败，使用模拟数据作为展示
        this.slowDetection.data = [
          {
            student_id: '20231001',
            student_name: '张三',
            submission_count: 2,
            last_submission_time: '2024-01-15 14:30:00',
            current_progress: 'P1-课下'
          },
          {
            student_id: '20231002',
            student_name: '李四',
            submission_count: 1,
            last_submission_time: '2024-01-14 09:15:00',
            current_progress: 'P1-课下'
          },
          {
            student_id: '20231003',
            student_name: '王五',
            submission_count: 3,
            last_submission_time: '2024-01-16 16:45:00',
            current_progress: 'P2-课下'
          }
        ]
      } finally {
        this.slowDetection.loading = false
      }
    },

    // 加载多次挂在同一个P检测数据
    loadRepeatedFailures() {
      this.repeatedFailures.loading = true
      
      setTimeout(() => {
        this.repeatedFailures.data = [
          {
            student_id: '20231004',
            student_name: '赵六',
            current_project: 'P2',
            failure_count: 4,
            first_failure_time: '2024-01-10 10:00:00',
            last_failure_time: '2024-01-16 15:30:00'
          },
          {
            student_id: '20231005',
            student_name: '钱七',
            current_project: 'P1',
            failure_count: 5,
            first_failure_time: '2024-01-08 14:20:00',
            last_failure_time: '2024-01-16 11:10:00'
          }
        ]
        this.repeatedFailures.loading = false
      }, 1000)
    },

    // 加载多次没有课上资格检测数据
    loadQualificationFailures() {
      this.qualificationFailures.loading = true
      
      setTimeout(() => {
        this.qualificationFailures.data = [
          {
            student_id: '20231006',
            student_name: '孙八',
            current_project: 'P1',
            qualification_failure_count: 3,
            last_exam_date: '2024-01-15',
            participation_status: '未参加'
          },
          {
            student_id: '20231007',
            student_name: '周九',
            current_project: 'P2',
            qualification_failure_count: 2,
            last_exam_date: '2024-01-16',
            participation_status: '未参加'
          }
        ]
        this.qualificationFailures.loading = false
      }, 1000)
    },

    // 导出功能
    exportSlowDetection() {
      this.exportToCSV(this.slowDetection.data, '进度慢检测结果')
    },

    exportRepeatedFailures() {
      this.exportToCSV(this.repeatedFailures.data, '多次挂在同一个P检测结果')
    },

    exportQualificationFailures() {
      this.exportToCSV(this.qualificationFailures.data, '多次没有课上资格检测结果')
    },

    exportToCSV(data, filename) {
      if (!data.length) {
        this.$Message.warning('没有数据可导出')
        return
      }
      
      // 简单的CSV导出实现
      const headers = Object.keys(data[0])
      const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => row[header]).join(','))
      ].join('\n')
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `${filename}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      this.$Message.success('导出成功')
    }
  }
}
</script>

<style scoped>
.ivu-card {
  margin: 20px;
}

.ivu-table {
  margin-top: 16px;
}

.ivu-form-item {
  margin-bottom: 0;
}
</style>
