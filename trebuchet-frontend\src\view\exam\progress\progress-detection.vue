<template>
  <div>
    <Card>
      <h2 slot="title">课程进度检测</h2>
      <div slot="extra">
        <Select v-model="selectedCourse" style="width: 200px" @on-change="onCourseChange">
          <Option v-for="course in courseList" :key="course.id" :value="course.id">
            {{ course.name }}
          </Option>
        </Select>
      </div>

      <Tabs :value="activeTab" @on-click="onTabChange">
        <!-- 功能1：进度慢检测 -->
        <TabPane label="进度慢检测" name="slow-detection">
          <Card>
            <Form ref="slowDetectionForm" :model="slowDetectionParams" :label-width="120" inline>
              <FormItem label="提交次数阈值">
                <InputNumber v-model="slowDetectionParams.submission_threshold" :min="1" :max="100" />
              </FormItem>
              <FormItem label="统计天数范围">
                <InputNumber v-model="slowDetectionParams.days_range" :min="1" :max="30" />
              </FormItem>
              <FormItem>
                <Button type="primary" @click="detectSlowProgress" :loading="slowDetectionLoading">
                  开始检测
                </Button>
                <Button 
                  type="success" 
                  @click="exportSlowStudents" 
                  :disabled="slowStudents.length === 0"
                  style="margin-left: 8px"
                >
                  一键导出
                </Button>
              </FormItem>
            </Form>
            
            <Alert v-if="slowStudents.length > 0" type="warning" style="margin: 16px 0">
              检测到 {{ slowStudents.length }} 名学生进度较慢
            </Alert>
            
            <Table 
              :data="slowStudents" 
              :columns="slowStudentsColumns" 
              :loading="slowDetectionLoading"
              style="margin-top: 16px"
            />
          </Card>
        </TabPane>

        <!-- 功能3：多次挂在同一个P检测 -->
        <TabPane label="同一P多次失败检测" name="repeated-failures">
          <Card>
            <Form ref="repeatedFailuresForm" :model="repeatedFailuresParams" :label-width="120" inline>
              <FormItem label="失败次数阈值">
                <InputNumber v-model="repeatedFailuresParams.failure_threshold" :min="1" :max="20" />
              </FormItem>
              <FormItem>
                <Button type="primary" @click="detectRepeatedFailures" :loading="repeatedFailuresLoading">
                  开始检测
                </Button>
              </FormItem>
            </Form>
            
            <Alert v-if="repeatedFailureStudents.length > 0" type="error" style="margin: 16px 0">
              检测到 {{ repeatedFailureStudents.length }} 名学生在同一P上多次失败
            </Alert>
            
            <Table 
              :data="repeatedFailureStudents" 
              :columns="repeatedFailureStudentsColumns" 
              :loading="repeatedFailuresLoading"
              style="margin-top: 16px"
            />
          </Card>
        </TabPane>

        <!-- 功能4：多次没有课上资格检测 -->
        <TabPane label="课上资格失败检测" name="qualification-failures">
          <Card>
            <Form ref="qualificationFailuresForm" :model="qualificationFailuresParams" :label-width="120" inline>
              <FormItem label="失败次数阈值">
                <InputNumber v-model="qualificationFailuresParams.failure_threshold" :min="1" :max="10" />
              </FormItem>
              <FormItem>
                <Button type="primary" @click="detectQualificationFailures" :loading="qualificationFailuresLoading">
                  开始检测
                </Button>
              </FormItem>
            </Form>
            
            <Alert v-if="qualificationFailureStudents.length > 0" type="warning" style="margin: 16px 0">
              检测到 {{ qualificationFailureStudents.length }} 名学生多次失去课上资格
            </Alert>
            
            <Table 
              :data="qualificationFailureStudents" 
              :columns="qualificationFailureStudentsColumns" 
              :loading="qualificationFailuresLoading"
              style="margin-top: 16px"
            />
          </Card>
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<script>
import { slowDetectionReq, repeatedFailuresReq, qualificationFailuresReq } from '@/api/progress'
import { courseReq } from '@/api/course'
import { getErrModalOptions } from '@/libs/util'
import { WhitePre, Tag } from '@/libs/render-item'

export default {
  name: 'ProgressDetection',
  data() {
    return {
      activeTab: 'slow-detection',
      selectedCourse: null,
      courseList: [],
      
      // 进度慢检测
      slowDetectionParams: {
        submission_threshold: 5,
        days_range: 7
      },
      slowDetectionLoading: false,
      slowStudents: [],
      slowStudentsColumns: [
        {
          title: '学号',
          key: 'student_id',
          render: (h, params) => WhitePre(h, params.row.student_id)
        },
        {
          title: '姓名',
          key: 'student_name',
          render: (h, params) => WhitePre(h, params.row.student_name)
        },
        {
          title: '班级',
          key: 'class_name'
        },
        {
          title: '提交次数',
          key: 'submission_count',
          render: (h, params) => Tag(h, 'red', params.row.submission_count)
        },
        {
          title: '最后提交时间',
          key: 'last_submission_time'
        }
      ],
      
      // 多次挂在同一个P检测
      repeatedFailuresParams: {
        failure_threshold: 3
      },
      repeatedFailuresLoading: false,
      repeatedFailureStudents: [],
      repeatedFailureStudentsColumns: [
        {
          title: '学号',
          key: 'student_id',
          render: (h, params) => WhitePre(h, params.row.student_id)
        },
        {
          title: '姓名',
          key: 'student_name',
          render: (h, params) => WhitePre(h, params.row.student_name)
        },
        {
          title: '当前P',
          key: 'current_project',
          render: (h, params) => Tag(h, 'blue', params.row.current_project)
        },
        {
          title: '失败次数',
          key: 'failure_count',
          render: (h, params) => Tag(h, 'red', params.row.failure_count)
        },
        {
          title: '最后失败时间',
          key: 'last_failure_time'
        }
      ],
      
      // 多次没有课上资格检测
      qualificationFailuresParams: {
        failure_threshold: 2
      },
      qualificationFailuresLoading: false,
      qualificationFailureStudents: [],
      qualificationFailureStudentsColumns: [
        {
          title: '学号',
          key: 'student_id',
          render: (h, params) => WhitePre(h, params.row.student_id)
        },
        {
          title: '姓名',
          key: 'student_name',
          render: (h, params) => WhitePre(h, params.row.student_name)
        },
        {
          title: '当前P',
          key: 'current_project',
          render: (h, params) => Tag(h, 'blue', params.row.current_project)
        },
        {
          title: '缺席次数',
          key: 'absence_count',
          render: (h, params) => Tag(h, 'orange', params.row.absence_count)
        },
        {
          title: '最近缺席考试',
          key: 'last_absence_exam'
        }
      ]
    }
  },
  mounted() {
    this.loadCourses()
  },
  methods: {
    async loadCourses() {
      try {
        const res = await courseReq('get')
        this.courseList = res.data.courses || []
        if (this.courseList.length > 0) {
          this.selectedCourse = this.courseList[0].id
        }
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    
    onCourseChange() {
      // 清空之前的检测结果
      this.slowStudents = []
      this.repeatedFailureStudents = []
      this.qualificationFailureStudents = []
    },
    
    onTabChange(name) {
      this.activeTab = name
    },
    
    async detectSlowProgress() {
      if (!this.selectedCourse) {
        this.$Message.warning('请先选择课程')
        return
      }
      
      this.slowDetectionLoading = true
      try {
        // 模拟API调用，返回示例数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.slowStudents = [
          {
            student_id: '2021001',
            student_name: '张三',
            class_name: '计科21-1',
            submission_count: 2,
            last_submission_time: '2024-01-15 14:30:00'
          },
          {
            student_id: '2021002', 
            student_name: '李四',
            class_name: '计科21-2',
            submission_count: 1,
            last_submission_time: '2024-01-12 09:15:00'
          }
        ]
        this.$Message.success('检测完成')
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      } finally {
        this.slowDetectionLoading = false
      }
    },
    
    exportSlowStudents() {
      // 模拟导出功能
      const csvContent = this.generateCSV(this.slowStudents, this.slowStudentsColumns)
      this.downloadCSV(csvContent, '进度慢学生名单.csv')
      this.$Message.success('导出成功')
    },
    
    async detectRepeatedFailures() {
      if (!this.selectedCourse) {
        this.$Message.warning('请先选择课程')
        return
      }
      
      this.repeatedFailuresLoading = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.repeatedFailureStudents = [
          {
            student_id: '2021003',
            student_name: '王五',
            current_project: 'P3-数据结构',
            failure_count: 4,
            last_failure_time: '2024-01-16 16:45:00'
          }
        ]
        this.$Message.success('检测完成')
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      } finally {
        this.repeatedFailuresLoading = false
      }
    },
    
    async detectQualificationFailures() {
      if (!this.selectedCourse) {
        this.$Message.warning('请先选择课程')
        return
      }
      
      this.qualificationFailuresLoading = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.qualificationFailureStudents = [
          {
            student_id: '2021004',
            student_name: '赵六',
            current_project: 'P2-算法基础',
            absence_count: 3,
            last_absence_exam: '2024-01-14 考试'
          }
        ]
        this.$Message.success('检测完成')
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      } finally {
        this.qualificationFailuresLoading = false
      }
    },
    
    generateCSV(data, columns) {
      const headers = columns.map(col => col.title).join(',')
      const rows = data.map(row => 
        columns.map(col => row[col.key] || '').join(',')
      ).join('\n')
      return headers + '\n' + rows
    },
    
    downloadCSV(content, filename) {
      const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', filename)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style scoped>
.ivu-card {
  margin-bottom: 16px;
}
</style>
