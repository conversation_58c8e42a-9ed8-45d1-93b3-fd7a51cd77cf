<template>
  <div>
    <Card>
      <h1 slot="title">进度检测功能概览</h1>
      <p slot="extra">
        <Tag color="blue">设计展示版本</Tag>
      </p>

      <Row :gutter="16">
        <Col span="12">
          <Card>
            <h2 slot="title">
              <Icon type="ios-school" style="margin-right: 8px;" />
              考试管理 - 进度检测
            </h2>
            <div slot="extra">
              <Button type="primary" size="small" @click="goToExamProgressDetection">
                进入页面
              </Button>
            </div>
            
            <List>
              <ListItem>
                <ListItemMeta
                  title="功能1：进度慢检测"
                  description="检测提交次数过少的学生，支持一键导出学生名单"
                />
                <div slot="action">
                  <Tag color="green">已实现</Tag>
                </div>
              </ListItem>
              
              <ListItem>
                <ListItemMeta
                  title="功能3：多次挂在同一个P检测"
                  description="统计在同一P上失败次数超过阈值的学生"
                />
                <div slot="action">
                  <Tag color="green">已实现</Tag>
                </div>
              </ListItem>
              
              <ListItem>
                <ListItemMeta
                  title="功能4：多次没有课上资格检测"
                  description="检测多次失去课下资格的学生"
                />
                <div slot="action">
                  <Tag color="green">已实现</Tag>
                </div>
              </ListItem>
            </List>
          </Card>
        </Col>
        
        <Col span="12">
          <Card>
            <h2 slot="title">
              <Icon type="ios-time" style="margin-right: 8px;" />
              课上信息 - 进度检测
            </h2>
            <div slot="extra">
              <Button type="primary" size="small" @click="goToOnExamProgressDetection">
                进入页面
              </Button>
            </div>
            
            <List>
              <ListItem>
                <ListItemMeta
                  title="功能2：考试通过人数检测及无人通过警报"
                  description="实时监测考试的通过情况，包括通过人数、通过率和警报功能"
                />
                <div slot="action">
                  <Tag color="green">已实现</Tag>
                </div>
              </ListItem>
              
              <ListItem>
                <ListItemMeta
                  title="实时数据展示"
                  description="通过人数统计、通过率计算、考试持续时间显示"
                />
                <div slot="action">
                  <Tag color="blue">增强功能</Tag>
                </div>
              </ListItem>
              
              <ListItem>
                <ListItemMeta
                  title="通过趋势图表"
                  description="可视化展示考试过程中的通过人数变化趋势"
                />
                <div slot="action">
                  <Tag color="blue">增强功能</Tag>
                </div>
              </ListItem>
            </List>
          </Card>
        </Col>
      </Row>

      <Card style="margin-top: 16px;">
        <h2 slot="title">
          <Icon type="ios-settings" style="margin-right: 8px;" />
          技术实现特点
        </h2>
        
        <Row :gutter="16">
          <Col span="8">
            <Card>
              <h3 slot="title">前端技术栈</h3>
              <List size="small">
                <ListItem>Vue 2 + View Design UI</ListItem>
                <ListItem>ECharts 图表库</ListItem>
                <ListItem>响应式布局设计</ListItem>
                <ListItem>组件化开发</ListItem>
              </List>
            </Card>
          </Col>
          
          <Col span="8">
            <Card>
              <h3 slot="title">功能特性</h3>
              <List size="small">
                <ListItem>实时数据刷新</ListItem>
                <ListItem>自动警报机制</ListItem>
                <ListItem>数据导出功能</ListItem>
                <ListItem>可配置阈值参数</ListItem>
              </List>
            </Card>
          </Col>
          
          <Col span="8">
            <Card>
              <h3 slot="title">用户体验</h3>
              <List size="small">
                <ListItem>直观的数据可视化</ListItem>
                <ListItem>便捷的操作界面</ListItem>
                <ListItem>清晰的状态提示</ListItem>
                <ListItem>统一的设计风格</ListItem>
              </List>
            </Card>
          </Col>
        </Row>
      </Card>

      <Card style="margin-top: 16px;">
        <h2 slot="title">
          <Icon type="ios-list" style="margin-right: 8px;" />
          API接口设计
        </h2>
        
        <Table :data="apiList" :columns="apiColumns" size="small" />
      </Card>

      <Alert type="info" style="margin-top: 16px;">
        <template slot="desc">
          <p><strong>注意：</strong>这是功能设计展示版本，包含了完整的UI界面和交互逻辑。</p>
          <p>所有数据均为模拟数据，用于演示页面效果和功能流程。</p>
          <p>实际部署时需要连接真实的后端API接口。</p>
        </template>
      </Alert>
    </Card>
  </div>
</template>

<script>
export default {
  name: 'ProgressOverview',
  data() {
    return {
      apiList: [
        {
          function: '进度慢检测',
          method: 'GET',
          url: '/api/progress/slow-detection/<course_pk>',
          params: 'submission_threshold, days_range',
          description: '检测提交次数过少的学生'
        },
        {
          function: '考试通过检测',
          method: 'GET', 
          url: '/api/progress/exam-pass-detection/<exam_pk>',
          params: '无',
          description: '实时检测考试通过情况'
        },
        {
          function: '重复失败检测',
          method: 'GET',
          url: '/api/progress/repeated-failures/<course_pk>',
          params: 'failure_threshold',
          description: '检测在同一P上多次失败的学生'
        },
        {
          function: '资格失败检测',
          method: 'GET',
          url: '/api/progress/qualification-failures/<course_pk>',
          params: 'failure_threshold',
          description: '检测多次失去课上资格的学生'
        }
      ],
      apiColumns: [
        {
          title: '功能',
          key: 'function',
          width: 120
        },
        {
          title: '方法',
          key: 'method',
          width: 80,
          render: (h, params) => h('Tag', { props: { color: 'blue' } }, params.row.method)
        },
        {
          title: 'API路径',
          key: 'url',
          render: (h, params) => h('code', { style: { background: '#f5f5f5', padding: '2px 4px' } }, params.row.url)
        },
        {
          title: '参数',
          key: 'params',
          width: 150
        },
        {
          title: '说明',
          key: 'description'
        }
      ]
    }
  },
  methods: {
    goToExamProgressDetection() {
      this.$router.push({ name: 'progress_detection' })
    },
    
    goToOnExamProgressDetection() {
      this.$router.push({ name: 'exam_progress_detection' })
    }
  }
}
</script>

<style scoped>
.ivu-card {
  margin-bottom: 16px;
}

.ivu-list-item-meta-title {
  font-weight: 600;
}

code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
