worker_processes 1;

events {
    worker_connections 1024;
}

http {
    map $sent_http_content_type $expires {
        default "max-age=36000";
        text/html "no-store"; # means no-cache
    }

    server {
        listen 8000;
        root /usr/share/nginx/html;
        include /etc/nginx/mime.types;
        types {
            application/bat bat;
        }

        location / {
            try_files $uri /index.html =404;
            add_header Cache-Control $expires;
        }

        gzip on;
        gzip_vary on;
        gzip_min_length 256;
        gzip_proxied expired no-cache no-store private auth;

        gzip_types
            application/atom+xml
            application/geo+json
            application/javascript
            application/x-javascript
            application/json
            application/ld+json
            application/manifest+json
            application/rdf+xml
            application/rss+xml
            application/xhtml+xml
            application/xml
            font/eot
            font/otf
            font/ttf
            image/svg+xml
            text/css
            text/javascript
            text/plain
            text/xml;
    }
}
