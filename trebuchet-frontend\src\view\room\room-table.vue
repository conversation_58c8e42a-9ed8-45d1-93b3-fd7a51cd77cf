<template>
  <table-template
    :get-api-data="getRoomData"
    :columns="columns"
    :handles="handles"
    :handle-view="viewRoom"
    :handle-delete="deleteRoom"
    :handle-change="updateRoom"
    :api-data-to-table-data="dataToRooms"
  />
</template>

<script>
import TableTemplate from '@/view/templates/table'
import { roomReq, roomReqWithId } from '@/api/room'

export default {
  name: 'RoomTable',
  components: { TableTemplate },
  data() {
    return {
      columns: [
        { title: '机房id', key: 'id', filter: {}, queryOptions: { type: Number } },
        { title: '名称', key: 'name', editable: true, filter: {} },
        {
          title: '可用性',
          editable: true,
          key: 'available',
          queryOptions: { type: Boolean },
          filter: {
            type: 'Select',
            option: {
              0: {
                value: true,
                name: 'true',
                color: 'green'
              },
              1: {
                value: false,
                name: 'false',
                color: 'red'
              }
            }
          }
        },
        { title: '可用座位数', key: 'seats_available' },
        { title: '总座位数', key: 'seats_all' }
      ],
      handles: ['view', 'delete'],
      buttonHandlers: {
        view: this.viewRoom,
        delete: this.deleteRoom
      }
    }
  },
  methods: {
    getRoomData(params) {
      return roomReq('get', params)
    },
    viewRoom(room) {
      this.$router.push({ name: 'room_editing', params: { id: room.row.id } })
    },
    deleteRoom(room) {
      return roomReqWithId('delete', room.id)
    },
    updateRoom(oldVal, newVal) {
      newVal.name = newVal.name || oldVal.name
      return roomReqWithId('put', oldVal.id, newVal)
    },
    dataToRooms(data) {
      return data['rooms']
    }
  }
}
</script>
