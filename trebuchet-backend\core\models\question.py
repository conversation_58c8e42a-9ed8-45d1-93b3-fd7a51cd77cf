from django.db import models

from core.models.permissions import QUESTION_VIEW, QUESTION_EDIT
from core.models.project import Project


class Question(models.Model):
    """
    课上检查的提问问题
    """
    name = models.CharField(max_length=150)  # 问题名称
    content = models.TextField()  # 问题内容
    hint = models.TextField()  # 提示信息
    created_at = models.DateTimeField(auto_now_add=True)
    enabled = models.BooleanField()  # 是否启用（启用的题目才会被抽到）
    project = models.ForeignKey(to=Project, on_delete=models.CASCADE)  # 所属项目

    class Meta:
        # no default permissions needed
        default_permissions = (QUESTION_VIEW)
        permissions = [
            (QUESTION_VIEW, QUESTION_VIEW),
            (QUESTION_EDIT, QUESTION_EDIT),
        ]
