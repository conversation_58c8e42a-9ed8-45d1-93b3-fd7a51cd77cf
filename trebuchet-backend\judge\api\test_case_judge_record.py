"""
define test case judge record's view-layer functions
"""
from django.core.exceptions import FieldError, ValidationError
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from judge.api.permissions import (JUDGE_CHANGE_TCJR, JUDGE_CREATE_TCJR,
                                   JUDGE_DELETE_TCJR, JUDGE_VIEW_TCJR)
from judge.constants import JUD<PERSON><PERSON>, UNKNOWN_ERROR
from judge.forms.judge_record import TestCaseJudgeRecordInfo
from judge.models import Problem<PERSON><PERSON><PERSON><PERSON><PERSON>, TestCase, TestCaseJudgeR<PERSON>ord


def _validate_create_test_case_judge_record(request: HttpRequest) -> bool:
    """
    validate create test case judge record
    Args:
        request:

    Returns:

    """
    fields = [field.name for field in TestCaseJudgeRecord._meta.get_fields()]
    data: dict = parse_data(request)
    for key in data.keys():
        if key not in fields or key in ['started_at', 'finished_at']:
            return False
    info: TestCaseJudgeRecordInfo = TestCaseJudgeRecordInfo(data)
    if not info.is_valid():
        return False
    judge_result = data.get('judge_result', JUDGING)

    # better method?
    if judge_result < JUDGING or judge_result > UNKNOWN_ERROR:
        return False

    query_id: int = data.get('problem_judge_record', None)
    if query_id is not None:
        model = ProblemJudgeRecord.objects.filter(id=query_id)
        if not model.exists():
            return False
    query_id: int = data.get('test_case', None)
    if query_id is not None:
        model = TestCase.objects.filter(id=query_id)
        if not model.exists():
            return False
    return True


def _validate_update_test_case_judge_record(request: HttpRequest) -> bool:
    """
    validate update test case judge record
    Args:
        request:

    Returns:

    """
    fields = [field.name for field in TestCaseJudgeRecord._meta.get_fields()]
    data: dict = parse_data(request)
    if data is None:
        return False
    for key in data.keys():
        if key not in fields:
            return False

    query_id = data.get('problem_judge_record', None)
    if query_id is not None:
        if not ProblemJudgeRecord.objects.filter(id=query_id).exists():
            return False
    query_id = data.get('test_case', None)
    if query_id is not None:
        if not TestCase.objects.filter(id=query_id).exists():
            return False
    return True


@response_wrapper
@jwt_auth(perms=[JUDGE_CREATE_TCJR])
@validate_args(func=_validate_create_test_case_judge_record)
@require_POST
def create_test_case_judge_record(request: HttpRequest):
    """
    create test case judge record
    :param request:
    :return:
    """
    data: dict = parse_data(request)

    query_id = data.get('test_case', None)
    if query_id is not None:
        data['test_case'] = TestCase.objects.get(id=query_id)

    query_id = data.get('problem_judge_record', None)
    if query_id is not None:
        data['problem_judge_record'] = ProblemJudgeRecord.objects.get(id=query_id)

    TestCaseJudgeRecord.objects.create(**data)
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_TCJR])
@require_GET
@require_item_exist(model=TestCaseJudgeRecord, field='id', item='id')
def get_test_case_judge_record(request: HttpRequest, query_id: int) -> dict:
    """
    get test case judge record
    :param request:
    :param query_id:
    :return:
    """
    info = model_to_dict(TestCaseJudgeRecord.objects.get(**{'id': query_id}))
    return success_api_response(info)


@response_wrapper
@jwt_auth(perms=[JUDGE_CHANGE_TCJR])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_test_case_judge_record)
@require_item_exist(model=TestCaseJudgeRecord, field='id', item='id')
def update_test_case_judge_record(request: HttpRequest, query_id: int) -> dict:
    """
    update test case judge record
    :param request:
    :param query_id:
    :return:
    """
    data: dict = parse_data(request)
    query = TestCaseJudgeRecord.objects.get(**{'id': query_id})

    query_id = data.get('problem_judge_record', None)
    if query_id is not None:
        query.problem = ProblemJudgeRecord.objects.get(id=query_id)
        del data['problem_judge_record']
    query_id = data.get('test_case', None)
    if query_id is not None:
        query.attachment = TestCase.objects.get(id=query_id)
        del data['test_case']
    try:
        for key in data.keys():
            if data[key] is None:
                continue
            setattr(query, key, data[key])
        query.save()
    except TypeError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
    except ValidationError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[JUDGE_DELETE_TCJR])
@require_http_methods(['DELETE'])
@require_item_exist(model=TestCaseJudgeRecord, field='id', item='id')
def delete_test_case_judge_record(request: HttpRequest, query_id: int) -> dict:
    """
    delete test case judge record
    :param request:
    :param query_id:
    :return:
    """
    model = TestCaseJudgeRecord.objects.get(**{'id': query_id})
    info = model_to_dict(model)
    model.delete()
    return success_api_response(info)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_TCJR])
@require_GET
@query_filter(fields=[("judge_result", int)])
@query_distinct(fields=["judge_result", ], model=TestCaseJudgeRecord)
@query_order_by(fields=["started_at", "finished_at"])
@query_page(default=10)
def list_test_case_judge_records(request: HttpRequest, *args, **kwargs):
    """
    list test case judge records
    :param request:
    :param args:
    :param kwargs:
    :return:
    """
    models_all = TestCaseJudgeRecord.objects.count()
    models: QuerySet = TestCaseJudgeRecord.objects.all()
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")

    # order by
    order_by = kwargs.get('order_by')
    if order_by is not None:
        models = models.order_by(*order_by)
    else:
        models = models.order_by('-id')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'problem_judge_record', 'test_case',
                'judge_result', 'raw_output', 'comment', 'started_at', 'finished_at'
            )
        )
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


TEST_CASE_JUDGE_RECORD_SET_API = wrapped_api({
    "post": create_test_case_judge_record,
    "get": list_test_case_judge_records,
})

TEST_CASE_JUDGE_RECORD_DETAIL_API = wrapped_api({
    "get": get_test_case_judge_record,
    "put": update_test_case_judge_record,
    "delete": delete_test_case_judge_record,
})
