"""
Student model related interface
"""
from typing import List
from collections import defaultdict

from core.models.student import Student


def map_student_ids_to_department(student_ids: List[int]):
    """
    将学号映射到其所属院系ID
    """
    students = Student.objects.filter(student_id__in=student_ids).values_list('department', 'student_id')
    result = defaultdict(list)
    for department, student_id in students:
        result[department].append(student_id)
    return dict(result)
