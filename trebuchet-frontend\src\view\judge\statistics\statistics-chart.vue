<template>
  <div ref="dom" className="charts chart-plot" />
</template>

<script>
import * as echarts from 'echarts'
import { on, off } from '@/libs/tools'
import tdTheme from '@/assets/chart.json'

echarts.registerTheme('tdTheme', tdTheme)

export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    text: {
      type: String,
      default: ''
    },
    subtext: {
      type: String,
      default: ''
    },
    animation: {
      type: Boolean,
      default: true
    },
    valueLength: {
      type: Number,
      default: 0
    },
    startTime: {
      type: String,
      default: ''
    },
    timeDelta: {
      type: Number,
      default: 60
    },
    lineFormatter: {
      type: Function,
      default: (item) => `${item.seriesName}: ${item.data}<br/>`
    }
  },
  data() {
    return {
      dom: null
    }
  },
  watch: {
    value() {
      this.refresh()
    },
    startTime() {
      this.refresh()
    }
  },
  mounted() {
    this.dom = echarts.init(this.$refs.dom, 'tdTheme')
    if (this.value && this.startTime) {
      this.refresh()
    }
  },
  beforeDestroy() {
    off(window, 'resize', this.resize)
  },
  methods: {
    resize() {
      this.dom.resize()
    },
    refresh() {
      this.dom.clear()
      this.dom.setOption(this.chartOption())
      on(window, 'resize', this.resize)
    },
    getTimeData() {
      const timeData = []
      let time = new Date(this.startTime).getTime()
      for (let i = 1; i <= this.valueLength; i++) {
        time += this.timeDelta
        const now = new Date(time)
        if (this.timeDelta <= 60000) {
          const m = now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes()
          const h = now.getHours() < 10 ? '0' + now.getHours() : now.getHours()
          timeData.push(`${h}:${m}`)
        } else {
          const M = now.getMonth() + 1 < 10 ? '0' + (now.getMonth() + 1) : now.getMonth() + 1
          const D = now.getDate() < 10 ? '0' + now.getDate() : now.getDate()
          timeData.push(`${M}-${D}`)
        }
      }
      return timeData
    },
    chartOption() {
      let legendData = []
      this.value.map((data) => {
        if (typeof data.name === 'undefined') {
          data.name = 'NoName'
        }
        legendData.push(data.name)
      })
      return {
        animation: this.animation,
        title: {
          text: this.text,
          subtext: this.subtext,
          top: 0
        },
        legend: {
          data: legendData,
          top: 30,
          type: 'scroll'
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = ''
            params.sort((a, b) => {
              return b.seriesName > a.seriesName ? 1 : b.seriesName < a.style ? -1 : 0
            })
            if (params.length > 0) {
              result += `时间: ${params[0].axisValue}<br/>`
            }
            params.forEach((item, index) => {
              result += this.lineFormatter(item, index, params)
            })
            return result
          }
        },
        xAxis: {
          type: 'category',
          data: this.getTimeData(),
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            interval: 'auto',
            formatter: '{value}'
          }
        },
        series: this.value.map((item) => ({ ...item, smooth: true }))
      }
    }
  }
}
</script>
