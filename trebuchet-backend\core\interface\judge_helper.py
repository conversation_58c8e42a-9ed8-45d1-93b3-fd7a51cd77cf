"""
get active exam and find project in exam and find problem in exam
"""
from collections import defaultdict

from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q

from core.interface.student import map_student_ids_to_department
from core.interface.student_progress import query_student_passed
from core.models import ProjectInExam
from core.models.course import Course
from core.models.exam import Exam
from core.models.exam_record import ExamRecord
from core.models.instructor_class import InstructorClass
from core.models.student import Student


def get_total_student_ids_in_project_in_exam(pie_id: int) -> list:
    """
    get total student in a project in exam
    Args:
        pie_id: pie id
    Returns: total student list
    """
    total_student = ExamRecord.objects \
        .filter(project_in_exam__id=pie_id) \
        .values_list('student__student_id', flat=True) \
        .distinct()
    return total_student


def get_projects_in_exam(exam_id: int, include_retaker: bool) -> dict:
    """
    get proejct in exam
    Args:
        exam_id:
        include_retaker:
    Returns:
    """
    pies = Exam.objects.get(pk=exam_id).projectinexam_set.all()
    course = Exam.objects.get(pk=exam_id).course
    project_list: list = []
    # all retake students in the course
    if include_retaker:
        all_retake_student = []
    else:
        all_retake_student = course.retake_students.values_list("student_id", flat=True)
    # all students expect retake students
    all_students = Student.objects.filter(Q(instructorclass__belong_to=course), ~Q(student_id__in=all_retake_student)) \
        .values_list('student_id', flat=True) \
        .distinct()
    # map students to department
    all_students_per_department = map_student_ids_to_department(all_students)

    # calculate each project in exam passing rate
    for pie in pies:
        # all students in the project in exam
        total_student = ExamRecord.objects \
            .filter(Q(project_in_exam=pie), ~Q(student__student_id__in=all_retake_student)) \
            .values_list('student__student_id', flat=True) \
            .distinct()
        # query each student passed or not by their problem judge record
        passed_student: list = []
        for student in total_student:
            try:
                if query_student_passed(student, pie.id):
                    passed_student.append(student)
            except ObjectDoesNotExist as _:
                pass
        # all passed student of this project before this exam
        all_passed_student = ExamRecord.objects.filter(Q(project_in_exam__project=pie.project), ~Q(check_result=-1)
                                                       , ~Q(student__student_id__in=all_retake_student)
                                                       , Q(project_in_exam__id__lt=pie.id)) \
            .values_list("student__student_id", flat=True). \
            distinct()
        # all passed student of this project before and include this exam
        all_passed_student = list(set(all_passed_student).union(set(passed_student)))

        # map to department
        all_passed_students_per_department = map_student_ids_to_department(all_passed_student)
        submitted_students_per_department = map_student_ids_to_department(total_student)
        passed_students_per_department = map_student_ids_to_department(passed_student)
        department_specific_count: dict = defaultdict(dict)

        for department in all_students_per_department.keys():
            department_specific_count[department]['total_student'] = len(
                submitted_students_per_department.get(department, []))
            department_specific_count[department]['passed_student'] = len(
                passed_students_per_department.get(department, []))
            department_specific_count[department]['all_student_in_course'] = len(
                all_students_per_department.get(department, []))
            department_specific_count[department]['all_passed_student_in_course'] = len(
                all_passed_students_per_department.get(department, []))
        data: dict = {
            'id': pie.id,
            'name': pie.project.name,
            'total_student': total_student.count(),
            'passed_student': len(passed_student),
            "department_specific_count": dict(department_specific_count)

        }
        project_list.append(data)
    res = {
        "data": project_list
    }
    return res


def get_problems_in_project(project_id: int) -> list:
    """
    get problem in project
    Args:
        project_id:
    Returns:
    """
    pie = ProjectInExam.objects.get(id=project_id)
    problems = list(pie.problems.all())
    return problems


def get_total_student_ids_in_course(course_code: str):
    """
    Args:
        course_code: course code
    Returns: students in course
    """
    course = Course.objects.get(code=course_code)
    classes = InstructorClass.objects.filter(belong_to=course)
    students: list = []
    for cla in classes:
        stu = list(cla.student.all().values_list('student_id', flat=True).distinct())
        for ele in stu:
            students.append(ele)
    return students
