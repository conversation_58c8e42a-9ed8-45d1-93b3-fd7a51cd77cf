<template>
  <div>
    <div style="margin-bottom: 15px">当前教室: {{ roomName || room }}</div>
    <row>
      <count-to-card title="当前时间" :init-end-val="timeStr" disable-count-to />
    </row>
    <row>
      <Col span="12">
        <count-to-card title="队列人数" :init-end-val="queueCount" disable-count-to />
      </Col>
      <Col span="12">
        <count-to-card title="队首编号" :init-end-val="queueHead" disable-count-to />
      </Col>
    </row>
  </div>
</template>

<script>
import moment from 'moment'
import { roomReqWithId } from '@/api/room'
import { getErrModalOptions } from '@/libs/util'
import { getQueueHead, queueReq } from '@/api/on-exam'
import CountToCard from '@/components/count-to-card'

export default {
  name: 'Timetable',
  components: { CountToCard },
  data() {
    return {
      room: null,
      roomName: null,
      queueCount: 0,
      queueHead: '-',
      timeDiff: 0,
      timeStr: '-',
      refreshTimer: null,
      displayTimer: null
    }
  },
  mounted() {
    const room = this.$store.getters['onClass/currentClassroom']
    if (room === null) {
      this.$Notice.info({ title: '请先在"考场信息总览"页面选择当前教室' })
    } else {
      this.update(room)
      this.room = room
      roomReqWithId('get', room)
        .then((res) => {
          this.roomName = res.data.name
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      this.displayTimer = setInterval(() => {
        this.display()
      }, 500)
      this.refreshTimer = setInterval(() => {
        this.update(room)
      }, 5000)
    }
  },
  destroyed() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    if (this.displayTimer) {
      clearInterval(this.displayTimer)
    }
  },
  methods: {
    update(room) {
      const params = { roomId: room }
      getQueueHead(room)
        .then((res) => {
          this.queueHead = res.data.location || '-'
          this.timeDiff = new moment(res.headers.date).diff(new moment()) + 500
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      queueReq(params)
        .then((res) => {
          this.queueCount = res.data['queue_num']
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    display() {
      this.timeStr = new moment().subtract(-this.timeDiff).format('HH:mm:ss')
    }
  }
}
</script>

<style lang="less" scoped>
.count-style {
  font-size: 100px;
}
</style>
