import json
from pathlib import Path

import requests
from requests import exceptions
import os
import sys

if len(sys.argv) < 3:
    print("用法: download_student_submit.py 1.json outdir")
    print("其中 1.json 是用 /courses/<id>/get-archive-json 生成的文件")
    exit()

opath = sys.argv[2]
if not os.path.exists(opath):
    os.mkdir(opath)

f = open(sys.argv[1], 'r', encoding='utf-8')

info_list = json.load(f)["list"]
f.close()

def write_file(filename, teacher, official_class, student_id, r):
    final_dir = os.path.join(opath, teacher, str(official_class), student_id)
    if not os.path.exists(final_dir):
        os.makedirs(final_dir)
    print(final_dir+'/'+filename)
    with open(os.path.join(final_dir, filename), 'wb') as target:
        target.write(r.content)

for record in info_list:
    try:
        r = requests.get(record['download_url'])
    except exceptions.Timeout as e:
        print('请求超时：'+str(e.message))
        exit()
    except exceptions.HTTPError as e:
        print('http请求错误: '+str(e.message))
        exit()
    else:
        if r.status_code != 200:
            print('请求错误：'+str(r.status_code)+','+str(r.reason))
            exit()
        else:
            if record['pass']:
                filename = str(record['problem_id']) + '_PASS.' + record['file_type']
            else:
                filename = str(record['problem_id']) + '_FAIL.' + record['file_type']

            official_class = record['official_class']
            teacher = record['teacher']
            write_file(filename, teacher, official_class, record['student_id'], r)

for dir_name in os.listdir(sys.argv[2]):
    if '.' not in dir_name:
        # 应该是一个文件夹吧
        class_dict = {} # key 为 班号, value 为 [学生数量, 文件数]
        for class_name in os.listdir(os.path.join(opath, dir_name)):
            student_cnt = 0
            file_cnt = 0
            for student_id in os.listdir(os.path.join(opath, dir_name, class_name)):
                student_cnt += 1
                file_cnt += len(os.listdir(os.path.join(opath, dir_name, class_name, student_id)))
            class_dict[class_name] = [student_cnt, file_cnt]
        # 将 class_dict 写到 txt 里面
        total_cnt = 0
        with open(os.path.join(opath, '{}.txt'.format(dir_name)), 'w', encoding='utf-8') as f:
            f.write("行政班级, 学生数量, 文件数量\n")
            for key in class_dict.keys():
                f.write("{}, {}, {}\n".format(key, class_dict[key][0], class_dict[key][1]))
                total_cnt += class_dict[key][0]
            f.write("学生人数 {}".format(total_cnt))
