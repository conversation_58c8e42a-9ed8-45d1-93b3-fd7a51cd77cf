const getButton = (h, type, text, onClick) => {
  const props = { type, size: 'small' }
  return h('Button', { props, style: { margin: '2px' }, on: { click: onClick } }, text)
}

const buttons = {
  delete: (btnText, btnType) => {
    btnText = btnText || 'Delete'
    btnType = btnType || 'error'
    return (h, params, vm) =>
      getButton(h, btnType, btnText, () => {
        // vm.delete(params.index)
        vm.$emit('on-delete', params)
      })
  },
  view: (btnText, btnType) => {
    btnText = btnText || 'View'
    btnType = btnType || 'primary'
    return (h, params, vm) =>
      getButton(h, btnType, btnText, () => {
        vm.$emit('on-view', params)
      })
  },
  changePassword: (btnText, btnType) => {
    btnText = btnText || 'Change'
    btnType = btnType || 'success'
    return (h, params, vm) =>
      getButton(h, btnType, btnText, () => {
        vm.$emit('on-change', params)
        vm.$emit('on-changePassword', params)
      })
  },
  changeEmail: (btnText, btnType) => {
    btnText = btnText || 'Change'
    btnType = btnType || 'success'
    return (h, params, vm) =>
      getButton(h, btnType, btnText, () => {
        vm.$emit('on-changeEmail', params)
      })
  }
}

export default buttons
