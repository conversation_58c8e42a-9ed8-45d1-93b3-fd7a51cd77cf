<template>
  <csv_uploader
    :expected-column-names="expectedColumnNames"
    :handle-upload="handleUpload"
    :handle-check="handleCheck"
    :alias-column-names="aliasColumnNames"
  />
</template>

<script>
import csv_uploader from '@/view/templates/csv-uploader'
import { studentListReq } from '@/api/student'

export default {
  name: 'StudentUploader',
  components: { csv_uploader },
  data() {
    return {
      expectedColumnNames: ['student_id', 'name', 'gender', 'department', 'official_class'],
      aliasColumnNames: {
        student_id: '学号',
        name: '姓名',
        gender: '性别',
        department: '部门',
        official_class: '行政班级'
      }
    }
  },
  methods: {
    handleCheck(data) {
      data = this.parseGender(data)
      return Promise.all(
        data.map(
          (item) =>
            new Promise((resolve, reject) => {
              // 当get请求失败时，请求的对象不存在，通过预检查；请求成功时，请求的对象id存在，不通过检查
              studentListReq('get', { student_id__exact: item['student_id'] })
                .then((res) => {
                  if (res['students_all'] > 0) {
                    reject(new Error(`Error: item ${item['student_id']} is existing`))
                  } else {
                    resolve()
                  }
                })
                .catch((err) => {
                  reject(err)
                })
            })
        )
      )
    },
    handleUpload(data) {
      const studentList = data.map((item) => {
        item.department = Number(item.department)
        return item
      })
      const params = { students_list: studentList }
      return studentListReq('post', params)
    },
    parseGender(data) {
      for (let index = 0; index < data.length; index++) {
        if (data[index].gender === '男') {
          data[index].gender = 1
        } else if (data[index].gender === '女') {
          data[index].gender = 2
        } else {
          data[index].gender = 0
        }
      }
      return data
    }
  }
}
</script>
