<template>
  <Col span="8" offset="8">
    <Row>
      <Card>
        <p slot="title">设置用户资料</p>
        <Form :label-width="80">
          <form-item label="全名">
            <p>{{ fullName }}</p>
          </form-item>
          <form-item label="角色">
            <p>{{ roleName }}</p>
          </form-item>
          <form-item label="当前课程">
            <Select v-model="nowCourse" @on-change="onChange">
              <Option v-for="course in authorizedCourses" :key="course.id" :value="course.id">
                {{ course.name }}
              </Option>
            </Select>
          </form-item>
          <form-item>
            <Button type="primary" @click="handleSubmit">提交</Button>
          </form-item>
        </Form>
      </Card>
    </Row>
  </Col>
</template>

<script>
import { getErrModalOptions } from '@/libs/util'
import { selfAuthorizedCoursesReq, userProfileReq } from '@/api/user'

export default {
  name: 'UserDetail',
  data() {
    return {
      id: null,
      user: {},
      authorizedCourses: [],
      nowCourse: null,
      fullName: null,
      roleName: null
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      userProfileReq('get', {})
        .then((res) => {
          this.fullName = res.data.first_name
          this.roleName = res.data.last_name
          this.id = res.data.id
          this.nowCourse = res.data.course.id
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.nowCourse = res.data.course.id
          } else {
            this.$Modal.info({
              title: '请选择当前课程'
            })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      selfAuthorizedCoursesReq('get', {}).then((res) => {
        this.authorizedCourses = res.data.courses
        this.authorizedCourses.sort((a, b) => b.id - a.id)
      })
    },
    onChange(id) {
      this.nowCourse = id
    },
    async handleSubmit() {
      try {
        await userProfileReq('put', {
          course_id: this.nowCourse
        })
        this.$Notice.success({ title: '修改成功' })
        this.$store.commit('user/setUserDefaultCourse', this.nowCourse)
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    }
  }
}
</script>
