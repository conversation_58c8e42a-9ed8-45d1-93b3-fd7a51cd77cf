"""
Seat Form
"""

from django import forms


class SeatInfo(forms.Form):
    """Deal with seat data from post/put request.

    Attributes:
        Almost the same asa Seat Model.
    """
    # Required
    room_id = forms.IntegerField(required=True)
    pos_x = forms.IntegerField(required=True)
    pos_y = forms.IntegerField(required=True)
    name = forms.CharField(max_length=25, required=True)
    # Optional
    available = forms.BooleanField(required=False)
    # we get <PERSON><PERSON><PERSON><PERSON> actually
    comment = forms.CharField(max_length=120, required=False)
