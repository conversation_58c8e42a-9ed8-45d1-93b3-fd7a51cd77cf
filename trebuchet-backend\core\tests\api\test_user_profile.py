"""
test for user profile
"""
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.test import Client, TestCase

from core.models.course import Course
from core.models.user_profile import UserProfile


class TestUserProfile(TestCase):
    """
    test user profile
    """

    def setUp(self) -> None:
        course = Course(code='123', name='123')
        course.save()
        get_user_model().objects.create_user(username="test", password="12345")
        user = get_user_model().objects.filter(username="test").first()
        permission_1 = Permission.objects.get(codename="view_userprofile")
        user.user_permissions.add(permission_1)
        user.save()
        user_prof = UserProfile(course=course, user=user)
        user_prof.save()

        self.client = Client()
        auth_token = self.client.post(
            "http://localhost:8000/api/token-auth",
            {"username": "test", "password": "12345"}).json().get("access_token")
        self.client.defaults["HTTP_AUTHORIZATION"] = "Bearer" + \
                                                     " " + auth_token

    # @mock.patch('core.api.auth.verify_jwt_token')
    def test_get(self):
        """test get"""
        # verify_jwt_token.return_value =  None,None, 'admin'
        response = self.client.get('/api/user-profile')
        print(response.json())
