<template>
  <div>
    <!-- 第一部分：考试说明和基本配置 -->
    <Row :gutter="16" style="margin-bottom: 24px">
      <Col span="24">
        <Card>
          <div slot="title" style="display: flex; align-items: center; font-weight: bold">
            <Icon
              :type="collapsed.section1 ? 'ios-arrow-forward' : 'ios-arrow-down'"
              @click="toggleCollapse('section1')"
              style="cursor: pointer; margin-right: 8px; font-size: 16px"
            />
            考试说明和基本配置
          </div>
          <Row :gutter="16" v-show="!collapsed.section1">
            <Col span="19">
              <div style="margin-bottom: 16px; color: #666; font-weight: 500">考试说明</div>
              <mavon-editor
                :ishljs="true"
                v-model="PIE.description"
                placeholder="PIE 描述在这里修改..."
                style="z-index: 1; min-height: 300px"
              />
            </Col>
            <Col span="5">
              <div style="margin-bottom: 16px; color: #666; font-weight: 500">基本配置</div>
              <Form ref="PIEUpdate" :model="PIE" :rules="PIERule" :label-width="100">
                <form-item prop="project" label="Project ID">
                  <p>{{ PIE.project }}</p>
                </form-item>
                <form-item prop="exam" label="Exam ID">
                  <p>{{ PIE.exam }}</p>
                </form-item>
                <form-item prop="begin_time" label="Begin Time">
                  <time-picker v-model="PIE.begin_time" type="time" placeholder="请选择时间" style="width: 100%" />
                </form-item>
                <form-item prop="duration" label="Duration(min)">
                  <Input v-model="PIE.duration" type="text" placeholder="请输入持续时间（分钟）" />
                </form-item>
                <form-item prop="is_open_for_students" label="对学生开放">
                  <i-switch v-model="PIE.is_open_for_students" size="large">
                    <span slot="open">开放</span>
                    <span slot="close">关闭</span>
                  </i-switch>
                </form-item>
                <form-item>
                  <Button type="primary" @click="handleSubmit('PIEUpdate')">确认修改</Button>
                </form-item>
              </Form>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>

    <!-- 第二部分：题目配置和题目预览列表 -->
    <Row :gutter="16" style="margin-bottom: 24px">
      <Col span="24">
        <Card>
          <div slot="title" style="display: flex; align-items: center; font-weight: bold">
            <Icon
              :type="collapsed.section2 ? 'ios-arrow-forward' : 'ios-arrow-down'"
              @click="toggleCollapse('section2')"
              style="cursor: pointer; margin-right: 8px; font-size: 16px"
            />
            题目配置和预览列表
          </div>
          <Row :gutter="16" v-show="!collapsed.section2">
            <Col span="15">
              <div style="margin-bottom: 16px; color: #666; font-weight: 500">题目配置</div>
              <Form ref="requirementUpdate" :model="requirement" :rules="requirementRule" :label-width="120">
                <form-item prop="problems" label="考试题目">
                  <Input v-model="requirement.problems" type="text" placeholder="请输入题目ID，用逗号分隔" />
                </form-item>
                <form-item prop="downloadProblems" label="提交下载题目">
                  <Input v-model="requirement.downloadProblems" type="text" placeholder="请输入题目ID，用逗号分隔" />
                </form-item>
                <form-item prop="requirements" label="Requirements">
                  <Input
                    v-model="requirement.requirements"
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                    placeholder="请输入要求"
                  />
                </form-item>
                <form-item prop="achieveRequirements" label="Achieve Requirements">
                  <Input
                    v-model="requirement.achieveRequirements"
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                    placeholder="请输入达成要求"
                  />
                </form-item>
                <form-item>
                  <Button type="primary" @click="handleSubmit('requirementUpdate')">确认修改</Button>
                </form-item>
              </Form>
            </Col>
            <Col span="9">
              <div style="margin-bottom: 16px; color: #666; font-weight: 500">题目预览列表</div>
              <Table :data="problemData" :columns="problemColumns" :height="400" />
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>

    <!-- 第三部分：问答题配置和问答题预览 -->
    <Row style="margin-bottom: 24px">
      <Col span="24">
        <Card>
          <div slot="title" style="display: flex; align-items: center; font-weight: bold">
            <Icon
              :type="collapsed.section3 ? 'ios-arrow-forward' : 'ios-arrow-down'"
              @click="toggleCollapse('section3')"
              style="cursor: pointer; margin-right: 8px; font-size: 16px"
            />
            问答题配置
          </div>
          <Row :gutter="16" v-show="!collapsed.section3">
            <Col span="12">
              <div style="margin-bottom: 16px; color: #666">如果修改，请传 json 格式字符串</div>
              <Form ref="questionUpdate" :model="questionForm" :rules="questionRule" :label-width="100">
                <form-item prop="questionText" label="Question">
                  <Input
                    v-model="questionForm.questionText"
                    type="textarea"
                    :autosize="{ minRows: 12, maxRows: 20 }"
                    placeholder="请输入问答题JSON"
                  />
                </form-item>
                <form-item>
                  <Button type="primary" @click="handleSubmit('questionUpdate')">确认修改</Button>
                </form-item>
              </Form>
            </Col>
            <Col span="12">
              <div style="margin-bottom: 16px; color: #666; font-weight: 500">问答题预览</div>
              <QuestionPreview :question-text="questionForm.questionText" />
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>

    <!-- 第四部分：其他功能 -->
    <Row :gutter="16">
      <Col span="12">
        <Card>
          <div slot="title" style="display: flex; align-items: center; font-weight: bold">
            <Icon
              :type="collapsed.section4 ? 'ios-arrow-forward' : 'ios-arrow-down'"
              @click="toggleCollapse('section4')"
              style="cursor: pointer; margin-right: 8px; font-size: 16px"
            />
            考试错误分析
          </div>
          <div v-show="!collapsed.section4">
            <Form ref="failAnalysisUpdate" :model="failAnalysis" :rules="failAnalysisRule" :label-width="120">
              <form-item prop="analysis" label="考试错误分析内容">
                <Input
                  v-model="failAnalysis.analysis"
                  type="textarea"
                  :autosize="{ minRows: 11, maxRows: 11 }"
                  placeholder='{
    "chart" : [
        {"name":"L1p1","value":6,"children":[...],"append":"11,6,2"},
        {"name":"L1p2","value":5,"children":[...],"append":"11,5,2"}
    ],
    "table": {
        "head": ["a", "b", "c"],
        "content": [["a1", "b2", "c1"], ["a2", "b2", "c2"], ...]
    },
    "conclusion" : ""
}'
                />
              </form-item>
              <form-item>
                <Button type="primary" @click="handleSubmit('failAnalysisUpdate')" style="margin-right: 8px">
                  确认修改
                </Button>
                <Button type="default" @click="handleDownload">考试错误信息下载</Button>
              </form-item>
            </Form>
          </div>
        </Card>
      </Col>
      <Col span="12">
        <Card>
          <div slot="title" style="display: flex; align-items: center; font-weight: bold">
            <Icon
              :type="collapsed.section4 ? 'ios-arrow-forward' : 'ios-arrow-down'"
              @click="toggleCollapse('section4')"
              style="cursor: pointer; margin-right: 8px; font-size: 16px"
            />
            发布通知
          </div>
          <div v-show="!collapsed.section4">
            <div style="margin-bottom: 16px; color: #666">若要查看或删除通知，请退回上一级考试面板</div>
            <Form ref="newsCreate" :model="news" :rules="newsRule" :label-width="100">
              <form-item prop="content" label="通知内容">
                <Input
                  v-model="news.content"
                  type="textarea"
                  :autosize="{ minRows: 6, maxRows: 10 }"
                  placeholder="请输入通知内容"
                />
              </form-item>
              <form-item prop="star">
                <Checkbox v-model="news.star">重要通知</Checkbox>
              </form-item>
              <form-item>
                <Button type="primary" @click="handleNewsPost">发布通知</Button>
              </form-item>
            </Form>
          </div>
        </Card>
      </Col>
    </Row>
    <Table ref="table" :data="[]" :columns="[]" style="display: none" />
  </div>
</template>

<script>
import {
  examProjectIdReq,
  examProjectRequireReq,
  createNewsReq,
  examQuestionReq,
  examProjectDownloadErrorInfo
} from '@/api/exam'
import { getErrModalOptions } from '@/libs/util'
import { Link } from '@/libs/render-item'
import QuestionPreview from '@/components/question-preview/QuestionPreview.vue'

export default {
  name: 'PIEDetail',
  components: {
    QuestionPreview
  },
  data() {
    return {
      // 折叠状态控制
      collapsed: {
        section1: false, // 考试说明和基本配置
        section2: false, // 题目配置和预览列表
        section3: false, // 问答题配置
        section4: false // 考试错误分析和发布通知
      },
      PIE: {},
      PIERule: {
        begin_time: [{ required: true, message: '请填写 begin time', trigger: 'blur' }],
        duration: [{ required: true, message: '请填写 duration', trigger: 'blur' }]
      },
      requirement: {
        problems: '',
        downloadProblems: '',
        requirements: '',
        achieveRequirements: null
      },
      requirementRule: {
        requirements: [{ required: true, message: '请填写 requirement', trigger: 'blur' }]
      },
      newsRule: {
        content: [{ required: true, message: '请填写通知内容', trigger: 'blur' }]
      },
      questionRule: {
        questionText: [{ required: true, message: '请填写问答题内容', trigger: 'blur' }]
      },
      failAnalysisRule: {
        analysis: [{ required: true, message: '请填写错误分析内容', trigger: 'blur' }]
      },
      questionForm: {
        questionText: null
      },
      failAnalysis: {
        analysis: null
      },
      problemData: [],
      problemColumns: [
        {
          title: 'id',
          key: 'id',
          render: (h, params) => Link(h, params.row.id, 'problem_detail')
        },
        {
          title: 'name',
          key: 'name'
        }
      ],
      news: { content: '', star: false }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    toggleCollapse(section) {
      this.collapsed[section] = !this.collapsed[section]
    },
    loadData() {
      examProjectIdReq('get', this.$route.params.id, {})
        .then((res) => {
          this.PIE = res.data
          this.PIE.duration = res.data.duration.toString()
          this.requirement.problems = res.data.problems.map((item) => item.id).toString()
          this.requirement.downloadProblems = res.data['download_problems'].map((item) => item.id).toString()
          this.problemData = res.data.problems
          this.requirement.requirements = res.data['pass_requirement']
          this.questionForm.questionText = JSON.stringify(res.data.question, null, 2)
          this.requirement.achieveRequirements = res.data['mark_requirement']
          this.failAnalysis.analysis = res.data.fail_analysis
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
    },

    getParsedQuestions() {
      try {
        if (!this.questionForm.questionText) return []
        const parsed = JSON.parse(this.questionForm.questionText)

        // 如果是数组，直接返回
        if (Array.isArray(parsed)) {
          return parsed
        }

        // 如果是单个对象，包装成数组
        if (typeof parsed === 'object' && parsed !== null) {
          return [parsed]
        }

        return []
      } catch (e) {
        return []
      }
    },
    parseList(str) {
      if (str === null || str === '') {
        return []
      } else {
        return str.split(',').map((item) => parseInt(item))
      }
    },
    update(name) {
      if (name === 'PIEUpdate') {
        return examProjectIdReq('put', this.$route.params.id, {
          begin_time: this.PIE.begin_time,
          duration: parseInt(this.PIE.duration),
          is_open_for_students: this.PIE.is_open_for_students,
          description: this.PIE.description
        })
      } else if (name === 'questionUpdate') {
        return examQuestionReq('put', this.$route.params.id, {
          question: this.questionForm.questionText
        })
      } else if (name === 'failAnalysisUpdate') {
        return examProjectIdReq('put', this.$route.params.id, {
          fail_analysis: this.failAnalysis.analysis
        })
      } else {
        return examProjectRequireReq('put', this.$route.params.id, {
          problems: this.parseList(this.requirement.problems),
          downloadProblems: this.parseList(this.requirement.downloadProblems),
          requirement: this.requirement.requirements,
          achieveRequirement:
            this.requirement.achieveRequirements === ''
              ? this.requirement.requirements
              : this.requirement.achieveRequirements
        })
      }
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.update(name)
            .then(() => {
              this.$Notice.success({ title: '修改成功' })
              this.loadData()
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    },
    handleNewsPost() {
      this.$refs['newsCreate'].validate(async (valid) => {
        if (valid) {
          try {
            await createNewsReq({
              pie_id: this.$route.params.id,
              exam_id: null,
              active: true,
              content: this.news.content,
              star: this.news.star
            })
            this.$Notice.success({ title: '发布成功' })
            this.news.content = ''
          } catch (e) {
            this.$Modal.error(getErrModalOptions(e))
          }
        }
      })
    },
    handleDownload() {
      examProjectDownloadErrorInfo('get', this.$route.params.id, {})
        .then((res) => {
          const csvData = res.data.data.split('\n')
          csvData.shift()
          csvData.pop()
          this.$refs.table.exportCsv({
            filename: res.data.filename,
            columns: [
              { key: 'student_id', title: '学号' },
              { key: 'testcase_id', title: '测试点' },
              { key: 'comment', title: '评测反馈' },
              { key: 'submit_time', title: '提交时间' }
            ],
            data: csvData.map((item) => {
              const regex = /(?:^|,)(?:"([^"]*(?:""[^"]*)*)"|([^,]*))/g
              const matches = []
              let match

              while ((match = regex.exec(item + ','))) {
                matches.push(match[1] !== undefined ? match[1].replace(/""/g, '"') : match[2])
              }

              const [, student_id, testcase_id, comment, submit_time] = matches

              let processedComment = ''
              if (comment) {
                processedComment = comment
                  .replace(/\n/g, '\\n')
                  .replace(/\r/g, '\\r')
                  .replace(/"/g, '\\"')
                  .replace(/\t/g, '\\t')
                  .replace(/,/g, '\\,')
              }

              return {
                student_id,
                testcase_id,
                comment: processedComment,
                submit_time
              }
            })
          })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    }
  }
}
</script>
