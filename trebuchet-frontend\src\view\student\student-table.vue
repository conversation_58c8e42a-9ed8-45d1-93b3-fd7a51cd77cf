<template>
  <div>
    <Card>
      <filter-table
        ref="table"
        :data="tableData"
        :columns="column"
        :default-filter="
          this.$store.state.app.tableFilter.studentTable ? this.$store.state.app.tableFilter.studentTable : {}
        "
        :handles="handles"
        @on-view="handleView"
        @on-search="onSearch"
        @on-delete="handleDelete"
        @on-changePassword="handleChangePassword"
        @on-changeEmail="handleChangeEmail"
      />
      <Button style="margin: 10px 0" type="primary" @click="exportExcel">导出为 CSV 文件</Button>
      <Button style="margin: 10px 20px" type="primary" @click="addStudent">添加学生</Button>
      <div style="margin: 10px; overflow: hidden">
        <div style="float: right; margin: 10px; overflow: hidden">
          <Page
            :total="totalCnt"
            :current="curPage"
            :page-size="pageSize"
            show-elevator
            show-total
            @on-change="changePage"
          >
            <p>总数{{ allRecords }}条，当前已筛选出来{{ totalCnt }}条</p>
          </Page>
        </div>
      </div>
    </Card>
    <Modal v-model="deleteConfirmVisible" title="确认删除" @on-ok="deleteAfterConfirm">
      {{ deleteConfirmData }}
    </Modal>
    <Modal
      v-if="currentChangeStudent !== null"
      v-model="showChangePasswordModal"
      title="修改学生密码"
      @on-ok="handleChangeStudentPassword"
    >
      <Form :label-width="80">
        <FormItem label="姓名">
          {{ currentChangeStudent.name }}
        </FormItem>
        <FormItem label="学号">
          {{ currentChangeStudent.student_id }}
        </FormItem>
        <FormItem label="新密码" prop="password">
          <Input v-model="studentNewPassword" />
        </FormItem>
      </Form>
    </Modal>
    <Modal
      v-if="currentChangeStudent !== null"
      v-model="showChangeEmailModal"
      title="修改学生邮箱"
      @on-ok="handleChangeStudentEmail"
    >
      <Form :label-width="80">
        <FormItem label="姓名">
          {{ currentChangeStudent.name }}
        </FormItem>
        <FormItem label="学号">
          {{ currentChangeStudent.student_id }}
        </FormItem>
        <FormItem label="新邮箱" prop="email">
          <Input v-model="studentNewEmail" />
        </FormItem>
      </Form>
    </Modal>
    <Modal v-model="addStudentModal" title="添加单个学生" @on-ok="handleAddStudent">
      <Form ref="addStudent" :label-width="80" :rules="studentRule" :model="studentToAdd">
        <FormItem label="学号" prop="student_id">
          <Input v-model="studentToAdd.student_id" />
        </FormItem>
        <FormItem label="姓名" prop="name">
          <Input v-model="studentToAdd.name" />
        </FormItem>
        <FormItem label="性别" prop="gender">
          <Radio-group v-model="studentToAdd.gender" type="button">
            <Radio :label="1">男生</Radio>
            <Radio :label="2">女生</Radio>
          </Radio-group>
        </FormItem>
        <FormItem label="学院" prop="department">
          <Input v-model="studentToAdd.department" />
        </FormItem>
        <FormItem label="行政班级" prop="official_class">
          <Input v-model="studentToAdd.official_class" />
        </FormItem>
        <FormItem label="邮箱" prop="email">
          <Input v-model="studentToAdd.email" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
import FilterTable from '@/view/filter-table/filter-table'
import { getErrModalOptions } from '@/libs/util'
import { studentListReq, studentReq } from '@/api/student'
import { examRecordReq } from '@/api/exam-record'
import _ from 'lodash'
import { WhitePre } from '@/libs/render-item'

export default {
  name: 'StudentTable2',
  components: { FilterTable },
  data() {
    return {
      handles: [
        { handle: 'view', text: '查看检查记录' },
        { handle: 'delete', text: '删除' },
        { handle: 'changePassword', text: '修改密码' },
        { handle: 'changeEmail', text: '修改邮箱' }
      ],
      column: [
        { title: 'id', key: 'id', width: 100, sortable: true },
        {
          title: '学号',
          key: 'student_id',
          width: 125,
          sortable: true,
          filter: { type: 'Input' },
          render: (h, params) => WhitePre(h, params.row['student_id'])
        },
        {
          title: '姓名',
          key: 'name',
          width: 150,
          sortable: true,
          filter: { type: 'Input' },
          render: (h, params) => WhitePre(h, params.row.name)
        },
        {
          title: '性别',
          key: 'gender',
          width: 125,
          sortable: true,
          filter: {
            type: 'Select',
            option: {
              0: {
                name: '未知',
                value: 0,
                color: 'gray'
              },
              1: {
                name: '男',
                value: 1,
                color: 'blue'
              },
              2: {
                name: '女',
                value: 2,
                color: 'red'
              }
            }
          }
        },
        { title: '行政班级', key: 'official_class', width: 125, sortable: true, filter: { type: 'Input' } },
        {
          title: '学院',
          key: 'department',
          width: 125,
          sortable: true,
          editable: true,
          filter: {
            type: 'Input'
          }
        },
        { title: '邮箱', key: 'email', width: 200, sortable: true, editable: true, filter: { type: 'Input' } }
      ],
      tableData: [],
      filter: {},
      totalCnt: 0,
      pageSize: 15,
      curPage: 1,
      allRecords: 0,
      deleteConfirmVisible: false,
      deleteConfirmData: {},
      addStudentModal: false,
      studentToAdd: {
        student_id: '',
        name: '',
        gender: '',
        department: '',
        official_class: ''
      },
      studentRule: {
        student_id: [{ required: true, message: '请填写学号', trigger: 'blur' }],
        name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
        department: [{ required: true, message: '请填写学院', trigger: 'blur' }],
        official_class: [{ required: true, message: '请填写班级', trigger: 'blur' }]
      },
      showChangePasswordModal: false,
      showChangeEmailModal: false,
      currentChangeStudent: null,
      studentNewPassword: ''
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.studentTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.studentTable)
    }
    this.curPage = this.$store.state.app.tablePage.studentTable ? this.$store.state.app.tablePage.studentTable : 1
    this.loadStudents()
  },
  methods: {
    async loadStudents() {
      try {
        let params = {
          page: this.curPage,
          page_size: this.pageSize,
          ...this.filter
        }
        let res = await studentListReq('get', params)
        this.tableData = this.parseGender(res.data['students'])
        this.totalCnt = res.data['total_count']
        this.curPage = res.data['page_now']
        this.allRecords = res.data['students_all']
        this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'studentTable' })
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    parseGender(data) {
      for (let index = 0; index < data.length; index++) {
        if (data[index].gender === 1) {
          data[index].gender = '男'
        } else if (data[index].gender === 2) {
          data[index].gender = '女'
        } else {
          data[index].gender = '未知'
        }
      }
      return data
    },
    changePage(index) {
      this.curPage = index
      this.loadStudents()
    },
    onSearch(search) {
      try {
        search = this.refactorSearchObject(search)
        this.curPage = 1
        this.loadStudents()
        this.$store.commit('setTableFilter', { filter: search, name: 'studentTable' })
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {}
      Object.keys(search).forEach((key) => {
        if (key === 'department' || key === 'gender') {
          this.filter[key + '__exact'] = search[key]
        } else {
          this.filter[key + '__contains'] = search[key]
        }
      })
      return searchNew
    },
    handleDelete(data) {
      this.deleteConfirmData = this.tableData[data.index]
      this.deleteConfirmVisible = true
    },
    handleChangePassword(data) {
      this.currentChangeStudent = this.tableData[data.index]
      this.showChangePasswordModal = true
    },
    handleChangeEmail(data) {
      this.currentChangeStudent = this.tableData[data.index]
      this.showChangeEmailModal = true
    },
    async deleteAfterConfirm() {
      if (this.deleteConfirmData !== null) {
        try {
          await studentReq('delete', this.deleteConfirmData.student_id)
          this.$Notice.success({ title: '删除成功' })
          await this.loadStudents()
        } catch (e) {
          this.$Modal.error(getErrModalOptions(e))
        }
      }
    },
    async handleView(data) {
      try {
        let res = await examRecordReq('get', {
          student_id__contains: data.row.student_id,
          page_size: 500,
          order_by: '-project_in_exam__exam__date'
        })
        if (res.data.models.length === 0) {
          this.$Notice.info({ title: '该学生无检查记录' })
        } else {
          await this.$router.push({ name: 'check_exam_record', params: { id: res.data.models[0].id } })
        }
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    async exportExcel() {
      let params = {
        ...this.filter
      }
      let res = await studentListReq('get', params)
      const data = this.parseGender(res.data.students)
      this.$refs['table'].exportCsv({
        filename: 'studentsTable.csv',
        columns: this.column,
        data: data
      })
    },
    addStudent() {
      this.addStudentModal = true
    },
    async handleAddStudent() {
      try {
        if (this.studentToAdd.gender !== 1 && this.studentToAdd.gender !== 2) {
          this.studentToAdd.gender = 0
        }
        this.$refs['addStudent'].validate(async (valid) => {
          if (valid) {
            await studentListReq('post', { students_list: [{ ...this.studentToAdd }] })
            this.$Notice.success({ title: '添加成功' })
          } else {
            this.$Notice.warning({ title: '信息填写不全' })
          }
        })
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    async handleChangeStudentPassword() {
      if (this.currentChangeStudent !== null) {
        try {
          await studentReq('put', this.currentChangeStudent.student_id, { password: this.studentNewPassword })
          this.studentNewPassword = ''
        } catch (e) {
          this.$Modal.error(getErrModalOptions(e))
        }
      }
    },
    async handleChangeStudentEmail() {
      if (this.currentChangeStudent !== null) {
        try {
          await studentReq('put', this.currentChangeStudent.student_id, { email: this.studentNewEmail })
          this.studentNewEmail = ''
        } catch (e) {
          this.$Modal.error(getErrModalOptions(e))
        }
      }
    }
  }
}
</script>
