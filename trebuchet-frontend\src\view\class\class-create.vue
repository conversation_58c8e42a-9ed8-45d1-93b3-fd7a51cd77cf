<template>
  <Row>
    <Col span="8" offset="6">
      <Card>
        <Form ref="classNew" :model="classNew" :rules="classRule" :label-width="80">
          <form-item prop="name" label="班级名称">
            <Input v-model="classNew.name" type="text" />
          </form-item>
          <form-item prop="teacher" label="教师">
            <Input v-model="classNew.teacher" type="text" />
          </form-item>
          <form-item label="学生列表">
            <div style="display: flex; align-items: center">
              <Upload :before-upload="beforeUpload" action="">
                <Button icon="ios-cloud-upload-outline">上传 CSV 文件</Button>
              </Upload>
              <label style="margin-left: 10px">文件列名: [ 学号 ]</label>
            </div>
            <strong>
              <span style="font-size: small">请确保 CSV 文件的编码格式为 UTF-8</span>
            </strong>
            <br />
            <div v-if="fileUploadStatus" style="margin-top: 10px">
              <Icon v-if="fileUploadStatus === 'uploading'" type="ios-loading" class="demo-spin-icon-load" />
              <Icon v-if="fileUploadStatus === 'success'" type="ios-checkmark-circle" color="#19be6b" />
              <Icon v-if="fileUploadStatus === 'error'" type="ios-close-circle" color="#ed4014" />
              <span style="margin-left: 5px">{{ fileUploadMessage }}</span>
            </div>
          </form-item>
          <form-item>
            <Button
              :disabled="!uploadFileReady || classNew.belong_to === -1"
              type="primary"
              @click="handleSubmit('classNew')"
            >
              确认创建
            </Button>
          </form-item>
        </Form>
      </Card>
    </Col>
  </Row>
</template>

<script>
import { getArrayFromFile, getErrModalOptions, getTableDataFromArray } from '@/libs/util'
import { classReq } from '@/api/class'
import { userProfileReq } from '@/api/user'
export default {
  name: 'ClassCreate',
  data() {
    return {
      classNew: {
        name: '',
        teacher: '',
        student: [],
        belong_to: this.$store.state.user.userDefaultCourse
      },
      classRule: {
        name: [
          { required: true, message: '请填写班级名称', trigger: 'blur' },
          { validator: this.validateClassName, trigger: 'blur' }
        ],
        teacher: [{ required: true, message: '请填写教师名', trigger: 'blur' }]
      },
      columns: [],
      uploadBtnMsg: '确认上传',
      expectedColumnNames: ['学号'],
      fileUploadStatus: null, // 'uploading', 'success', 'error'
      fileUploadMessage: ''
    }
  },
  computed: {
    columnNames() {
      return this.columns.map((item) => item.title)
    },
    uploadFileReady() {
      if (!this.columnNames || this.columnNames.length !== this.expectedColumnNames.length) {
        return false
      }
      return this.columnNames.every((item, index) => item === this.expectedColumnNames[index])
    }
  },
  mounted() {
    if (this.$store.state.user.userDefaultCourse === -1) {
      // reload
      userProfileReq('get')
        .then((res) => {
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
            this.classNew.belong_to = res.data.course.id
          } else {
            this.$Modal.info({
              title: '请在课程信息/课程总览选择当前课程'
            })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    }
  },
  methods: {
    validateClassName(rule, value, callback) {
      if (value === '') {
        return callback()
      }
      // 检查班级名是否重复（全局检查，与后端逻辑保持一致）
      classReq('get', {
        name__exact: value
      })
        .then((res) => {
          if (res.data.data && res.data.data.length > 0) {
            callback(new Error('班级名称已存在，请使用其他名称'))
          } else {
            callback()
          }
        })
        .catch(() => {
          // 如果请求失败，允许通过验证（可能是网络问题）
          callback()
        })
    },
    async beforeUpload(file) {
      // 显示上传中状态
      this.fileUploadStatus = 'uploading'
      this.fileUploadMessage = '正在解析文件...'

      try {
        const data = await getArrayFromFile(file)
        const { columns, tableData } = getTableDataFromArray(data)
        this.classNew.student = tableData.map((data) => data['学号'])
        this.columns = columns

        // 文件解析成功
        this.fileUploadStatus = 'success'
        this.fileUploadMessage = `文件解析成功，共读取 ${tableData.length} 条学生记录`
      } catch (err) {
        this.fileUploadStatus = 'error'
        this.fileUploadMessage = '文件解析失败：只能上传 CSV 文件'
        this.$Notice.warning({ title: '只能上传 CSV 文件' })
      }

      if (!this.uploadFileReady) {
        this.uploadBtnMsg = '格式不符'
        this.fileUploadStatus = 'error'
        this.fileUploadMessage = '文件格式不符合要求'
        this.$Notice.warning({ title: '格式不符' })
      } else if (this.classNew.belong_to === -1) {
        this.uploadBtnMsg = '默认课程为空'
        this.fileUploadStatus = 'error'
        this.fileUploadMessage = '默认课程为空，请先选择课程'
        this.$Notice.warning({ title: '默认课程为空' })
      } else {
        this.uploadBtnMsg = '确认上传'
        if (this.fileUploadStatus !== 'error') {
          this.fileUploadStatus = 'success'
          this.fileUploadMessage = `文件上传成功，共 ${this.classNew.student.length} 名学生`
        }
      }
    },
    upload(data) {
      return classReq('post', data)
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.upload({
            name: this.classNew.name,
            teacher: this.classNew.teacher,
            student: this.classNew.student,
            belong_to: this.$store.state.user.userDefaultCourse
          })
            .then(() => {
              this.$Notice.success({ title: '创建成功' })
              this.$router.push({ name: 'class_table' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    }
  }
}
</script>

<style scoped>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
