"""
declare Discussion model
"""
from django.db import models
from django.contrib.auth import get_user_model

from core.models.course import Course
from core.models.permissions import (DISCUSSION_CREATE, DISCUSSION_DELETE, DISCUSSION_CHANGE, DISCUSSION_VIEW)
from judge.models import Problem

PRIORITY_TYPE_MEANINGLESS = 0
PRIORITY_TYPE_COMMON = 1
PRIORITY_TYPE_ESSENCE = 2
PRIORITY_TYPE_TOP = 3


class Discussion(models.Model):
    PRIORITY_TYPE = [
        (PRIORITY_TYPE_MEANINGLESS, "无意义"),
        (PRIORITY_TYPE_COMMON, "普通"),
        (PRIORITY_TYPE_ESSENCE, "精华"),
        (PRIORITY_TYPE_TOP, "置顶")
    ]

    author = models.ForeignKey(get_user_model(), on_delete=models.RESTRICT, null=False, db_index=True)

    title = models.CharField(max_length=50, null=False)
    priority = models.IntegerField(choices=PRIORITY_TYPE, default=PRIORITY_TYPE_COMMON, null=False, db_index=True)
    closed = models.BooleanField(default=False, null=False)

    course = models.ForeignKey(Course, on_delete=models.CASCADE, null=False, db_index=True)
    problem = models.ForeignKey(Problem, on_delete=models.CASCADE, null=True, db_index=True)

    created_at = models.DateTimeField(blank=False, null=False, auto_now_add=True)
    updated_at = models.DateTimeField(blank=False, null=False, auto_now=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (DISCUSSION_CHANGE, DISCUSSION_CHANGE),
            (DISCUSSION_CREATE, DISCUSSION_CREATE),
            (DISCUSSION_DELETE, DISCUSSION_DELETE),
            (DISCUSSION_VIEW, DISCUSSION_VIEW)
        ]
