{"ast": null, "code": "import Main from '@/view/index/main';\nexport const examRouter = {\n  path: '/exam',\n  name: 'exam',\n  component: Main,\n  meta: {\n    title: '考试管理',\n    icon: 'ios-paper',\n    jumpRoute: '/exam/exam-table'\n  },\n  children: [{\n    path: 'exam-table',\n    name: 'exam_table',\n    meta: {\n      title: '考试列表'\n    },\n    component: () => import('@/view/exam/exam/exam-table')\n  }, {\n    path: 'exam-create',\n    name: 'exam_create',\n    meta: {\n      title: '考试创建',\n      hideInMenu: true\n    },\n    component: () => import('@/view/exam/exam/exam-create')\n  }, {\n    path: 'exam-detail/:id',\n    name: 'exam_detail',\n    meta: {\n      title: '考试详细信息',\n      hideInMenu: true\n    },\n    component: () => import('@/view/exam/exam/exam-detail')\n  }, {\n    path: 'progress-push',\n    name: 'progress_push',\n    meta: {\n      title: '进度推进'\n    },\n    component: () => import('@/view/exam/progress/progress-push')\n  }, {\n    path: 'project-tree',\n    name: 'project_tree',\n    meta: {\n      title: '项目管理'\n    },\n    component: () => import('@/view/exam/project/project-tree')\n  }, {\n    path: 'project-detail/:id',\n    name: 'project_detail',\n    meta: {\n      title: '项目创建',\n      hideInMenu: true\n    },\n    component: () => import('@/view/exam/project/project-detail')\n  }, {\n    path: 'project-in-exam/:id',\n    name: 'project_in_exam',\n    meta: {\n      title: 'PIE 详情',\n      hideInMenu: true\n    },\n    component: () => import('@/view/exam/project/project-in-exam')\n  }, {\n    path: 'student-achieve',\n    name: 'student_achieve',\n    meta: {\n      title: '学生成绩成就'\n    },\n    component: () => import('@/view/exam/achieve/student-achieve')\n  }, {\n    path: 'class-achieve',\n    name: 'class_achieve',\n    meta: {\n      title: '班级成就图表'\n    },\n    component: () => import('@/view/exam/achieve/class-achieve')\n  }, {\n    path: 'fail-analysis',\n    name: 'fail_analysis',\n    meta: {\n      title: '错误分析'\n    },\n    component: () => import('@/view/exam/analysis/fail-analysis')\n  }, {\n    path: 'progress-detection',\n    name: 'progress_detection',\n    meta: {\n      title: '进度检测'\n    },\n    component: () => import('@/view/exam/progress/progress-detection')\n  }]\n};", "map": {"version": 3, "names": ["Main", "examRouter", "path", "name", "component", "meta", "title", "icon", "jumpRoute", "children", "hideInMenu"], "sources": ["E:/CO/助教/dev projects/trebuchet-frontend/src/view/exam/router.js"], "sourcesContent": ["import Main from '@/view/index/main'\r\n\r\nexport const examRouter = {\r\n  path: '/exam',\r\n  name: 'exam',\r\n  component: Main,\r\n  meta: {\r\n    title: '考试管理',\r\n    icon: 'ios-paper',\r\n    jumpRoute: '/exam/exam-table'\r\n  },\r\n  children: [\r\n    {\r\n      path: 'exam-table',\r\n      name: 'exam_table',\r\n      meta: {\r\n        title: '考试列表'\r\n      },\r\n      component: () => import('@/view/exam/exam/exam-table')\r\n    },\r\n    {\r\n      path: 'exam-create',\r\n      name: 'exam_create',\r\n      meta: {\r\n        title: '考试创建',\r\n        hideInMenu: true\r\n      },\r\n      component: () => import('@/view/exam/exam/exam-create')\r\n    },\r\n    {\r\n      path: 'exam-detail/:id',\r\n      name: 'exam_detail',\r\n      meta: {\r\n        title: '考试详细信息',\r\n        hideInMenu: true\r\n      },\r\n      component: () => import('@/view/exam/exam/exam-detail')\r\n    },\r\n    {\r\n      path: 'progress-push',\r\n      name: 'progress_push',\r\n      meta: {\r\n        title: '进度推进'\r\n      },\r\n      component: () => import('@/view/exam/progress/progress-push')\r\n    },\r\n    {\r\n      path: 'project-tree',\r\n      name: 'project_tree',\r\n      meta: {\r\n        title: '项目管理'\r\n      },\r\n      component: () => import('@/view/exam/project/project-tree')\r\n    },\r\n    {\r\n      path: 'project-detail/:id',\r\n      name: 'project_detail',\r\n      meta: {\r\n        title: '项目创建',\r\n        hideInMenu: true\r\n      },\r\n      component: () => import('@/view/exam/project/project-detail')\r\n    },\r\n    {\r\n      path: 'project-in-exam/:id',\r\n      name: 'project_in_exam',\r\n      meta: {\r\n        title: 'PIE 详情',\r\n        hideInMenu: true\r\n      },\r\n      component: () => import('@/view/exam/project/project-in-exam')\r\n    },\r\n    {\r\n      path: 'student-achieve',\r\n      name: 'student_achieve',\r\n      meta: {\r\n        title: '学生成绩成就'\r\n      },\r\n      component: () => import('@/view/exam/achieve/student-achieve')\r\n    },\r\n    {\r\n      path: 'class-achieve',\r\n      name: 'class_achieve',\r\n      meta: {\r\n        title: '班级成就图表'\r\n      },\r\n      component: () => import('@/view/exam/achieve/class-achieve')\r\n    },\r\n    {\r\n      path: 'fail-analysis',\r\n      name: 'fail_analysis',\r\n      meta: {\r\n        title: '错误分析'\r\n      },\r\n      component: () => import('@/view/exam/analysis/fail-analysis')\r\n    },\r\n    {\r\n      path: 'progress-detection',\r\n      name: 'progress_detection',\r\n      meta: {\r\n        title: '进度检测'\r\n      },\r\n      component: () => import('@/view/exam/progress/progress-detection')\r\n    }\r\n  ]\r\n}\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,mBAAmB;AAEpC,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEJ,IAAI;EACfK,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,YAAY;IAClBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,6BAA6B;EACvD,CAAC,EACD;IACEF,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbI,UAAU,EAAE;IACd,CAAC;IACDN,SAAS,EAAE,MAAM,MAAM,CAAC,8BAA8B;EACxD,CAAC,EACD;IACEF,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE;MACJC,KAAK,EAAE,QAAQ;MACfI,UAAU,EAAE;IACd,CAAC;IACDN,SAAS,EAAE,MAAM,MAAM,CAAC,8BAA8B;EACxD,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,oCAAoC;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,kCAAkC;EAC5D,CAAC,EACD;IACEF,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,gBAAgB;IACtBE,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbI,UAAU,EAAE;IACd,CAAC;IACDN,SAAS,EAAE,MAAM,MAAM,CAAC,oCAAoC;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE;MACJC,KAAK,EAAE,QAAQ;MACfI,UAAU,EAAE;IACd,CAAC;IACDN,SAAS,EAAE,MAAM,MAAM,CAAC,qCAAqC;EAC/D,CAAC,EACD;IACEF,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,qCAAqC;EAC/D,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,mCAAmC;EAC7D,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,eAAe;IACrBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,oCAAoC;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,oBAAoB;IAC1BE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,yCAAyC;EACnE,CAAC;AAEL,CAAC"}, "metadata": {}, "sourceType": "module"}