"""[summary]
"""

from django.db.models import ObjectDoesNotExist, Q

from core.api.student_progress import parse_requirement
from core.models.project_in_exam import ProjectInExam
from core.models.student_progress import StudentProgress


def query_student_passed(username: str, project_in_exam_id: int):
    """query exam problem pass status of a specified student
    """
    project_in_exam = ProjectInExam.objects.get(pk=project_in_exam_id)
    requirement = project_in_exam.pass_requirement

    course_id = project_in_exam.project.course.id
    progress_filter = Q(**{"course__id__exact": course_id}) \
                      & Q(**{"student__student_id__exact": username})
    progress = StudentProgress.objects.filter(progress_filter).first()
    # 若无 progress，说明 progress 发生了问题，直接报异常
    if not progress:
        raise ObjectDoesNotExist
    qualified = progress.qualified
    # 若是当前的 PIE，直接使用并修改即可
    if progress.current_project == project_in_exam:
        if not qualified:
            qualified = parse_requirement(requirement, username, project_in_exam_id)
            if qualified:
                progress.qualified = qualified
                progress.save()
        return qualified
    # 若 PIE 不是当前 PIE，无法通过 progress 直接查询，重新计算
    # 这种情况只出现于查看过去的考试，不需要关注性能
    return parse_requirement(requirement, username, project_in_exam_id)
