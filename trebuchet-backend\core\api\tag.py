from django.core.exceptions import ObjectDoesNotExist
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import require_http_methods

from core.api.auth import jwt_auth
from core.api.permissions import (DISCUSSION_TAG_CREATE, DISCUSSION_TAG_VIEW,
                                  DISCUSSION_TAG_DELETE, DISCUSSION_TAG_UPDATE)
from core.api.utils import (ErrorCode, failed_api_response,
                            response_wrapper, success_api_response,
                            wrapped_api, parse_data)
from discussion.models import Tag


def validate_tag(func):
    """validate tag exist
    """

    def wrapper(request: HttpRequest, *args, **kwargs):
        tag_id = request.GET.get('tag_id', None)
        try:
            tag = Tag.objects.get(pk=tag_id)
        except ObjectDoesNotExist:
            return failed_api_response(ErrorCode.INVALID_REQUEST_ARGUMENT_ERROR, "tag_id不存在")
        return func(request, tag, *args, **kwargs)

    return wrapper


@response_wrapper
@jwt_auth(perms=[DISCUSSION_TAG_CREATE])
@require_http_methods(['PUT'])
def create_tag(request: HttpRequest, *args, **kwargs):
    """add tag

    [method]: PUT

    [route]: /api/tag
    """
    body = parse_data(request)
    name = body.get('name', None)
    priority = Tag.objects.all().count()
    tag = Tag(name=name, priority=priority)
    tag.save()
    return success_api_response({'result': 'OK, tag added',
                                 'data': {
                                     'id': tag.id,
                                     'name': tag.name,
                                     'priority': tag.priority
                                 }})


@response_wrapper
@jwt_auth(perms=[DISCUSSION_TAG_UPDATE])
@require_http_methods(['POST'])
@validate_tag
def update_tag(request: HttpRequest, tag, *args, **kwargs):
    """update tag

    [method]: POST

    [route]: /api/tag
    """
    body = parse_data(request)
    name = body.get('name', None)
    if name:
        tag.name = name
    tag.save()
    return success_api_response({'result': 'OK, tag updated',
                                 'data': {
                                     'id': tag.id,
                                     'name': tag.name,
                                     'priority': tag.priority
                                 }})


@response_wrapper
@jwt_auth(perms=[DISCUSSION_TAG_VIEW])
@require_http_methods(['GET'])
@validate_tag
def get_tag(request: HttpRequest, tag, *args, **kwargs):
    """get tag

    [method]: GET

    [route]: /api/tag
    """
    discussion_info = []
    for discussion in tag.discussion.all():
        discussion_info.append(model_to_dict(discussion))
    data = {
        'id': tag.id,
        'name': tag.name,
        'priority': tag.priority,
        'discussion': discussion_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[DISCUSSION_TAG_DELETE])
@require_http_methods(['DELETE'])
@validate_tag
def remove_tag(request: HttpRequest, tag, *args, **kwargs):
    """remove tag

    [method]: DELETE

    [route]: /api/tag
    """
    tag.delete()
    return success_api_response({'result': 'OK, tag removed'})


TAG_DETAIL_API = wrapped_api({
    "put": create_tag,
    "post": update_tag,
    "get": get_tag,
    "delete": remove_tag
})


@response_wrapper
@jwt_auth(perms=[DISCUSSION_TAG_VIEW])
@require_http_methods(['GET'])
def get_tag_list(request: HttpRequest, *args, **kwargs):
    """get tag

    [method]: GET

    [route]: /api/tag_list
    """
    tag_info = []
    for tag in Tag.objects.all().order_by("priority"):

        discussion_info = []
        for discussion in tag.discussion.all():
            discussion_info.append(model_to_dict(discussion))
        tag_info.append({
            'id': tag.id,
            'name': tag.name,
            'priority': tag.priority,
            'discussion': discussion_info
        })
    return success_api_response({"data": tag_info})


@response_wrapper
@jwt_auth(perms=[DISCUSSION_TAG_UPDATE])
@require_http_methods(['POST'])
def update_tag_list(request: HttpRequest, *args, **kwargs):
    """update tag_list priority

    [method]: POST

    [route]: /api/tag_list
    """
    body = parse_data(request)
    tag_id_list = body.get('tag_id_list', None)
    if not isinstance(tag_id_list, list):
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGUMENT_ERROR, "tag_id_list 必须是数组")
    cnt = Tag.objects.all().count()
    if len(tag_id_list) != cnt:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGUMENT_ERROR, "tag_id_list 长度不符")
    if len(set(tag_id_list)) != len(tag_id_list):
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGUMENT_ERROR, "tag_id_list 有id重复")
    for index, tag_id in enumerate(tag_id_list):
        Tag.objects.filter(id=tag_id).update(priority=index)

    tag_info = []
    for tag in Tag.objects.all().order_by("priority"):

        discussion_info = []
        for discussion in tag.discussion.all():
            discussion_info.append(model_to_dict(discussion))
        tag_info.append({
            'id': tag.id,
            'name': tag.name,
            'priority': tag.priority,
            'discussion': discussion_info
        })

    return success_api_response({'result': 'OK, tag_list ordered',
                                 "data": tag_info})


TAG_API = wrapped_api({
    "get": get_tag_list,
    "post": update_tag_list,
})
