# Generated by Django 2.2.6 on 2020-10-16 07:30

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0014_auto_20200719_1713'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='omniconfig',
            options={'default_permissions': (),
                     'permissions': [('查看所有 Omni 配置', '查看所有 Omni 配置'), ('编辑 Omni 配置', '编辑 Omni 配置')]},
        ),
        migrations.AddField(
            model_name='course',
            name='allowed_users',
            field=models.ManyToManyField(to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddConstraint(
            model_name='studentseatrecord',
            constraint=models.UniqueConstraint(fields=('exam', 'student'), name='no duplicate student in exam'),
        ),
    ]
