# Generated by Django 3.1.7 on 2021-04-21 00:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0019_auto_20210413_2043'),
        ('judge', '0009_auto_20210419_2211'),
    ]

    operations = [
        migrations.CreateModel(
            name='Discussion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50)),
                ('priority', models.IntegerField(choices=[(0, '无意义'), (1, '普通'), (2, '精华'), (3, '置顶')], default=1)),
                ('closed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                             to=settings.AUTH_USER_MODEL)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.course')),
                ('problem',
                 models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='judge.problem')),
            ],
            options={
                'permissions': [('修改讨论', '修改讨论'), ('创建讨论', '创建讨论'), ('删除讨论', '删除讨论'), ('查看讨论', '查看讨论')],
                'default_permissions': (),
            },
        ),
        migrations.CreateModel(
            name='Response',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('authority', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('discussion', models.ForeignKey(default=False, on_delete=django.db.models.deletion.CASCADE,
                                                 to='discussion.discussion')),
            ],
        ),
        migrations.CreateModel(
            name='Vote',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('response', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='discussion.response')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user_id', 'response_id')},
            },
        ),
        migrations.CreateModel(
            name='Follow',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('discussion',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='discussion.discussion')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user_id', 'discussion_id')},
            },
        ),
        migrations.CreateModel(
            name='Cite',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sourced_cites',
                                             to='discussion.response')),
                ('target', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='targetd_cites',
                                             to='discussion.response')),
            ],
            options={
                'unique_together': {('source_id', 'target_id')},
            },
        ),
    ]
