import Main from '@/view/index/main'

export const messageRouter = {
  path: '/message',
  name: 'message',
  component: Main,
  meta: {
    title: '推送消息',
    icon: 'ios-chatboxes',
    jumpRoute: '/message/push-message-table'
  },
  children: [
    {
      path: 'push-message-table',
      name: 'push_message_table',
      meta: {
        title: '推送消息列表'
      },
      component: () => import('@/view/message/push-message-table')
    },
    {
      path: 'push-message-show/:id',
      name: 'push_message_show',
      meta: {
        title: '推送消息详情',
        hideInMenu: true
      },
      component: () => import('@/view/message/push-message-show')
    },
    {
      path: 'push-message-create',
      name: 'push_message_create',
      meta: {
        title: '推送消息创建'
      },
      component: () => import('@/view/message/push-message-detail')
    },
    {
      path: 'push-message-detail/:id',
      name: 'push_message_detail',
      meta: {
        title: '推送消息修改',
        hideInMenu: true
      },
      component: () => import('@/view/message/push-message-detail')
    }
  ]
}
