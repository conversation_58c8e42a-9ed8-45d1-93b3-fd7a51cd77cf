"""
declare JudgeParameterTemplate model
"""

from django.contrib.auth import get_user_model
from django.db import models


class JudgeParameterTemplate(models.Model):
    """
    Parameters for judge process.
    """
    parameter = models.TextField()
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(get_user_model(), on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return self.description

    class Meta:
        default_permissions = ()
        permissions = []
