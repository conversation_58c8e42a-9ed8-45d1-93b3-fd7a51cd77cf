"""
declare TestCase model
"""

from django.contrib.auth import get_user_model
from django.db import models
from nose.tools import nottest

from judge.models.admin_uploaded_file import AdminUploadedFile
from judge.models.permissions import (CHANGE_TESTCASE, CREATE_TESTCASE,
                                      DELETE_TESTCASE, VIEW_TESTCASE)


@nottest
class TestCase(models.Model):
    """
    Single test point for a specific problem.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    judge_parameter = models.TextField()
    judge_data = models.ForeignKey(AdminUploadedFile, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(get_user_model(), on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return self.name

    class Meta:
        default_permissions = ()
        permissions = [
            (CHANGE_TESTCASE, CHANGE_TESTCASE),
            (CREATE_TESTCASE, CREATE_TESTCASE),
            (DELETE_TESTCASE, DELETE_TESTCASE),
            (VIEW_TESTCASE, VIEW_TESTCASE)
        ]
