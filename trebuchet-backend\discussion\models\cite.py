"""
declare Cite model
"""
from django.db import models

from discussion.models import Response


class Cite(models.Model):
    source = models.ForeignKey(
        Response, on_delete=models.CASCADE, null=False, related_name="sourced_cites", db_index=True
    )
    target = models.ForeignKey(
        Response, on_delete=models.CASCADE, null=True, related_name="targetd_cites", db_index=True
    )

    class Meta:
        unique_together = ("source_id", "target_id")
