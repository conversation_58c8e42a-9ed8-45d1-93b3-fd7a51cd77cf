import { getRequest, getRequestForFile } from '@/api/util'

export const judgeReq = (method, params) => {
  return getRequest(`/api/judge/problem_judge_record`, method, params)
}

export const judgeIdReq = (method, judgeId, params) => {
  return getRequest(`/api/judge/problem_judge_record/${judgeId}`, method, params)
}

export const judgeProblemReq = (method, params) => {
  return getRequest(`/api/judge/problem`, method, params)
}

export const judgeProblemIdReq = (method, judgeProblemId, params) => {
  return getRequest(`/api/judge/problem/${judgeProblemId}`, method, params)
}

export const testcaseReq = (method, params) => {
  return getRequest(`/api/judge/test_case`, method, params)
}

export const testcaseIdReq = (method, testcaseId, params) => {
  return getRequest(`/api/judge/test_case/${testcaseId}`, method, params)
}

export const judgeRecordFileReq = (method, judgeId, params) => {
  return getRequestForFile(`/api/judge/file/problem_judge_record/${judgeId}`, method, params)
}

export const problemFileReq = (method, problemId, params) => {
  return getRequestForFile(`/api/judge/file/problem/${problemId}`, method, params)
}

export const testcaseFileReq = (method, testcaseId, params) => {
  return getRequestForFile(`/api/judge/file/test_case/${testcaseId}`, method, params)
}

export const judgeExamStatisticsReq = (method, examId, params) => {
  return getRequest(`/api/judge/statistics_exam/${examId}`, method, params)
}

export const judgeProjectStatisticsReq = (method, projectId, params) => {
  return getRequest(`/api/judge/statistics_project/${projectId}`, method, params)
}

export const getProblemStatistics = (method, problemId, params) => {
  return getRequest(`/api/judge/statistics_problem/${problemId}`, method, params)
}

export const getChartData = (method, pieId, params) => {
  return getRequest(`/api/judge/chart/project/${pieId}`, method, params)
}

export const getProblemChartData = (pieId, pid) => {
  return getRequest(`/api/judge/chart/problem/${pieId}/${pid}`, 'get')
}

export const getProjectChartData = (pid) => {
  return getRequest(`/api/judge/chart/project_history/${pid}`, 'get')
}

export const getProjectQuestionChartData = (pid) => {
  return getRequest(`/api/judge/chart/project_history_question/${pid}`, 'get')
}

export const getProjectAnswerChartData = (pid) => {
  return getRequest(`/api/judge/chart/project_history_answer/${pid}`, 'get')
}

export const getStudentScoreData = (studentId) => {
  return getRequest(`/api/judge/chart/student-score/${studentId}`, 'get')
}

export const rejudgeReq = (id) => {
  return getRequest(`/api/judge/rejudge`, 'post', { record_id: id })
}

export const getRankReq = (pid) => {
  return getRequest(`/api/judge/rank/${pid}`, 'get')
}

export const getSubmitChartReq = (pid) => {
  return getRequest(`/api/judge/chart/project_submit/${pid}`, 'get')
}

export const batchRejudgeReq = (method, params) => {
  return getRequest(`/api/judge/rejudge/batch-rejudge`, method, params)
}

export const batchRejudgeIdReq = (method) => {
  return getRequest(`/api/judge/rejudge/batch-rejudge/batch_id`, method)
}

export const examEventRecordReq = (method, params) => {
  return getRequest(`/api/judge/exam_event_record`, method, params)
}

export const examEventRecordIdReq = (method, id) => {
  return getRequest(`/api/judge/exam_event_record/${id}`, method)
}

export const problemDetailReq = (pieId, pid) => {
  return getRequest(`/api/judge/details/problem/${pieId}/${pid}`, 'get')
}

export const setProblemChoice = (pid, choices) => {
  return getRequest('/api/judge/choice', 'post', { problem_id: pid, add: choices })
}
