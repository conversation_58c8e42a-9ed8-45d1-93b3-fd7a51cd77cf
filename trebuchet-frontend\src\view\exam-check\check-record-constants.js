const recordStatus = [
  { value: 0, name: '未签到', color: 'default', buttonType: 'launchpad-button-notCheckIn' },
  { value: 1, name: '考试中', color: 'yellow', buttonType: 'warning' },
  { value: 2, name: '已签退', color: 'red', buttonType: 'error' },
  { value: 3, name: '排队中', color: 'blue', buttonType: 'info' },
  { value: 4, name: '已通过', color: 'green', buttonType: 'success' }
]

// const recordCheckResult = ['F', 'C', 'B', 'A'] 多的几个算历史遗留,留着防止老的数据寄了
const recordCheckResult = [
  { value: -1, name: 'F' },
  { value: 0, name: 'D' },
  { value: 1, name: 'C' },
  { value: 2, name: 'B' },
  { value: 3, name: 'A' },
  { value: 4, name: 'A+' }
]

const passedColor = { color: 'green', buttonType: 'success' }

export { recordStatus, recordCheckResult, passedColor }
