"""interface check-in and check-out
"""

from django.utils import timezone

from core.api.utils import ErrorCode
from core.models.exam_record import (STATUS_IN_PROGRESS, STATUS_NOT_CHECKED_IN,
                                     ExamRecord)
from core.models.student import Student


def student_check_in_exam_interface(student: Student):
    """与 student_check_in_exam 基本相同，单独抽离出来作为接口
    """
    exam_record: ExamRecord = student.examrecord_set.all().order_by('-id').first()
    if exam_record is None:
        return (False, ErrorCode.ITEM_NOT_FOUND, 'Student does not have this exam record')
    if exam_record.status is not STATUS_NOT_CHECKED_IN:
        return (False, ErrorCode.REFUSE_ACCESS, "Already Check In")

    exam_record.status = STATUS_IN_PROGRESS
    exam_record.checked_in_at = timezone.now()
    exam_record.save()
    return (True, None, None)
