export default {
  namespaced: true,
  state: {
    currentClassroom: null,
    overviewActive: { name: 'queue', radio: '仅查看' }
  },
  mutations: {
    setCurrentClassroom(state, classroom) {
      state.currentClassroom = classroom
    },
    setOverviewTab(state, name) {
      state.overviewActive.name = name
    },
    setOverviewRadio(state, radio) {
      state.overviewActive.radio = radio
    }
  },
  getters: {
    currentClassroom: (state) => state.currentClassroom
  }
}
