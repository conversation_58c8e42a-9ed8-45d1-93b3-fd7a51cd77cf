<template>
  <Card>
    <filter-table
      :data="tableData"
      :columns="columns"
      :default-filter="
        this.$store.state.app.tableFilter.examRecordTable ? this.$store.state.app.tableFilter.examRecordTable : {}
      "
      @on-search="onSearch"
    />
    <div style="margin: 10px; overflow: hidden">
      <div style="float: right">
        <Page
          :total="totalCnt"
          :current="curPage"
          :page-size="pageSize"
          show-elevator
          show-total
          @on-change="changePage"
        >
          <p>总数{{ allRecords }}条，当前已筛选出来{{ totalCnt }}条</p>
        </Page>
      </div>
    </div>
  </Card>
</template>

<script>
import { examRecordReq } from '@/api/exam-record'
import FilterTable from '@/view/filter-table/filter-table'
import { getErrModalOptions } from '@/libs/util'
import _ from 'lodash'
import { recordStatus, recordCheckResult, passedColor } from './check-record-constants'
import { LinkButton, Tag, WhitePre } from '@/libs/render-item'

export default {
  name: 'ExamRecordTable',
  components: { FilterTable },
  data() {
    return {
      tableData: [],
      columns: [
        {
          title: 'Student ID',
          key: 'student__student_id',
          filter: {
            type: 'Input'
          },
          render: (h, params) => WhitePre(h, params.row['student__student_id'])
        },
        {
          title: 'Student Name',
          key: 'student__name',
          filter: {
            type: 'Input'
          },
          render: (h, params) => WhitePre(h, params.row['student__name'])
        },
        {
          title: 'Teacher Name',
          key: 'teacher_name'
        },
        {
          title: 'Exam Date',
          key: 'project_in_exam__exam__date',
          filter: {
            type: 'Input'
          }
        },
        {
          title: 'Project Name',
          key: 'project_in_exam__project__name',
          filter: {
            type: 'input'
          }
        },
        {
          title: 'Grade',
          key: 'grade',
          filter: {
            type: 'Select',
            option: recordCheckResult
          },
          render: (h, params) =>
            h('p', params.row.status === 2 ? recordCheckResult[params.row.check_result + 1].name : '暂无评级')
        },
        {
          title: 'Comment',
          render: (h, params) => h('p', params.row.check_comment === '' ? '暂无评语' : params.row.check_comment)
        },
        {
          title: 'Examinant',
          key: 'examinant__username',
          filter: {
            type: 'input'
          },
          render: (h, params) =>
            h(
              'p',
              params.row['examinant__first_name'] === ''
                ? params.row['examinant__username']
                : `${params.row['examinant__first_name']} (${params.row['examinant__username']})`
            )
        },
        {
          title: 'Status',
          key: 'status',
          render: (h, params) =>
            Tag(
              h,
              params.row.status === 2 && params.row.check_result !== -1
                ? passedColor.color
                : recordStatus[params.row.status].color,
              recordStatus[params.row.status].name
            ),
          filter: {
            type: 'Select',
            option: recordStatus
          }
        },
        {
          title: 'Action',
          key: 'action',
          align: 'center',
          render: (h, params) => LinkButton(h, params.row.id, 'check_exam_record', '查看修改', false)
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      order_by: '-project_in_exam__exam__date',
      filter: {},
      allRecords: 0
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.examRecordTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.examRecordTable)
    }
    this.curPage = this.$store.state.app.tablePage.examRecordTable ? this.$store.state.app.tablePage.examRecordTable : 1
    this.loadData(this.curPage)
  },
  methods: {
    loadData(index) {
      examRecordReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.allRecords = res.data['models_all']
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    changePage(index) {
      examRecordReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.allRecords = res.data['models_all']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'examRecordTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onSearch(search) {
      search = this.refactorSearchObject(search)
      examRecordReq('get', {
        page: 1,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.allRecords = res.data['models_all']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'examRecordTable' })
          this.$store.commit('setTableFilter', { filter: search, name: 'examRecordTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {} // reset filter
      Object.keys(search).forEach((key) => {
        if (key === 'status') {
          this.filter[key + '__exact'] = search[key]
        } else if (key === 'project_in_exam__exam__id') {
          this.filter['exam__exact'] = search[key]
        } else if (key === 'project_in_exam__project__name') {
          this.filter['project_name__contains'] = search[key]
        } else if (key === 'examinant__username') {
          this.filter['examinant__username__contains'] = search[key]
        } else if (key === 'project_in_exam__exam__date') {
          this.filter['date__exact'] = search[key]
        } else if (key === 'student__name') {
          this.filter['name__contains'] = search[key]
        } else if (key === 'grade') {
          this.filter['check_result__exact'] = search[key]
        } else {
          this.filter['student_id__contains'] = search[key]
        }
      })
      return searchNew
    }
  }
}
</script>
