"""
declare Student model
"""
from django.db import models
from django.contrib.auth import get_user_model

from core.models.permissions import (STUDENT_CHANGE, STUDENT_CREATE,
                                     STUDENT_DELETE, STUDENT_VIEW)

GENDER_TYPE_UNKNOWN = 0
GENDER_TYPE_MALE = 1
GENDER_TYPE_FEMALE = 2

CATEGORY_DEFAULT = 0


class Student(models.Model):
    """This model describes an student.

    Attributes:
        student_id: A <PERSON>r<PERSON>ield storing the student's unique id in BUAA.
        name: A <PERSON><PERSON><PERSON>ield storing the student's name.
        department: An IntegerField which represents the subject in which the student majors.
        email: An EmailField storing the student's email.
        photo: An FilePathField storing the location of student's photo.
        thumb: An FilePathField storing the location of student's Thumbnail.
    """
    GENDER_TYPE = [
        (GENDER_TYPE_UNKNOWN, '未知'),
        (GENDER_TYPE_MALE, '男'),
        (GENDER_TYPE_FEMALE, '女'),
    ]
    student_id = models.CharField(max_length=25, unique=True)
    category = models.IntegerField(default=CATEGORY_DEFAULT)
    name = models.CharField(max_length=50)
    gender = models.IntegerField(choices=GENDER_TYPE, default=GENDER_TYPE_UNKNOWN)
    department = models.IntegerField()
    official_class = models.CharField(max_length=25)
    photo = models.ImageField(null=True, upload_to='student/photo')
    user = models.OneToOneField(get_user_model(), on_delete=models.CASCADE, null=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (STUDENT_CHANGE, STUDENT_CHANGE),
            (STUDENT_CREATE, STUDENT_CREATE),
            (STUDENT_DELETE, STUDENT_DELETE),
            (STUDENT_VIEW, STUDENT_VIEW)
        ]
