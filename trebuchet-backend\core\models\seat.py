"""
declare Seat model
"""

from django.db import models

from core.models.permissions import (SEAT_CHANGE, SEAT_CREATE, SEAT_DELETE,
                                     SEAT_VIEW)
from core.models.room import Room


class Seat(models.Model):
    """Describe a seat in physical world.

    Attributes:
        room -- A Foreignkey to Room with a related_name 'seats'. Where the seat is.
        name -- A <PERSON><PERSON><PERSON><PERSON> saving the seat's identity in the room, usually it's a seat number
        pos_x -- An Integer<PERSON>ield saving the seat's position information, required by frontend
        pos_y -- An IntegerField saving the seat's position information, required by frontend
        available -- A BooleanField.
                     Whether the seat is available (maybe useful in automatic seat-planning)
        comment -- A TextField saving extra comments/details of current seat
    """
    room = models.ForeignKey(to=Room, on_delete=models.CASCADE)
    name = models.CharField(max_length=25)
    pos_x = models.IntegerField()
    pos_y = models.IntegerField()
    available = models.BooleanField(default=True)
    comment = models.TextField(default='')

    class Meta:
        default_permissions = ()
        permissions = [
            (SEAT_CHANGE, SEAT_CHANGE),
            (SEAT_CREATE, SEAT_CREATE),
            (SEAT_DELETE, SEAT_DELETE),
            (SEAT_VIEW, SEAT_VIEW)
        ]
