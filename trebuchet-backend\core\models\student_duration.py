"""
declare StudentDuratino Model
"""
from django.db import models
from core.models.student import Student



class StudentDuration(models.Model):
    """Record how long does the student log in the cscore

    Attributes:
        Student_id   string
        Last_Connect sql.NullTime
        Duration     int
    """

    student = models.ForeignKey(to=Student, on_delete=models.CASCADE)
    last_connect = models.DateTimeField()
    duration = models.IntegerField()

    class Meta:
        default_permissions = ()
        permissions = []
