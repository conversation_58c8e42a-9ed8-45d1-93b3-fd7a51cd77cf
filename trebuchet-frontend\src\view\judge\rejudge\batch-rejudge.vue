<template>
  <div>
    <Row>
      <Col span="12" offset="6">
        <Card>
          <Form :model="examsChosen">
            <form-item v-for="(exam, index) in examsChosen.exams" :key="index" :prop="'exams.' + index + '.value'">
              <Row>
                <Col span="6">
                  <Select
                    v-model="exam.value"
                    style="width: 200px; float: left"
                    placeholder="请选择Exam"
                    @on-change="handleConfirm(exam.value, index)"
                  >
                    <Option v-for="item in examList" :key="item.id" :value="item.id">
                      {{ item.id }} : {{ item.date }}
                    </Option>
                  </Select>
                </Col>
                <Col span="4" offset="4">
                  <Button type="primary" @click="handleRemove(index)">删除</Button>
                </Col>
              </Row>
              <br />
              <Row>
                <Col span="10">
                  <Select
                    v-model="projectSelected[exam.value]"
                    placeholder="请选择Project"
                    multiple
                    @on-open-change="handleClose($event, exam.value)"
                  >
                    <Option
                      v-for="item in projectInExam[exam.value]"
                      :key="item.id"
                      :value="item.id"
                      :label="item['project__name']"
                    />
                  </Select>
                </Col>
              </Row>
              <br />
              <Row>
                <Col span="10">
                  <Select v-model="problemSelected[exam.value]" placeholder="请选择题目" multiple>
                    <Option
                      v-for="item in problemSelected[exam.value]"
                      :key="item.id"
                      :value="item.id"
                      :label="item.name"
                    />
                  </Select>
                </Col>
              </Row>
              <Divider />
            </form-item>
            <Row>
              <Col span="8">
                <Button type="dashed" long icon="plus-round" @click="handleAdd()">新增 Exam</Button>
              </Col>
            </Row>
            <br />
            <Row>
              <Col span="10">
                <Select v-model="rejudgeType" placeholder="请选择重测范围" clearable>
                  <Option
                    v-for="item in rejudgeTypeOptions"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </Select>
              </Col>
            </Row>
            <br />
            <Row>
              <Col span="10">
                <Input v-model="rejudgeComment" placeholder="填写comment" />
              </Col>
            </Row>
            <Row>batch_id 生成方式: {日期}_{时间}_{comment}</Row>
            <br />
            <FormItem>
              <Button type="primary" :loading="submitLoading" @click="handleSubmit">提交重测</Button>
              <Button style="margin-left: 8px" @click="handleReset">重置</Button>
            </FormItem>
          </Form>
        </Card>
      </Col>
    </Row>
    <Row v-if="started">
      <Col span="12" offset="6">
        <h3>提示 : 在完成评测前不要离开页面或刷新</h3>
        <div>评测中,评测进度: {{ judgeProgress }} / {{ recordsToRejudge.length }}</div>
      </Col>
    </Row>
    <Row>
      <Col span="12" offset="6">
        <Table v-if="finished" :data="rejudgeRecords" :columns="columns" no-data-text="没有结果变化的record" />
      </Col>
    </Row>
  </div>
</template>

<script>
import { Tag } from '@/libs/render-item'
import { getErrModalOptions } from '@/libs/util'
import { judgeReq, batchRejudgeReq } from '@/api/judge'
import { examProjectReq, examProjectIdReq, examReq } from '@/api/exam'

export default {
  name: 'BatchRejudge',
  data() {
    return {
      examList: [],
      examsChosen: {
        exams: [
          {
            value: ''
          }
        ]
      },
      projectInExam: {},
      projectSelected: {},
      problemSelected: {},
      rejudgeTypeOptions: [
        { value: 0, label: '全部学生的最新 Record' },
        { value: 1, label: '全部学生的最新且结果为 WA 的 Record' }
      ],
      recordsToRejudge: [],
      rejudgeType: null,
      rejudgeComment: '',
      finished: false,
      started: false,
      rejudgeRecords: [],
      columns: [
        { key: 'origin_recordId', title: '原记录ID' },
        { key: 'user_name', title: '学生' },
        {
          title: '原结果',
          render: (h, params) => (!params.row.origin_result ? Tag(h, 'green', 'PASSED') : Tag(h, 'red', 'FAILED'))
        },
        {
          title: '新结果',
          render: (h, params) => (!params.row.rejudge_result ? Tag(h, 'green', 'PASSED') : Tag(h, 'red', 'FAILED'))
        },
        { key: 'problem_id', title: '题目ID' }
      ],
      intervalId: 0,
      judgeProgress: 0,
      submitLoading: false
    }
  },
  computed: {
    totalCnt() {
      return this.batchIdList.length
    }
  },
  mounted() {
    this.loadExams()
  },
  methods: {
    loadExams() {
      examReq('get', {
        order_by: '-date'
      })
        .then((res) => {
          this.examList = res.data.exams
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
    },
    handleRemove(index) {
      this.examsChosen.exams = this.examsChosen.exams.filter((element, i) => {
        return i !== index
      })
    },
    handleAdd() {
      this.examsChosen.exams = this.examsChosen.exams.concat([{ value: '' }])
    },
    handleConfirm(examId) {
      examProjectReq('get', { exam__id__exact: examId })
        .then((res) => {
          this.projectInExam[examId] = res.data.models
          this.projectSelected[examId] = []
          this.$Notice.info({ title: `选中的 ExamId : ${examId}` })
          this.$forceUpdate()
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    handleClose(open, examId) {
      if (open === false) {
        if (this.projectSelected[examId] === undefined) {
          return
        }
        this.problemSelected[examId] = []
        this.projectSelected[examId].forEach((projectId) => {
          examProjectIdReq('get', projectId).then((res) => {
            res.data.problems.forEach((item) => {
              this.problemSelected[examId].push(item)
            })
            this.$forceUpdate()
          })
        })
      }
    },
    selectByLast(data) {
      let userList = new Set()
      const filter = this.rejudgeType === 1 ? (record) => record.judge_result === 1 : () => true
      data.forEach((item) => {
        let name = item['edx_username']
        if (!userList.has(name)) {
          userList.add(name)
          if (filter(item)) this.recordsToRejudge.push(item.id)
        }
      })
    },
    handleSubmit() {
      this.submitLoading = true
      this.recordsToRejudge = []
      let problems = []
      for (const examId in this.problemSelected) {
        problems = problems.concat(this.problemSelected[examId])
      }
      Promise.all(
        problems.map(
          (item) =>
            new Promise((resolve, reject) => {
              judgeReq('get', { problem__id__exact: item })
                .then((res) => {
                  judgeReq('get', { problem__id__exact: item, page_size: res.data['total_count'] }).then((result) => {
                    this.selectByLast(result.data.models)
                    resolve(result.data.models)
                  })
                })
                .catch((err) => reject(err))
            })
        )
      )
        .then(() => {
          if (this.recordsToRejudge.length === 0) {
            this.$Notice.info({ title: '无需要重测的记录' })
            this.submitLoading = false
            return
          }
          batchRejudgeReq('post', {
            record_ids: this.recordsToRejudge,
            rejudge_comment: this.rejudgeComment
          })
            .then((res) => {
              this.judgeProgress = 0
              this.started = true
              this.intervalId = setInterval(() => {
                batchRejudgeReq('get', { batch_id__exact: res.data.batch_id }).then((res) => {
                  this.testIfFinish(res.data)
                })
              }, 2000)
            })
            .catch((err) => this.$Modal.error(getErrModalOptions(err)))
        })
        .catch((err) => this.$Modal.error(getErrModalOptions(err)))
    },
    countPending(records) {
      let total = 0
      for (const record in records) {
        if (records[record]['rejudge_record_data'].judge_result < 0) ++total
      }
      return total
    },
    testIfFinish(data) {
      this.judgeProgress = data['all_record_count'] - this.countPending(data['records'])
      if (this.judgeProgress === this.recordsToRejudge.length) {
        this.finished = true
        this.started = false
        clearInterval(this.intervalId)
        this.showData(data)
        this.submitLoading = false
      }
    },
    handleReset() {
      this.examsChosen = {
        exams: [
          {
            value: ''
          }
        ]
      }
      this.projectInExam = {}
      this.projectSelected = {}
      this.problemSelected = {}
      this.rejudgeType = null
      this.finished = false
    },
    showData(data) {
      this.rejudgeRecords = []
      const records = data['records']
      for (const item in records) {
        this.rejudgeRecords.push({
          origin_recordId: records[item]['judge_record_data']['id'],
          user_name: records[item]['judge_record_data']['edx_username'],
          origin_result: records[item]['judge_record_data']['judge_result'],
          rejudge_result: records[item]['rejudge_record_data']['judge_result'],
          problem_id: records[item]['judge_record_data']['problem']
        })
      }
    }
  }
}
</script>
