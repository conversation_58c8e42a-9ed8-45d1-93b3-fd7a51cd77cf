"""
define problem judge record's intermediate functions exposed to other apps
"""
from core.models.project_in_exam import ProjectInExam
import datetime
import pytz
from collections import ChainMap

from django.db.models import Count, F, Max, Q
from django.utils import timezone

from judge.constants import FAILED, PASSED
from judge.models.problem import Problem
from judge.models.problem_judge_record import ProblemJudgeRecord


def get_problem_judge_record_by_q(query_dict: dict):
    """intermediate function
    """
    record_filter = Q()
    for key, value in query_dict.items():
        record_filter &= Q(**{key: value})
    return ProblemJudgeRecord.objects.filter(record_filter)


def get_problem_judge_record_for_core(username: str, problem_id: int, judge_result: int):
    """interface function for app core
    """
    record_filter_dict = {
        "edx_username__exact": username,
        "problem_id__exact": problem_id,
        "judge_result__exact": judge_result
    }
    return get_problem_judge_record_by_q(record_filter_dict)


def get_problem_final_judge_result_for_core(username: str, problem_id: int, pie_id=None):
    """interface function for app core
    """
    if pie_id is None:
        record = ProblemJudgeRecord.objects.filter(
            edx_username=username, problem__id=problem_id).order_by('-id').first()
    else:
        pie = ProjectInExam.objects.get(pk=pie_id)
        record = ProblemJudgeRecord.objects.filter(
            edx_username=username, problem__id=problem_id, project_in_exam=pie).order_by('-id').first()
    if record and record.judge_result == 0:
        return True
    return False


def get_last_submit_of_students(edx_username_list: str):
    """interface function for app core
    """
    records = ProblemJudgeRecord.objects \
        .annotate(problem_name=F("problem__name")) \
        .filter(edx_username__in=edx_username_list).order_by(
        "edx_username", "-id").distinct("edx_username")

    return dict(ChainMap(*map(lambda pair: {pair[0]: {"submitted_at": pair[1], "problem_name": pair[2]}},
                              list(records.values_list("edx_username", "submitted_at", "problem_name")))))


def get_final_judge_result_of_student_in_project_in_exam(project_in_exam_id: int, edx_username: str) -> list:
    """interface function for app core
    """
    pie = ProjectInExam.objects.get(pk=project_in_exam_id)
    final_records = ProblemJudgeRecord.objects.filter(
        edx_username=edx_username,
        project_in_exam=pie).order_by('problem', '-id').distinct('problem')

    result = {}
    for record in final_records:
        result[record.problem.id] = {
            'record_id': record.id,
            'problem_name': record.problem.name,
            'judge_result': 'Passed' if record.judge_result == PASSED else 'Failed',
        }

    return result


def statistics_at_the_end_of_the_semester(begin_at, students, pie_begin_at, projects):
    """Some sad stories
    """
    project_judge_record_all = ProblemJudgeRecord.objects.filter(submitted_at__gt=begin_at)
    result = []
    for student in students:
        student_name = student[0]
        student_id = student[1]
        progress = student[2]
        # initialize
        data = {
            "student_id": student_id,
            "student_name": student_name,
            "progress": progress,
            "all_cnt": 0,
            "ac_cnt": 0,
            "wa_cnt": 0,
            "max_problem": "",
            "max_problem_cnt": 0,
            "night_submission_cnt": 0,
            "latest_date": begin_at.date(),
            "latest_time": begin_at.time(),
            "quickest_problem": "",
            "quickest_rank": 0
        }

        pjr = project_judge_record_all.filter(edx_username=student_id)
        all_cnt = pjr.count()
        data["all_cnt"] = all_cnt

        # AC WA count
        ac_pjr = pjr.filter(judge_result=PASSED)
        wa_pjr = pjr.filter(judge_result=FAILED)

        data["ac_cnt"] = ac_pjr.count()
        data["wa_cnt"] = wa_pjr.count()

        problems = pjr.values("problem__name").annotate(cnt=Count("problem"))

        max_problem_cnt = problems.aggregate(Max("cnt")).get("cnt__max")

        try:
            max_problem = next(problem["problem__name"]
                               for problem in list(problems) if problem["cnt"] == max_problem_cnt)
            data["max_problem_cnt"] = max_problem_cnt
            data["max_problem"] = max_problem
        except StopIteration:
            # no submission
            result.append(data)
            continue

        # late
        offset_time = datetime.timedelta(hours=6)
        latest_date = datetime.datetime.combine(begin_at, datetime.datetime.min.time())
        for submitted_time in map(lambda x: (timezone.localtime(x["submitted_at"]) - offset_time),
                                  pjr.values("submitted_at")):
            if submitted_time.time() > latest_date.time():
                latest_date = submitted_time
        latest_date = latest_date + offset_time
        data["latest_date"] = latest_date.date()
        data["latest_time"] = latest_date.time()

        data["night_submission_cnt"] = pjr.filter(
            Q(submitted_at__time__gte=datetime.time(hour=1)) & Q(submitted_at__time__lt=datetime.time(hour=5))).count()

        # quick
        quickest_time = datetime.timedelta(hours=114514)
        quickest_problem_id = 0
        for record in ac_pjr.order_by("problem", "submitted_at").distinct("problem").values("problem",
                                                                                            "project_in_exam",
                                                                                            "submitted_at"):
            if record["project_in_exam"] is None or record["project_in_exam"] == -1:
                continue
            period = timezone.localtime(record["submitted_at"]) - pie_begin_at[record["project_in_exam"]]
            if period < quickest_time:
                quickest_time = period
                quickest_problem_id = record["problem"]
        # no available submission
        if quickest_problem_id != 0:
            quickest_problem = Problem.objects.get(pk=quickest_problem_id).name

            problem_rank = project_judge_record_all.filter(problem__id=quickest_problem_id) \
                .filter(judge_result=PASSED) \
                .order_by("edx_username", "submitted_at") \
                .distinct("edx_username").values("edx_username", "submitted_at")
            problem_rank = sorted(problem_rank, key=lambda x: x["submitted_at"])
            quickest_rank = next(
                (index for (index, d) in enumerate(problem_rank) if d["edx_username"] == student_id)) + 1
            data["quickest_problem"] = quickest_problem
            data["quickest_rank"] = quickest_rank

        # projects
        data.update({"Pre_cnt": 0})
        project_sum = 0
        for project in projects:
            project_cnt = pjr.filter(problem__name__iregex=r"^{}_.+$".format(project)).count()
            project_sum += project_cnt
            data.update({
                project + "_cnt": project_cnt
            })
        data["Pre_cnt"] += all_cnt - project_sum

        result.append(data)
    return result


def statistics_distribution_at_the_end_of_the_semester(begin_at, students):
    """Some sad stories
    """
    project_judge_record_all = ProblemJudgeRecord.objects.filter(submitted_at__gt=begin_at)
    result = []
    for student in students:
        student_name = student[0]
        student_id = student[1]
        # initialize

        pjr = project_judge_record_all.filter(edx_username=student_id)
        all_cnt = pjr.count()
        sunday_cnt = pjr.filter(submitted_at__week_day=1).count()
        monday_cnt = pjr.filter(submitted_at__week_day=2).count()
        tuesday_cnt = pjr.filter(submitted_at__week_day=3).count()
        wednesday_cnt = pjr.filter(submitted_at__week_day=4).count()
        thursday_cnt = pjr.filter(submitted_at__week_day=5).count()
        friday_cnt = pjr.filter(submitted_at__week_day=6).count()
        saturday_cnt = pjr.filter(submitted_at__week_day=7).count()

        from_0_to_3_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=0)) &
                                     Q(submitted_at__time__lt=datetime.time(hour=3))).count()
        from_3_to_6_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=3)) &
                                     Q(submitted_at__time__lt=datetime.time(hour=6))).count()
        from_6_to_9_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=6)) &
                                     Q(submitted_at__time__lt=datetime.time(hour=9))).count()
        from_9_to_12_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=9)) &
                                      Q(submitted_at__time__lt=datetime.time(hour=12))).count()
        from_12_to_15_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=12)) &
                                       Q(submitted_at__time__lt=datetime.time(hour=15))).count()
        from_15_to_18_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=15)) &
                                       Q(submitted_at__time__lt=datetime.time(hour=18))).count()
        from_18_to_21_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=18)) &
                                       Q(submitted_at__time__lt=datetime.time(hour=21))).count()
        from_21_to_24_cnt = pjr.filter(Q(submitted_at__time__gte=datetime.time(hour=21)) &
                                       Q(submitted_at__time__lte=datetime.time(hour=23))).count()
        data = {
            "student_id": student_id,
            "student_name": student_name,
            "all_cnt": all_cnt,
            "monday_cnt": monday_cnt,
            "tuesday_cnt": tuesday_cnt,
            "wednesday_cnt": wednesday_cnt,
            "thursday_cnt": thursday_cnt,
            "friday_cnt": friday_cnt,
            "saturday_cnt": saturday_cnt,
            "sunday_cnt": sunday_cnt,
            "from_0_to_3_cnt": from_0_to_3_cnt,
            "from_3_to_6_cnt": from_3_to_6_cnt,
            "from_6_to_9_cnt": from_6_to_9_cnt,
            "from_9_to_12_cnt": from_9_to_12_cnt,
            "from_12_to_15_cnt": from_12_to_15_cnt,
            "from_15_to_18_cnt": from_15_to_18_cnt,
            "from_18_to_21_cnt": from_18_to_21_cnt,
            "from_21_to_24_cnt": from_21_to_24_cnt,
        }
        result.append(data)
    return result


def non_submit_checker(course_underclass_start_time, edx_username):
    course_start_datetime = datetime.datetime.strptime(str(course_underclass_start_time), '%Y-%m-%d')
    if not ProblemJudgeRecord.objects.filter(
            edx_username=edx_username,
            submitted_at__gte=course_start_datetime.astimezone(pytz.timezone('Asia/Shanghai'))
    ).exists():
        return True
    return False
