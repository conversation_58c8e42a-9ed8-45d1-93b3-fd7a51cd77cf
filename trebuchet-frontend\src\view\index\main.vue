<template>
  <Layout style="height: 100%" class="main">
    <Sider
      v-model="collapsed"
      :width="mini ? fullWidth : 256"
      :collapsed-width="mini ? 0 : 64"
      :style="{ overflow: 'hidden' }"
      class="left-sider"
      hide-trigger
      collapsible
    >
      <side-menu
        ref="sideMenu"
        :active-name="$route.name"
        :collapsed="collapsed"
        :menu-list="menuList"
        @on-select="turnToPage"
        accordion
      >
        <div class="logo-con">
          <img v-show="!collapsed" key="max-logo" :src="require('@/assets/images/logo.png')" alt="max-logo" />
          <img v-show="collapsed" key="min-logo" :src="require('@/assets/images/logo-min.png')" alt="min-logo" />
        </div>
      </side-menu>
    </Sider>
    <Layout>
      <Header class="header-con">
        <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
          <user />
          <fullscreen v-model="isFullscreen" style="margin-right: 10px" />
        </header-bar>
      </Header>
      <Content class="main-content-con">
        <Layout class="main-layout-con">
          <Content class="content-wrapper">
            <keep-alive :include="cacheList">
              <router-view :key="$route.fullPath" />
            </keep-alive>
            <ABackTop :height="100" :bottom="80" :right="50" container=".content-wrapper" />
          </Content>
        </Layout>
      </Content>
    </Layout>
  </Layout>
</template>

<script>
import './main.less'
import routers from '@/router/routers'
import { mapActions, mapMutations } from 'vuex'

import User from '@/components/user'
import SideMenu from '@/components/side-menu'
import ABackTop from '@/components/a-back-top'
import HeaderBar from '@/components/header-bar'
import Fullscreen from '@/components/fullscreen'

export default {
  name: 'Main',
  components: {
    User,
    SideMenu,
    ABackTop,
    HeaderBar,
    Fullscreen
  },
  data() {
    return {
      collapsed: false,
      isFullscreen: false
    }
  },
  computed: {
    tagNavList() {
      return this.$store.state.app.tagNavList
    },
    cacheList() {
      const filter = this.tagNavList.filter((item) => !(item.meta && item.meta.notCache)).map((item) => item.name)
      return ['ParentView', ...(this.tagNavList.length ? filter : [])]
    },
    menuList() {
      return this.$store.getters.menuList
    },
    mini() {
      return this.$store.getters['view/mini']
    },
    fullWidth() {
      return this.$store.getters['view/fullWidth']
    }
  },
  watch: {
    $route(newRoute) {
      const { name, query, params, meta } = newRoute
      this.updateTagNavList({
        route: { name, query, params, meta },
        type: 'push'
      })
      this.setBreadCrumb(newRoute)
      this.$refs.sideMenu.updateOpenName(newRoute.name)
    }
  },
  mounted() {
    this.setHomeRoute(routers)
    const { name, params, query, meta } = this.$route
    this.updateTagNavList({
      route: { name, params, query, meta }
    })
    this.setBreadCrumb(this.$route)
    if (!this.tagNavList.find((item) => item.name === this.$route.name)) {
      this.$router.push({ name: 'home' })
    }
    this.resizeEventHandler()
    window.addEventListener('resize', this.resizeEventHandler)
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeEventHandler)
  },
  methods: {
    ...mapMutations(['setBreadCrumb', 'updateTagNavList', 'setHomeRoute']),
    ...mapActions({ handleLogin: 'user/handleLogin' }),
    turnToPage(route) {
      let { name, params, query } = {}
      if (typeof route === 'string') {
        name = route
      } else {
        name = route.name
        params = route.params
        query = route.query
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        window.open(name.split('_')[1])
        return
      }
      this.$router.push({ name, params, query })
      if (this.mini) {
        this.collapsed = true
      }
    },
    handleCollapsedChange(state) {
      this.collapsed = state
    },
    resizeEventHandler() {
      this.$store.commit('view/updateSize', window.innerWidth, window.innerHeight)
      this.collapsed = window.innerWidth < 1100
    }
  }
}
</script>
