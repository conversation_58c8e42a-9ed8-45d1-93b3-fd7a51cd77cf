Debug: false # Set to true for development

# Postgres
DatabaseHost: <database host>
DatabasePort: 1111
DatabaseUser: trebuchet-develop
DatabasePassword: "<redacted>"
DatabaseName: trebuchet-develop

# Redis
RedisAddress: localhost:6379
RedisPassword: ""
RedisDatabase: 0

# Mail
SmtpUsername: <EMAIL>
SmtpFrom: <EMAIL>
SmtpPassword: sadfasdfasdfasd
SmtpHost: smtpdm.aliyun.com
SmtpPort: 465

# Object Storage
S3SecretId: asdfsadfasdfasdf
S3SecretKey: asdfasfsadf
S3Region: ap-beijing
S3Address: 127.0.0.1:9109
S3BucketSubmission: cscore-submission-dev
S3BucketImage: cscore-image-dev
S3BucketJudgeData: cscore-judgedata-dev
S3BucketProblemData: cscore-problemdata-dev
S3UseSSL: false

# Django specific
DjangoSecretKey: 3a5da36e-6ea6-47d9-a3f1-e00826b89412

# Application settings
MaxUploadSize : 10485760
LogPath: ./ll.log # LOG_PATH is optional

# Git update token
GitToken: xxxxxq
