<template>
  <Row>
    <Col span="8" offset="6">
      <Card>
        <Form ref="courseNew" :model="courseNew" :rules="courseRule" :label-width="120">
          <form-item prop="name" label="课程名称">
            <Input v-model="courseNew.name" type="text" />
          </form-item>
          <form-item prop="code" label="课程代码">
            <Input v-model="courseNew.code" type="text" />
          </form-item>
          <form-item>
            <Button type="primary" @click="handleSubmit('courseNew')">确认创建</Button>
          </form-item>
        </Form>
      </Card>
    </Col>
  </Row>
</template>

<script>
import { courseReq } from '@/api/course'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'CourseCreate',
  data() {
    return {
      currentCourses: [],
      courseNew: {
        name: '',
        code: ''
      },
      courseRule: {
        name: [{ required: true, message: '请填写课程名称', trigger: 'blur' }],
        code: [{ required: true, message: '请填写课程编号', trigger: 'blur' }]
      }
    }
  },
  methods: {
    upload(data) {
      return courseReq('post', data)
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          const date = new Date()
          this.upload({
            name: this.courseNew.name,
            code: this.courseNew.code,
            date: `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
          })
            .then(() => {
              this.$Notice.success({ title: '创建成功' })
              this.$router.push({ name: 'course_table' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    }
  }
}
</script>
