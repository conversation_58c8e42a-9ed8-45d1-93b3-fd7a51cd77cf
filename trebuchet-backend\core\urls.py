"""
define the url routes of core api
"""
from django.urls import path

from core.api.auth import obtain_jwt_token, refresh_jwt_token
from core.api.check_judge_permission import CHECK_JUDGE_PERMISSION_API
from core.api.check_questions import QUESTION_LIST_API, QUESTION_CRUD_API
from core.api.class_management import CLASS_DETAIL_API, CLASS_SET_API
from core.api.course_management import COURSE_DETAIL_API, COURSE_SET_API, RETAKE_STUDENT_API, \
    COURSE_ANNOUNCEMENT_API, course_export_final_results, course_archive
from core.api.enrollment import (enroll_all, enrollment_flush, student_enroll,
                                 student_unenroll, update_course_enrollment)
from core.api.exam import EXAM_DETAIL_API, EXAM_SET_API, retrieve_students_in_exam
from core.api.exam_check_in_and_out import (EXAM_CHECK_IN_API, EXAM_CHECK_OUT_API, EXAM_CLEAR_ROOM_API,
                                            EXAM_ROLL_BACK_CHECK_IN_API,
                                            EXAM_ROLL_BACK_CHECK_OUT_API)
from core.api.exam_queue import QUEUE_HEAD_API, QUEUE_LIST_API, QUEUE_PUSH_API, roll_back_push_student_in_queue, \
    QUEUE_POP_API
from core.api.exam_record import WECHAT_GET_GRADE_LIST_API, EXAM_RECORD_DETAIL_API, EXAM_RECORD_SET_API, \
    EXAM_RECORD_PROBLEM_DETAIL_API, export_all_exam_records, export_final_exam_records, EXAM_RECORD_DELAY_API
from core.api.frontend_test import HAND_UP_TEST_API, test_hand_up_in_exam
from core.api.news import NEWS_API
from core.api.project import PROJECT_DETAIL_API, PROJECT_SET_API, PROJECT_WHITELIST_API
from core.api.project_in_exam import (PROJECT_IN_EXAM_DETAIL_API,
                                      PROJECT_IN_EXAM_SET_API,
                                      REQUIREMENT_SET_API, get_project_in_exam_csv,
                                      QUESTION_SET_API, download_error_info,
                                      get_exam_projects)
from core.api.push_message import PUSH_MESSAGE_DETAIL_API, PUSH_MESSAGE_SET_API, do_push_push_test, \
    REDO_PUSH_EMAIL_MESSAGE_DETAIL_API
from core.api.room import ROOM_ARRANGEMENT_API, ROOM_DETAIL_API, ROOM_EXAMRECORD_API, ROOM_SET_API
from core.api.seat import SEAT_DETAIL_API, SEAT_SET_API
from core.api.statistics import final_statistics, final_statistics_distribution
from core.api.student_management import (STUDENT_DETAIL_API, STUDENT_PHOTO_API,
                                         STUDENT_SET_API, STUDENT_SET_CSV_API)
from core.api.student_progress import (STUDENT_PROGRESS_SET_API, check_in_exam,
                                       update_students_under_class_progress, initialize_progress,
                                       initialize_retake_progress,
                                       PUSH_ALL_STUDENT_AFTER_EXAM_API,
                                       PUSH_ALL_STUDENT_BEFORE_EXAM_API,
                                       STUDENT_PROGRESS_STATISTICS_API,
                                       skip_to_next_project_in_exam,
                                       list_student_progress_csv_output,
                                       list_student_progress_achievement,
                                       list_student_progress_achievement_csv_output,
                                       get_student_pass_under_class,
                                       get_non_submit_student_list,
                                       get_combined_student_progress_statistics,
                                       get_combined_student_progress_statistics_byclass,
                                       reset_retake_progress,
                                       get_qualification_list_of_pie,
                                       get_students_exam_record,
                                       CLASS_PROGRESS_API, NO_RETAKE_CLASS_PROGRESS_API)
from core.api.student_seat_record import (ARRANGEMENT_DETAIL_API, get_student_seat_csv,
                                          STUDENT_SEAT_API)
from core.api.tag import TAG_API, TAG_DETAIL_API
from core.api.user_group_management import create_user_group, delete_user_group, list_groups, USER_GROUP_DETAIL_API
from core.api.user_management import (change_password, create_user,
                                      delete_user, list_users, list_permissions, USER_DETAIL_API)
from core.api.user_profile import (USER_PROFILE_DETAIL_API,
                                   SPECIFIED_USER_PROFILE_DETAIL_API,
                                   AUTHORIZED_COURSES_API,
                                   SPECIFIED_AUTHORIZED_COURSES_API)
from core.api.fail_analysis import (get_project_fail_analysis,
                                    update_project_fail_analysis)

urlpatterns = [
    path('classes', CLASS_SET_API),
    path('classes/<int:class_id>', CLASS_DETAIL_API),
    path('students', STUDENT_SET_API),
    path('students-csv', STUDENT_SET_CSV_API),
    path('students/<str:student_id>', STUDENT_DETAIL_API),
    path('students/<int:id>/photo', STUDENT_PHOTO_API),
    path('rooms', ROOM_SET_API),
    path('rooms/<int:id>', ROOM_DETAIL_API),
    path('room-arrangement/<int:id>', ROOM_ARRANGEMENT_API),
    path('room-examrecord/<int:id>', ROOM_EXAMRECORD_API),
    path('seats', SEAT_SET_API),
    path('seats/<int:id>', SEAT_DETAIL_API),
    path('courses', COURSE_SET_API),
    path('courses/<int:course_id>', COURSE_DETAIL_API),
    path('announcement', COURSE_ANNOUNCEMENT_API),
    path('courses/<int:course_id>/retake_student', RETAKE_STUDENT_API),
    path('courses/<int:course_id>/final-results', course_export_final_results),
    path('courses/<int:course_id>/get-archive-json', course_archive),
    path('seat_arrangements/<int:exam_id>', ARRANGEMENT_DETAIL_API),
    path('seat_arrangements/<int:exam_id>/csv', get_student_seat_csv),
    path('student-seat/<str:student_id>', STUDENT_SEAT_API),
    path('token-auth', obtain_jwt_token),
    path('token-refresh', refresh_jwt_token),
    path('project', PROJECT_SET_API),
    path('project/<int:id>', PROJECT_DETAIL_API),
    path('courses/<int:course_id>/enroll', student_enroll),
    path('courses/<int:course_id>/unenroll', student_unenroll),
    path('courses/<int:course_id>/enroll_flush', enrollment_flush),
    path('courses/<int:course_id>/enroll_all', enroll_all),
    path('courses/<int:course_id>/update_enrollment', update_course_enrollment),
    path('user-profile', USER_PROFILE_DETAIL_API),
    path('user-profile/<int:user_id>', SPECIFIED_USER_PROFILE_DETAIL_API),
    path('user-profile/authorized-courses', AUTHORIZED_COURSES_API),
    path('user-profile/<int:user_id>/authorized-courses', SPECIFIED_AUTHORIZED_COURSES_API),
    path('exams', EXAM_SET_API),
    path('exams/<int:exam_id>', EXAM_DETAIL_API),
    path('project-in-exam/<int:id>', PROJECT_IN_EXAM_DETAIL_API),
    path('project-in-exam', PROJECT_IN_EXAM_SET_API),
    path('project-in-exam/<int:id>/requirement', REQUIREMENT_SET_API),
    path('project-in-exam/<int:id>/question', QUESTION_SET_API),
    path('project-in-exam/<int:id>/download-error-info', download_error_info),
    path('project-in-exam/<int:id>/csv', get_project_in_exam_csv),
    path('project-in-exam/<int:exam_id>/exam', get_exam_projects),

    path('fail-analysis/<int:project_id>', get_project_fail_analysis),
    path('fail-analysis/<int:project_id>/update', update_project_fail_analysis),

    path('exam-record/<int:query_id>', EXAM_RECORD_DETAIL_API),
    path('exam-record-problem-detail/<int:query_id>', EXAM_RECORD_PROBLEM_DETAIL_API),
    path('exam-record', EXAM_RECORD_SET_API),
    path('exam-record/export_all_exam_records/<int:course_id>', export_all_exam_records),
    path('exam-record/export_final_exam_records/<int:course_id>', export_final_exam_records),
    path('exam-record/delay_minute', EXAM_RECORD_DELAY_API),

    path('exam-queue/<int:room_id>', QUEUE_HEAD_API),
    path('exam-queue/<str:student_id>/<int:project_in_exam_id>', QUEUE_PUSH_API),
    path('exam-queue-list/<int:room_id>', QUEUE_LIST_API),
    path('exam-queue/<int:room_id>/<str:student_id>/<int:project_in_exam_id>/cancelHandsUp',
         roll_back_push_student_in_queue),
    path('exam-queue/<int:room_id>/<str:student_id>/<int:project_in_exam_id>/cancelPop', QUEUE_POP_API),

    path('progress/<int:course_id>/init', initialize_progress),
    path('progress/<int:course_id>/retake-init', initialize_retake_progress),
    path('progress/<int:course_id>/underclass', update_students_under_class_progress),
    path('progress/<int:course_id>/exam/<int:student_id>', check_in_exam),
    path('progress/<int:course_id>/skip/<str:student_id>', skip_to_next_project_in_exam),
    path('check-judge-permission', CHECK_JUDGE_PERMISSION_API),
    path('progress/<int:course_id>', STUDENT_PROGRESS_SET_API),
    path('progress/<int:course_id>/csv', list_student_progress_csv_output),
    path('progress/statistics/<int:exam_pk>', STUDENT_PROGRESS_STATISTICS_API),
    path('progress/statistics-combine', get_combined_student_progress_statistics),
    path('progress/statistics-combine-class', get_combined_student_progress_statistics_byclass),
    path('progress/<int:course_id>/reset-retake-progress', reset_retake_progress),
    path('progress/<int:course_id>/achievement', list_student_progress_achievement),
    path('progress/<int:course_id>/achievement/csv', list_student_progress_achievement_csv_output),
    path('progress/<int:course_id>/class', CLASS_PROGRESS_API),
    path('progress/<int:course_id>/no-retake-class', NO_RETAKE_CLASS_PROGRESS_API),
    path('progress/<int:course_id>/exam-student/<int:exam_id>', get_students_exam_record),

    path('user/create', create_user),
    path('user/delete', delete_user),
    path('user/change-password', change_password),
    path('user', list_users),
    path('user/<int:user_id>', USER_DETAIL_API),
    path('user/permission', list_permissions),

    path('user-group/create', create_user_group),
    path('user-group/delete', delete_user_group),
    path('user-group', list_groups),
    path('user-group/<int:group_id>', USER_GROUP_DETAIL_API),

    path('exam-check-in/<str:student_id>', EXAM_CHECK_IN_API),
    path('exam-check-out/<str:student_id>', EXAM_CHECK_OUT_API),
    path('exam-clear-room', EXAM_CLEAR_ROOM_API),
    path('push-all-student-after-exam/<int:course_pk>', PUSH_ALL_STUDENT_AFTER_EXAM_API),
    path('push-all-student-before-exam/<int:course_pk>', PUSH_ALL_STUDENT_BEFORE_EXAM_API),
    path('get-qualification-list-of-pie', get_qualification_list_of_pie),
    path('exams/<int:exam_id>/students', retrieve_students_in_exam),
    path('project/<int:id>/whitelist', PROJECT_WHITELIST_API),
    path('wechat-get-student-exam-grade-list/<str:student_id>', WECHAT_GET_GRADE_LIST_API),
    path('wechat-get-student-pass-under-class/<str:student_id>', get_student_pass_under_class),
    path('non_submit_student', get_non_submit_student_list),
    path('roll-back-check-in/<str:student_id>', EXAM_ROLL_BACK_CHECK_IN_API),
    path('roll-back-check-out/<str:student_id>', EXAM_ROLL_BACK_CHECK_OUT_API),
    path('test-hand-up/<int:room_pk>/<str:student_id>', HAND_UP_TEST_API),
    path('test-hand-up-in-exam', test_hand_up_in_exam),

    path('courses/<int:course_id>/statistics/final', final_statistics),
    path('courses/<int:course_id>/statistics/final/distribution', final_statistics_distribution),

    path('question/list', QUESTION_LIST_API),
    path('question/item', QUESTION_CRUD_API),

    path('news', NEWS_API),

    path('push-message', PUSH_MESSAGE_SET_API),
    path('push-message/<int:query_id>', PUSH_MESSAGE_DETAIL_API),
    path('redo-push-email-message/<int:query_id>', REDO_PUSH_EMAIL_MESSAGE_DETAIL_API),
    path('push-message-test/<int:query_id>', do_push_push_test),
    path('tag', TAG_DETAIL_API),
    path('tag_list', TAG_API),
]
