"""
Room Form
"""

from django import forms


class RoomInfo(forms.Form):
    """Deal with room data from post/put request.

    Attributes:
        Almost the same as Room Model.
    """

    # Required
    name = forms.CharField(max_length=50)
    available = forms.BooleanField(required=False)

    # Optional
    # we get charField actually
    comment = forms.CharField(max_length=120, required=False)
    # seats should be validated by another form
