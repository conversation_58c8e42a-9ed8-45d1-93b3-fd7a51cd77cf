"""
declare Course model
"""

from django.db import models
from django.contrib.auth import get_user_model

from core.models.permissions import (COURSE_CHANGE, COURSE_CREATE,
                                     COURSE_DELETE, COURSE_VIEW, COURSE_EXPORT_FINAL_RESULTS)

DEFAULT_TUTORIAL_SITE = "http://192.168.0.187:8301/"


class Course(models.Model):
    """This model describes a course.

    此模型用于描述教学课程。

    Course is different from class, and it is about period.
    For example, Course In 2019 Autumn.

    Course 与 Class 不同, 这是一个与某段时间相关联的实体。
    譬如, "2019年秋" 课程表明其开课时间是 2019 年秋季。

    Attributes:
        code: A <PERSON>r<PERSON>ield storing the code of course, which is somehow the same as name.
        name: A CharField storing the name of course, which is read by human.

    属性:
        code: Char<PERSON>ield, 用于存储课程代号，与 name 的用途基本相同
        name: <PERSON><PERSON><PERSON><PERSON>, 用于存储课程名称
    """
    code = models.Char<PERSON>ield(max_length=100)
    name = models.CharField(max_length=50)
    retake_students = models.ManyToManyField(
        to='Student', related_name='+')

    # 2019/10/11 01:59 updated by Dailan He
    # the default backward relation name of this field is 'Exam.course'
    # which clashes with an existing field (the 'course' property of Exam)
    # so we have to handle the conflict
    # in fact, we do not need a backward relation here
    # so set related_name to '+' to disable the relation
    # see:
    # https://docs.djangoproject.com/en/dev/ref/models/fields/#django.db.models.ForeignKey.related_name
    under_class_exam = models.ForeignKey(to="Exam", on_delete=models.CASCADE, null=True,
                                         related_name='+')
    allowed_users = models.ManyToManyField(get_user_model())

    tutorial_site = models.URLField(default=DEFAULT_TUTORIAL_SITE)

    opening = models.BooleanField(default=False)

    class Meta:
        default_permissions = ()
        permissions = [
            (COURSE_CREATE, COURSE_CREATE),
            (COURSE_CHANGE, COURSE_CHANGE),
            (COURSE_VIEW, COURSE_VIEW),
            (COURSE_DELETE, COURSE_DELETE),
            (COURSE_EXPORT_FINAL_RESULTS, COURSE_EXPORT_FINAL_RESULTS)
        ]
