<template>
  <card style="width: 350px; margin: 20px">
    <p slot="title" style="margin-top: 4px; margin-bottom: 4px">
      {{ name }}
    </p>
    <Row>
      <Col style="margin-bottom: 20px">
        <p style="text-align: center">课程编号: {{ code }}</p>
      </Col>
    </Row>
    <Row>
      <Col style="margin-bottom: 20px">
        <p style="text-align: center">课程ID: {{ id }}</p>
      </Col>
    </Row>
    <Row style="text-align: center; margin-bottom: 10px; display: flex" justify="center">
      <Col span="7">
        <p class="operation" @click="onShow">查看课下</p>
      </Col>
      <Col span="1">|</Col>
      <Col span="7">
        <p class="operation" @click="onUpdate">更新课程</p>
      </Col>
      <Col span="1">|</Col>
      <Col span="7">
        <p class="operation" @click="onDelete">删除课程</p>
      </Col>
    </Row>
  </card>
</template>

<script>
import { courseIdReq } from '@/api/course'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'CourseCard',
  props: {
    name: {
      type: String,
      default: 'xxx 课程'
    },
    id: {
      type: Number,
      default: 1111
    },
    code: {
      type: String,
      default: 'ACODE'
    }
  },
  data() {
    return {
      columns: [
        {
          title: '课程名称',
          key: 'name'
        },
        {
          title: '课程编号',
          key: 'code'
        },
        {
          title: '课程ID',
          key: 'id'
        }
      ],
      showControl: false
    }
  },
  methods: {
    onShow() {
      courseIdReq('get', this.id, {})
        .then((res) => {
          if (res.data.under_class_exam !== -1) {
            this.$router.push({ name: 'exam_detail', params: { id: res.data.under_class_exam } })
          } else {
            this.$Notice.info({ title: '该课程无对应的课下考试' })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onUpdate() {
      this.$router.push({ name: 'course_update', params: { id: this.id } })
    },
    onDelete() {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          courseIdReq('delete', this.id, {})
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.$emit('delete')
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.operation {
  color: #18bc9c;
  background-color: transparent;
}

.operation:hover {
  color: #0f7864;
  text-decoration: underline;
}
</style>
