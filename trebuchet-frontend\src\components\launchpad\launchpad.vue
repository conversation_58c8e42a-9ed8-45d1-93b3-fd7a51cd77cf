<template>
  <div v-if="!isReloading">
    <Row
      v-for="(ki, i) in row"
      :key="ki"
      type="flex"
      justify="space-between"
      :style="{ width: String(boardWidth) + 'px' }"
    >
      <div v-for="(kj, j) in col" :key="kj" style="padding: 0">
        <Button
          :type="getType(i, j)"
          :style="{ height: btnSizeStr, width: btnSizeStr, padding: '0', 'border-style': 'solid' }"
          :class="getClass(i, j)"
          @click="onBtnClick(i, j)"
        >
          <div :style="textStyle">
            {{ getText(i, j) }}
          </div>
          <div v-if="getSubTitle(i, j)" :style="subtitleStyle" v-html="getSubTitle(i, j)" />
        </Button>
      </div>
    </Row>
  </div>
</template>

<script>
import _ from 'lodash'

export default {
  name: 'Launchpad',
  props: {
    value: {
      type: Object,
      default: () => ({ row: 3, col: 3, data: _.times(9, _.constant({ text: '', type: 'default' })) })
    },
    span: {
      type: Number,
      default: -1
    },
    fontSize: {
      type: Number,
      default: -1
    },
    fontFactor: {
      type: Number,
      default: 1.0
    },
    subtitleFontFactor: {
      type: Number,
      default: 0.6
    },
    width: {
      type: Number,
      default: -1
    },
    height: {
      type: Number,
      default: -1
    },
    scale: {
      type: Number,
      default: 1
    },
    limitHeight: {
      type: Boolean,
      default: false
    },
    disable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isReloading: false,
      clientHeight: 900,
      clientWidth: 1080,
      resizeSleeping: false
    }
  },
  computed: {
    boardWidth() {
      return this.btnSizeNumber * this.col
    },
    btnSizeNumber() {
      const _h = this.height < 0 ? this.clientHeight : this.height
      const _w = this.width < 0 ? this.clientWidth : this.width
      const beforeScaled = this.limitHeight ? Math.min(_w / this.col, _h / this.row) : _w / this.col
      const size = this.scale * beforeScaled
      return Math.floor(size)
    },
    btnSizeStr() {
      return String(this.btnSizeNumber) + 'px'
    },
    col() {
      return this.value.col
    },
    row() {
      return this.value.row
    },
    fontSizeFixed() {
      const fontSize = this.fontSize >= 0 ? this.fontSize : Math.floor(this.btnSizeNumber / 3)
      return this.fontFactor * fontSize
    },
    textStyle() {
      return { fontSize: String(this.fontSizeFixed) + 'px' }
    },
    subtitleStyle() {
      return { fontSize: String(this.fontSizeFixed * this.subtitleFontFactor) + 'px' }
    }
  },
  watch: {
    value(newVal) {
      if (newVal.row * newVal.col !== newVal.data.length) {
        console.error('row * col !== length')
      }
      this.reload()
    }
  },
  mounted() {
    setTimeout(() => {
      this.clientHeight = document.documentElement.clientHeight
      this.clientWidth = this.$el.clientWidth
    }, 500)
    const _onresize = window.onresize
    window.onresize = (argv) => {
      if (_onresize) {
        _onresize(argv)
      }
      if (this.resizeSleeping === false) {
        this.resizeSleeping = setTimeout(() => {
          this.clientHeight = document.documentElement.clientHeight
          this.clientWidth = this.$el.clientWidth
          this.resizeSleeping = false
        }, 200)
      }
    }
  },
  methods: {
    onBtnClick(i, j) {
      if (!this.disable) {
        const index = this.getIndex(i, j)
        this.$emit('on-click', { i, j, index })
      }
    },
    getIndex(i, j) {
      return i * this.col + j
    },
    getData(i, j) {
      return this.value.data[this.getIndex(i, j)]
    },
    getText(i, j) {
      return this.getData(i, j).text || ''
    },
    getSubTitle(i, j) {
      return this.getData(i, j).subtitle
    },
    getType(i, j) {
      if (this.getData(i, j).type !== 'launchpad-button-notCheckIn') {
        return this.getData(i, j).type || 'default'
      } else {
        return 'text'
      }
    },
    getClass(i, j) {
      if (this.getData(i, j).type === 'launchpad-button-notCheckIn') {
        return 'launchpad-button-notCheckIn'
      }
    },
    async reload() {
      this.isReloading = true
      await this.$nextTick()
      this.isReloading = false
    }
  }
}
</script>

<style lang="less" scoped>
.launchpad-button-notCheckIn {
  color: #fff;
  background-color: #808080;
  border-color: #fff;
}

.launchpad-button-notCheckIn:hover {
  color: #fff;
  background-color: #dcdcdc;
  border-color: #dcdcdc;
}
</style>
