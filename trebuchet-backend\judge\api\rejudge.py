"""
rejudge
"""

from django.db.models.functions import datetime
from django.http import HttpRequest
from django.views.decorators.http import require_POST, require_GET
from django.forms import model_to_dict
from django.db.models import Q

from core.api.auth import jwt_auth
from core.api.utils import (ErrorCode, failed_api_response,
                            response_wrapper, success_api_response,
                            wrapped_api, parse_data, validate_args)
from core.api.query_utils import query_filter
from judge.api.permissions import JUDGE_REJUDGE
from judge.constants import QUEUEING
from judge.models import ProblemJudgeRecord, RejudgedProblemJudgeRecord, TestCaseJudgeRecord, \
    RejudgedTestCaseJudgeRecord


def validate_single_rejudge_request(request: HttpRequest) -> bool:
    """
    validate rejudge request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"record_id"}
    if not data.keys() <= allowed_fields:
        return False
    record_id = data.get("record_id")
    if record_id is None or record_id == "":
        return False
    if not isinstance(data.get('record_id'), int) and not isinstance(data.get('record_id'), str):
        return False
    return True


def validate_batch_rejudge_request(request: HttpRequest) -> bool:
    """
    validate rejudge request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"record_ids", "rejudge_comment"}
    if not data.keys() <= allowed_fields:
        return False
    record_ids = data.get("record_ids")
    if record_ids is None or record_ids == "":
        return False
    if not isinstance(data.get('record_ids'), list):
        return False
    if not isinstance(data.get('rejudge_comment'), str):
        return False
    return True


@response_wrapper
@jwt_auth(perms=[JUDGE_REJUDGE], whitelisted_tokens=[])
@require_POST
@validate_args(func=validate_single_rejudge_request)
def single_rejudge(request: HttpRequest):
    body = parse_data(request)
    record_id = body.get('record_id', None)
    return success_api_response({'data': _inline_rejudge(record_id)})


@response_wrapper
@jwt_auth(perms=[JUDGE_REJUDGE], whitelisted_tokens=[])
@require_POST
@validate_args(func=validate_batch_rejudge_request)
def batch_rejudge(request: HttpRequest):
    body = parse_data(request)
    record_id_set = set(body.get('record_ids', None))
    rejudge_comment = body.get('rejudge_comment', None)
    if rejudge_comment is None or rejudge_comment == "":
        rejudge_comment = 'None'
    batch_id = str(datetime.datetime.now().strftime('%Y-%m-%d-%H:%M:%S')) + '_' + rejudge_comment
    data = []
    for record_id in record_id_set:
        data.append(_inline_rejudge(record_id, batch_id))
    return success_api_response({'batch_id': batch_id,
                                 'data': data})


def _inline_rejudge(record_id, batch_id=None):
    try:
        record = ProblemJudgeRecord.objects.get(id=record_id)
    except ProblemJudgeRecord.DoesNotExist:
        return {'result': 'Error! The record_id ' + str(record_id) + ' does not exist!'}
    cases_records = TestCaseJudgeRecord.objects.filter(problem_judge_record__exact=record).all()
    rejudged_problem_judge_record = RejudgedProblemJudgeRecord.objects.create(
        original_record=record,
        edx_username=record.edx_username,
        problem=record.problem,
        attachment=record.attachment,
        student_comment=record.student_comment,
        judger_identifier=record.judger_identifier,
        judge_result=record.judge_result,
        course_code=record.course_code,
        chapter_code=record.chapter_code,
        project_in_exam=record.project_in_exam,
        submitted_at=datetime.datetime.now(),
        started_at=record.started_at,
        finished_at=record.finished_at,
        created_at=datetime.datetime.now(),
        tool_chain=record.tool_chain,
        batch_id=batch_id
    )
    rejudged_problem_judge_record.save()
    for case_record in cases_records:
        RejudgedTestCaseJudgeRecord.objects.create(
            problem_judge_record=rejudged_problem_judge_record,
            test_case=case_record.test_case,
            judge_result=case_record.judge_result,
            raw_output=case_record.raw_output,
            comment=case_record.comment,
            started_at=case_record.started_at,
            finished_at=case_record.finished_at
        )
        case_record.delete()
    record.judge_result = QUEUEING
    record.save()
    return {'result': 'success! The record_id ' + str(record_id) + ' is rejudging!'}


@response_wrapper
@jwt_auth(perms=[JUDGE_REJUDGE], whitelisted_tokens=[])
@require_GET
@query_filter(fields=[("batch_id", str)])
def list_batch_rejudge_record(request: HttpRequest, **kwargs):
    """根据batch_id获得对应批次的重测记录
    Arguments:
        request {HttpRequest} -- GET
    Returns:
        failed/success -- failed/success to batch rejudge
    """
    try:
        batch_id_filter = kwargs.get('filter')
    except AttributeError:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, 'The request is illegal!')
    if not batch_id_filter:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, 'The batch_id is required!')
    # 原始评测记录被存储在RejudgedProlemJudgeRecord中
    original_record_set = RejudgedProblemJudgeRecord.objects.filter(batch_id_filter)
    if not original_record_set.exists():
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, 'The batch id does not exist!')
    # 将重测结果不同的两条记录以字典形式返回
    ret_record_pair_dict = {}
    for original_record in original_record_set:
        rejudge_record_set = RejudgedProblemJudgeRecord.objects.filter(
            pk__gt=original_record.pk,
            original_record__pk=original_record.original_record.pk)
        if not rejudge_record_set.exists():
            # 只有一次重测的记录直接从ProblemJudgeRecord中得到新的重测记录
            rejudge_record = ProblemJudgeRecord.objects.get(pk=original_record.original_record.pk)
        else:
            # 多次重测的记录需要在RejudgedProlemJudgeRecord寻找上一次重测记录
            rejudge_record = rejudge_record_set.order_by('pk').first()
        # 结果相同的重测记录不需要返回
        if rejudge_record.judge_result == original_record.judge_result:
            continue
        ret_record_pair_dict[str(original_record.pk)] = {
            'judge_record_data': model_to_dict(original_record),
            'rejudge_record_data': model_to_dict(rejudge_record)
        }
    return success_api_response({
        'all_record_count': len(original_record_set),
        'varied_record_count': len(ret_record_pair_dict),
        'records': ret_record_pair_dict})


@response_wrapper
@jwt_auth(perms=[JUDGE_REJUDGE])
@require_GET
def list_batch_id(request: HttpRequest):
    """获取现在已经创建的所有batch_id
    Arguments:
        request {HttpRequest} -- GET
    Returns:
        failed/success -- failed/success to get batch_id_list
    """
    batch_id_list = list(
        RejudgedProblemJudgeRecord.objects
            .exclude(Q(batch_id=None) | Q(batch_id=''))
            .values_list('batch_id', flat=True)
            .distinct())
    return success_api_response({
        'batch_id_list': batch_id_list
    })


REJUDGE_RECORD_API = wrapped_api({
    "POST": single_rejudge
})

BATCH_REJUDGE_RECORD_API = wrapped_api({
    "GET": list_batch_rejudge_record,
    "POST": batch_rejudge
})
