"""
admin classes for core models
"""
from django.contrib.admin import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>an<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ist<PERSON>ilter
from django.contrib.admin.decorators import register

from .models.announcement import Announcement
from .models.user_profile import UserProfile
from .models.omni_config import OmniConfig
from .models.student import Student


@register(Student)
class StudentAdmin(ModelAdmin):
    """
    admin for Student
    """


@register(UserProfile)
class UserProfileAdmin(ModelAdmin):
    """
    admin for User Profile
    """


@register(OmniConfig)
class OmniConfigAdmin(ModelAdmin):
    """
    admin for Omni Config
    """
    list_display = ('name', 'content_view', 'created_at', 'created_by', 'enabled')
    list_filter = (
        ('enabled', BooleanFieldListFilter),
        ('created_by', RelatedOnlyFieldListFilter),
    )
    search_fields = ['name', 'content']
    ordering = ['name', 'created_at']

    def content_view(self, obj):
        return obj.content


@register(Announcement)
class AnnouncementAdmin(ModelAdmin):
    """
    admin for Announcement Profile
    """
