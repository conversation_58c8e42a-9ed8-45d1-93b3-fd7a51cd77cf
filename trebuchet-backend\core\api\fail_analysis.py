import json

from django.http import HttpRequest
from django.views.decorators.http import require_GET, require_POST

from core.api.utils import (
    response_wrapper,
    success_api_response,
    failed_api_response,
    ErrorCode,
    parse_data
)
from core.api.auth import jwt_auth
from core.api.permissions import CORE_PROGRESS_VIEW
from core.models import ProjectInExam


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
def get_project_fail_analysis(request: HttpRequest, project_id: int):
    """
    获取**已经缓存在数据库中的错误信息**，而不是主动进行更新
    [method]: GET
    [route]: /api/progress/<int:course_id>/exam-student/<int:student_id>
    """
    pie = ProjectInExam.objects.get(pk=project_id)
    if pie is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Project not found.")
    # 防止为空
    analysis_text = pie.fail_analysis or "{}"

    try:
        parsed_analysis = json.loads(analysis_text.strip())
        data = {"analysis": parsed_analysis}
        return success_api_response(data)
    except json.JSONDecodeError as error:
        return failed_api_response(
            ErrorCode.INVALID_REQUEST_ARGUMENT_ERROR,
            f"Failed to parse analysis data: {str(error)}"
        )


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_POST
def update_project_fail_analysis(request: HttpRequest, project_id: int):
    """
    更新错误信息，耗时较久，需要爬取大量数据
    [method]: POST
    [route]: /api/progress/<int:course_id>/exam-student/<int:student_id>
    """
    # 这里暂时为了过风格测试，实际上的逻辑应该是主动计算错误信息
    pie = ProjectInExam.objects.get(pk=project_id)
    data = parse_data(request)
    analysis_text = data.get("analysis")
    pie.fail_analysis = analysis_text

    return success_api_response(None)
