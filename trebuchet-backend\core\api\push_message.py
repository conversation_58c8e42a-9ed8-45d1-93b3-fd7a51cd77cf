import smtplib
from contextlib import suppress
from email.header import Header
from email.mime.text import MIMEText

from django.core.exceptions import FieldError, ValidationError
from django.core.paginator import Paginator
from django.db.models import QuerySet, Q
from django.http import HttpRequest
from django.utils import timezone
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_PUSH_MSG_VIEW, CORE_PUSH_MSG_EDIT, CORE_PUSH_MSG_PUSH)
from core.api.query_utils import (query_filter, query_order_by, query_page)
from core.api.utils import (ErrorCode, add_notification_for_user, failed_api_response, parse_data,
                            require_item_exist, response_wrapper, send_email_to_students,
                            success_api_response, validate_args, wrapped_api, require_course_permission)
from core.models.course import Course
from core.models.push_message import (PUSH_STATUS_ERROR, PUSH_STATUS_NOT_PUSHED, PUSH_STATUS_SUCCESS,
                                      PushMessage, MESSAGE_TYPE_HTML)
from core.models.push_message_record import (PushMessageRecord, MSG_PUSH_STATUS_NOT_PUSHED,
                                             MSG_PUSH_STATUS_PUSHED, MSG_PUSH_STATUS_ERROR)
from core.models.student import Student
from core.models.user_profile import UserProfile
from trebuchet import settings


def _validate_create_push_message(request: HttpRequest) -> bool:
    """
    validate create push message request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"message_type", "title", "content", "comment",
                      "with_email", "with_web_notification", "recipients"}
    if not data.keys() <= allowed_fields:
        return False
    # 检查各字段属性
    data_valid = True
    # 消息类型 必填 类型值之一
    message_type = data.get("message_type", None)
    if (message_type is None) or (message_type not in [MESSAGE_TYPE_HTML]):
        data_valid = False
    # title 必填 非空字符串
    title = data.get("title", None)
    if (title is None) or (not isinstance(title, str)) or (title.strip() == "") or (len(title) > 50):
        data_valid = False
    # content 必填 非空字符串
    content = data.get("content", None)
    if (content is None) or (not isinstance(content, str)) or (content.strip() == ""):
        data_valid = False
    # recipients 必填 字符串列表
    recipients = data.get("recipients", None)
    if (recipients is None) or (not isinstance(recipients, list)):
        data_valid = False
    for stu_id in recipients:
        if not isinstance(stu_id, str):
            data_valid = False
    # 是否发送电子邮件 必填 布尔值
    with_email = data.get("with_email", None)
    if (with_email is None) or (not isinstance(with_email, bool)):
        data_valid = False
    # 是否发送cscore铃铛推送 必填 布尔值
    with_web_notification = data.get("with_web_notification", None)
    if (with_web_notification is None) or (not isinstance(with_web_notification, bool)):
        data_valid = False
    # 至少选中一种推送方式
    if data_valid and not (with_email or with_web_notification):
        data_valid = False
    # 消息说明 可选 字符串
    comment = data.get("comment", "")
    if (comment is None) or (not isinstance(comment, str)) or (len(comment) > 150):
        data_valid = False
    return data_valid


def _validate_update_push_message(request: HttpRequest) -> bool:
    """
    validate create push message request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"message_type", "title", "content", "comment",
                      "with_email", "with_web_notification", "recipients"}
    if not data.keys() <= allowed_fields:
        return False
    # 检查各字段属性
    data_valid = True
    # 消息类型 非必须更改 类型值之一
    message_type = data.get("message_type", MESSAGE_TYPE_HTML)
    if (message_type is None) or (message_type not in [MESSAGE_TYPE_HTML]):
        data_valid = False
    # title 非必须更改 非空字符串
    title = data.get("title", "non-empty-string")
    if (title is None) or (not isinstance(title, str)) or (title.strip() == "") or (len(title) > 50):
        data_valid = False
    # content 非必须更改 非空字符串
    content = data.get("content", "non-empty-string")
    if (content is None) or (not isinstance(content, str)) or (content.strip() == ""):
        data_valid = False
    # recipients 非必须更改 字符串列表
    recipients = data.get("recipients", [])
    if (recipients is None) or (not isinstance(recipients, list)):
        data_valid = False
    for stu_id in recipients:
        if not isinstance(stu_id, str):
            data_valid = False
    # 是否发送电子邮件 非必须更改 布尔值
    with_email = data.get("with_email", False)
    if (with_email is None) or (not isinstance(with_email, bool)):
        data_valid = False
    # 是否发送cscore铃铛推送 非必须更改 布尔值
    with_web_notification = data.get("with_web_notification", None)
    if (with_web_notification is None) or (not isinstance(with_web_notification, bool)):
        data_valid = False
    # 消息说明 非必须更改 字符串
    comment = data.get("comment", "")
    if (comment is None) or (not isinstance(comment, str)) or (len(comment) > 150):
        data_valid = False
    return data_valid


def find_students_by_id(student_ids: list):
    student_list = []
    unknown_students = []
    for stu_id in student_ids:
        query = Student.objects.filter(student_id=stu_id)
        if query.exists():
            student_list.append(query[0])
        else:
            unknown_students.append(stu_id)
    return student_list, unknown_students


@response_wrapper
@jwt_auth(perms=[CORE_PUSH_MSG_EDIT])
@require_POST
@validate_args(func=_validate_create_push_message)
def create_push_message(request: HttpRequest):
    """create a push message

    [method]: POST

    [route]: /api/push-message
    """
    data: dict = parse_data(request)
    # 找出学生 若有学生不存在，报错返回
    student_list, unknown_students = find_students_by_id(data['recipients'])
    if len(unknown_students) != 0:
        unknown_students_str = ", ".join(unknown_students)
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "找不到学生: " + unknown_students_str)

    del data['recipients']

    current_user_profile = UserProfile.objects.filter(
        user=request.user).first()
    if current_user_profile and current_user_profile.course:
        data['course'] = current_user_profile.course
    data['created_by'] = request.user

    push_message: PushMessage = PushMessage.objects.create(**data)
    # 添加学生关联
    push_message.recipients.set(student_list)
    push_message.save()
    return success_api_response({'success': True, 'push_message_id': push_message.pk})


@response_wrapper
@jwt_auth(perms=[CORE_PUSH_MSG_VIEW])
@require_GET
@require_course_permission
@query_filter(fields=[('message_type', int), ('title', str), ('content', str), ('comment', str),
                      ('with_email', bool), ('with_web_notification', bool), ('status', int), ('created_by', str)])
@query_order_by(fields=['status', 'created_at', 'pushed_at'])
@query_page(default=10)
def list_push_message(request: HttpRequest, course: Course, *args, **kwargs):
    """list push-messages

    [method]: GET

    [route]: /api/exam-record
    """
    models_all = PushMessage.objects.count()
    models: QuerySet = PushMessage.objects.filter(course=course)
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered).filter(course=course)
    except FieldError:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR,
                                   "Unsupported Filter Method.")
    # order by
    order_by = kwargs.get('order_by')
    try:
        if order_by is not None:
            order_by.append('-id')
            models = models.order_by(*order_by)
        else:
            models = models.order_by('-id')
    except FieldError:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR,
                                   'Unsupported Order Method')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'message_type', 'title', 'content', 'comment', 'with_email',
                'with_web_notification', 'status', 'created_at', 'created_by__username', 'pushed_at'
            ))
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PUSH_MSG_VIEW])
@require_GET
@require_item_exist(model=PushMessage, field='id', item='query_id')
def get_push_message(request: HttpRequest, query_id: int):
    """get an specified push_message

    [method]: GET

    [route]: /api/push-message/<int:query_id>
    """
    push_message = PushMessage.objects.get(id=query_id)
    recipients_status = list(PushMessageRecord.objects.filter(push_message=push_message)
                             .values("id", "push_message__id", "student__student_id", "student__name",
                                     "student__user__email", "notification_status", "notification_confirmed_at",
                                     "email_status", "email_confirmed_at"))
    data = {
        "id": push_message.id,
        "message_type": push_message.message_type,
        "title": push_message.title,
        "content": push_message.content,
        "comment": push_message.comment,
        "with_email": push_message.with_email,
        "with_web_notification": push_message.with_web_notification,
        "status": push_message.status,
        "created_at": push_message.created_at,
        "created_by__username": push_message.created_by.username,
        "pushed_at": push_message.pushed_at,
        "recipients_status": recipients_status
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PUSH_MSG_EDIT])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_push_message)
@require_item_exist(model=PushMessage, field='id', item='query_id')
def update_push_message(request: HttpRequest, query_id: int):
    """update an specified push-message

    [method]: PUT

    [route]: /api/push-message/<int:query_id>
    """
    push_message: PushMessage = PushMessage.objects.get(id=query_id)
    if push_message.status != PUSH_STATUS_NOT_PUSHED:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Message already pushed")
    data: dict = parse_data(request)

    # message_type content comment可以自行覆盖
    student_list = []
    unknown_students = []
    recipients_changed = False
    if data.get("recipients", None) is not None:
        # 找出学生 若有学生不存在，报错返回
        student_list, unknown_students = find_students_by_id(data['recipients'])
        if len(unknown_students) != 0:
            unknown_students_str = ", ".join(unknown_students)
            return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "找不到学生: " + unknown_students_str)
        del data['recipients']
        recipients_changed = True

    # 确认with_email和with_web_notification至少一个开启
    with_email = data.get('with_email', push_message.with_email)
    with_web_notification = data.get('with_web_notification', push_message.with_web_notification)
    if not (with_email or with_web_notification):
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR)

    try:
        for key in data.keys():
            if data[key] is None:
                continue
            setattr(push_message, key, data[key])
        push_message.save()
    except TypeError:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR)
    except ValidationError:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR)

    if recipients_changed:
        # 重设学生关联
        push_message.recipients.clear()
        push_message.recipients.set(student_list)
        push_message.save()

    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[CORE_PUSH_MSG_PUSH])
@require_POST
@require_item_exist(model=PushMessage, field='id', item='query_id')
def do_push_push_message(request: HttpRequest, query_id: int):
    """push a push_message to student

    [method]: POST

    [route]: /api/push-message/<int:query_id>
    """

    # 防止测试时误操作，需要传入{"do_push": true}
    data: dict = parse_data(request)
    if data is None or data.get("do_push", False) is False:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR)

    push_message: PushMessage = PushMessage.objects.get(id=query_id)
    recipient_records: QuerySet = PushMessageRecord.objects.filter(push_message=push_message)

    failed_info_list = []

    # 课程通知推送
    notf_records = recipient_records.filter(
        notification_status__in=[MSG_PUSH_STATUS_NOT_PUSHED, MSG_PUSH_STATUS_ERROR])

    if push_message.with_web_notification:
        inbox_detail = {"notificationType": "push-msg",
                        "pushMsgExpandable": False,
                        "title": push_message.title,
                        "content": push_message.content}
        for record in notf_records:
            if record.student.user is None:
                failed_info_list.append((record.student, "No such user"))
                record.notification_status = MSG_PUSH_STATUS_ERROR
                record.save()
                continue
            notf = add_notification_for_user(record.student.user.id, push_message.title, inbox_detail)
            record.related_notification = notf
            record.notification_status = MSG_PUSH_STATUS_PUSHED
            record.save()

    # email推送
    mail_success = True
    email_records = recipient_records.filter(email_status__in=[MSG_PUSH_STATUS_NOT_PUSHED, MSG_PUSH_STATUS_ERROR])
    if push_message.with_email:
        student_list = list(map(lambda x: x.student, email_records))
        mail_success, email_failed_list = send_email_to_students(student_list, push_message.title, push_message.content)
        if mail_success:
            failed_info_list.extend(email_failed_list)
            email_failed_students = list(map(lambda x: x[0], email_failed_list))
            email_records.filter(student__in=email_failed_students).update(email_status=MSG_PUSH_STATUS_ERROR)
            email_records.exclude(student__in=email_failed_students).update(email_status=MSG_PUSH_STATUS_PUSHED)
        else:
            email_records.all().update(email_status=MSG_PUSH_STATUS_ERROR)

    push_message.status = PUSH_STATUS_SUCCESS if (mail_success and len(failed_info_list) == 0) else PUSH_STATUS_ERROR
    push_message.pushed_at = timezone.now()
    push_message.save()

    failure_list: list[tuple[str, str]] = list(map(lambda x: (x[0].student_id, x[1]), failed_info_list))
    # 为了排查错误，显示错误详情
    # if not mail_success:
    #     failure_list = [("All", "All Email Failed")]

    data = {"status": push_message.status, "failure_list": failure_list}
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PUSH_MSG_PUSH])
@require_POST
@require_item_exist(model=PushMessage, field='id', item='query_id')
def redo_push_email_message(request: HttpRequest, query_id: int):
    """redo push email message for those who failed to receive e-mail

    [method]: POST


    [route]: /api/redo-push-email-message/<int:query_id>
    """
    data: dict = parse_data(request)
    if data is None or not data.get("redo_push", False):
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "invalid data or redo_push check")

    push_message: PushMessage = PushMessage.objects.get(id=query_id)
    if not push_message.with_email:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "message without email")
    target_records: QuerySet = PushMessageRecord.objects.filter(push_message=push_message,
                                                                email_status=MSG_PUSH_STATUS_ERROR)
    failed_info_list = []

    student_list = list(map(lambda x: x.student, target_records))
    mail_success, email_failed_list = send_email_to_students(student_list, push_message.title, push_message.content)
    if mail_success:
        failed_info_list.extend(email_failed_list)
        email_failed_students = list(map(lambda x: x[0], email_failed_list))
        target_records.filter(student__in=email_failed_students).update(email_status=MSG_PUSH_STATUS_ERROR)
        target_records.exclude(student__in=email_failed_students).update(email_status=MSG_PUSH_STATUS_PUSHED)
    else:
        target_records.all().update(email_status=MSG_PUSH_STATUS_ERROR)

    push_message.status = PUSH_STATUS_SUCCESS if not PushMessageRecord.objects.filter(push_message=push_message).filter(
        Q(email_status=MSG_PUSH_STATUS_ERROR) | Q(
            notification_status=MSG_PUSH_STATUS_ERROR)).exists() else PUSH_STATUS_ERROR
    push_message.save()

    failure_list: list[tuple[str, str]] = list(map(lambda x: (x[0].student_id, x[1]), failed_info_list))
    # 为了排查错误，显示错误详情
    # if not mail_success:
    #     failure_list = [("All", "All Email Failed")]
    data = {"status": push_message.status, "failure_list": failure_list}
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PUSH_MSG_PUSH])
@require_POST
@require_item_exist(model=PushMessage, field='id', item='query_id')
def do_push_push_test(request: HttpRequest, query_id: int):
    data: dict = parse_data(request)
    if data is None or data.get("do_push", False) is False:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "invalid data or push check")

    if data.get("email", None) is None:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "invalid email")

    email = data.get("email", None)

    push_message: PushMessage = PushMessage.objects.get(id=query_id)

    try:
        if settings.MAIL_SMTP_PORT == 465:
            smtp_connection = smtplib.SMTP_SSL(host=settings.MAIL_SMTP_HOST, port=settings.MAIL_SMTP_PORT)
        else:
            smtp_connection = smtplib.SMTP(host=settings.MAIL_SMTP_HOST, port=settings.MAIL_SMTP_PORT)
        smtp_connection.login(user=settings.MAIL_SMTP_USERNAME, password=settings.MAIL_SMTP_PASSWORD)
    except smtplib.SMTPException:
        return failed_api_response(ErrorCode.REFUSE_ACCESS)

    try:
        message = MIMEText(push_message.content, "html", "utf-8")
        message['Subject'] = Header(push_message.title, "utf-8")
        message['From'] = Header(settings.MAIL_SMTP_FROM)
        message['To'] = Header(email)
        smtp_connection.sendmail(settings.MAIL_SMTP_FROM, [email], message.as_string())
    except smtplib.SMTPException:
        with suppress(smtplib.SMTPException):
            smtp_connection.quit()
            return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "unrecognized email " + email)

    with suppress(smtplib.SMTPException):
        smtp_connection.quit()

    return success_api_response({'success': True, "response": "email has been send to " + email})


PUSH_MESSAGE_SET_API = wrapped_api({
    "get": list_push_message,
    "post": create_push_message
})

PUSH_MESSAGE_DETAIL_API = wrapped_api({
    "get": get_push_message,
    "put": update_push_message,
    "post": do_push_push_message
})

REDO_PUSH_EMAIL_MESSAGE_DETAIL_API = wrapped_api({
    "post": redo_push_email_message,
})
