"""
declare StudentSeat model
"""

from django.db import models

from core.models.exam import Exam
from core.models.permissions import (SEAT_RECORD_CHANGE, SEAT_RECORD_CREATE,
                                     SEAT_RECORD_DELETE, SEAT_RECORD_VIEW)
from core.models.seat import Seat
from core.models.student import Student


class StudentSeatRecord(models.Model):
    """A student-seat pair used to represent the mapping relationship in seating arrangements

    Attributes:
        student -- A ForeignKey to Student
        seat -- A ForeignKey to Seat
    """
    student = models.ForeignKey(to=Student, on_delete=models.CASCADE)
    seat = models.ForeignKey(to=Seat, on_delete=models.CASCADE)
    exam = models.ForeignKey(to=Exam, on_delete=models.CASCADE)

    class Meta:
        default_permissions = ()
        permissions = [
            (SEAT_RECORD_CHANGE, SEAT_RECORD_CHANGE),
            (SEAT_RECORD_CREATE, SEAT_RECORD_CREATE),
            (SEAT_RECORD_DELETE, SEAT_RECORD_DELETE),
            (SEAT_RECORD_VIEW, SEAT_RECORD_VIEW)
        ]
        constraints = [
            models.UniqueConstraint(fields=['exam', 'student'], name='no duplicate student in exam')
        ]
