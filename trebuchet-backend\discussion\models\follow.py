"""
declare Response model
"""

from django.db import models
from django.contrib.auth.models import User

from discussion.models import Discussion


class Follow(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=False, db_index=True
    )
    discussion = models.ForeignKey(
        Discussion, on_delete=models.CASCADE, null=False, db_index=True
    )
    created_at = models.DateTimeField(blank=False, null=False, auto_now_add=True)

    class Meta:
        unique_together = ("user_id", "discussion_id")
