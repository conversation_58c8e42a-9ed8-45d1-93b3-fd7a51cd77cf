"""
declare ExamQueue model
"""
from django.db import models
from django.contrib.auth import get_user_model

from core.models import ProjectInExam
from core.models.exam import Exam
from core.models.permissions import (EXAM_QUEUE_CHANGE, EXAM_QUEUE_CREATE,
                                     EXAM_QUEUE_DELETE, EXAM_QUEUE_VIEW, EXAM_QUEUE_TEST)
from core.models.room import Room
from core.models.student import Student


class ExamQueue(models.Model):
    """This model describes the relationships between queue and students.

    Attributes:
        student: a Database Foreignkey to Student.
        project_in_exam: a Database Foreignkey to ProjectInExam.
        room: a Database Foreignkey to Room.
    """
    student = models.ForeignKey(to=Student, on_delete=models.CASCADE)
    project_in_exam = models.ForeignKey(to=ProjectInExam, on_delete=models.CASCADE)
    exam = models.ForeignKey(to=Exam, on_delete=models.CASCADE)
    room = models.ForeignKey(to=Room, on_delete=models.CASCADE)
    pushed_at = models.DateTimeField(auto_now_add=True)
    popped_at = models.DateTimeField(null=True)
    valid = models.BooleanField(default=True)
    examinant = models.ForeignKey(
        to=get_user_model(), on_delete=models.SET_NULL, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['student', 'room', 'exam', 'project_in_exam'], name='queue_student_index'),
        ]
        default_permissions = ()
        permissions = [
            (EXAM_QUEUE_CREATE, EXAM_QUEUE_CREATE),
            (EXAM_QUEUE_CHANGE, EXAM_QUEUE_CHANGE),
            (EXAM_QUEUE_VIEW, EXAM_QUEUE_VIEW),
            (EXAM_QUEUE_DELETE, EXAM_QUEUE_DELETE),
            (EXAM_QUEUE_TEST, EXAM_QUEUE_TEST)
        ]
