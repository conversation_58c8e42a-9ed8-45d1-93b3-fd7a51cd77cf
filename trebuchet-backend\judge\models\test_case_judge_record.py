"""
declare TestCaseJudgeRecord model
"""
from django.db import models
from nose.tools import nottest

from judge.constants import TEST_CASE_RESULT_TAG_CHOICES
from judge.models.permissions import (CHANGE_TCJR, CREATE_TCJR, DELETE_TCJR,
                                      VIEW_TCJR, VIEW_TESTCASE_PASS)
from judge.models.problem_judge_record import ProblemJudgeRecord
from judge.models.test_case import TestCase


@nottest
class TestCaseJudgeRecord(models.Model):
    """
    Result of single test point.
    """
    problem_judge_record = models.ForeignKey(ProblemJudgeRecord, on_delete=models.CASCADE)
    test_case = models.ForeignKey(TestCase, on_delete=models.CASCADE)
    judge_result = models.IntegerField(choices=TEST_CASE_RESULT_TAG_CHOICES)
    raw_output = models.TextField(blank=True, null=True)
    comment = models.TextField()
    started_at = models.DateTimeField(blank=True, null=True)
    finished_at = models.DateTime<PERSON>ield(blank=True, null=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (CHANGE_TCJR, CHANGE_TCJR),
            (CREATE_TCJR, CREATE_TCJR),
            (DELETE_TCJR, DELETE_TCJR),
            (VIEW_TCJR, VIEW_TCJR),
            (VIEW_TESTCASE_PASS, VIEW_TESTCASE_PASS)
        ]
