{"ast": null, "code": "import { getErrModalOptions } from '@/libs/util';\nexport default {\n  name: 'ProgressDetection',\n  data() {\n    return {\n      activeTab: 'slow-detection',\n      // 进度慢检测\n      slowDetection: {\n        submissionThreshold: 5,\n        daysRange: 7,\n        loading: false,\n        data: []\n      },\n      slowDetectionColumns: [{\n        title: '学号',\n        key: 'student_id',\n        width: 120\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        width: 120\n      }, {\n        title: '提交次数',\n        key: 'submission_count',\n        width: 100,\n        render: (h, params) => {\n          const count = params.row.submission_count;\n          const color = count < 3 ? 'red' : count < 5 ? 'orange' : 'green';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, count);\n        }\n      }, {\n        title: '最后提交时间',\n        key: 'last_submission_time',\n        width: 160\n      }, {\n        title: '当前进度',\n        key: 'current_progress',\n        width: 120\n      }],\n      // 多次挂在同一个P检测\n      repeatedFailures: {\n        failureThreshold: 3,\n        loading: false,\n        data: []\n      },\n      repeatedFailuresColumns: [{\n        title: '学号',\n        key: 'student_id',\n        width: 120\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        width: 120\n      }, {\n        title: '当前P',\n        key: 'current_project',\n        width: 150\n      }, {\n        title: '失败次数',\n        key: 'failure_count',\n        width: 100,\n        render: (h, params) => {\n          const count = params.row.failure_count;\n          const color = count >= 5 ? 'red' : count >= 3 ? 'orange' : 'yellow';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, count);\n        }\n      }, {\n        title: '首次失败时间',\n        key: 'first_failure_time',\n        width: 160\n      }, {\n        title: '最近失败时间',\n        key: 'last_failure_time',\n        width: 160\n      }],\n      // 多次没有课上资格检测\n      qualificationFailures: {\n        failureThreshold: 2,\n        loading: false,\n        data: []\n      },\n      qualificationFailuresColumns: [{\n        title: '学号',\n        key: 'student_id',\n        width: 120\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        width: 120\n      }, {\n        title: '当前P',\n        key: 'current_project',\n        width: 150\n      }, {\n        title: '失去资格次数',\n        key: 'qualification_failure_count',\n        width: 120,\n        render: (h, params) => {\n          const count = params.row.qualification_failure_count;\n          const color = count >= 4 ? 'red' : count >= 2 ? 'orange' : 'yellow';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, count);\n        }\n      }, {\n        title: '最近一次课上考试',\n        key: 'last_exam_date',\n        width: 160\n      }, {\n        title: '参加状态',\n        key: 'participation_status',\n        width: 100,\n        render: (h, params) => {\n          const status = params.row.participation_status;\n          const color = status === '未参加' ? 'red' : 'green';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, status);\n        }\n      }]\n    };\n  },\n  mounted() {\n    this.loadSlowDetection();\n  },\n  methods: {\n    onTabChange(name) {\n      this.activeTab = name;\n      // 根据切换的标签页加载对应数据\n      switch (name) {\n        case 'slow-detection':\n          if (!this.slowDetection.data.length) {\n            this.loadSlowDetection();\n          }\n          break;\n        case 'repeated-failures':\n          if (!this.repeatedFailures.data.length) {\n            this.loadRepeatedFailures();\n          }\n          break;\n        case 'qualification-failures':\n          if (!this.qualificationFailures.data.length) {\n            this.loadQualificationFailures();\n          }\n          break;\n      }\n    },\n    // 加载进度慢检测数据\n    async loadSlowDetection() {\n      if (!this.currentCourseId) {\n        this.$Message.warning('请先选择当前课程');\n        return;\n      }\n      this.slowDetection.loading = true;\n      try {\n        const params = {\n          submission_threshold: this.slowDetection.submissionThreshold,\n          days_range: this.slowDetection.daysRange\n        };\n        const res = await slowDetectionReq(this.currentCourseId, params);\n        this.slowDetection.data = res.data.students || [];\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n        // 如果API调用失败，使用模拟数据作为展示\n        this.slowDetection.data = [{\n          student_id: '20231001',\n          student_name: '张三',\n          submission_count: 2,\n          last_submission_time: '2024-01-15 14:30:00',\n          current_progress: 'P1-课下'\n        }, {\n          student_id: '20231002',\n          student_name: '李四',\n          submission_count: 1,\n          last_submission_time: '2024-01-14 09:15:00',\n          current_progress: 'P1-课下'\n        }, {\n          student_id: '20231003',\n          student_name: '王五',\n          submission_count: 3,\n          last_submission_time: '2024-01-16 16:45:00',\n          current_progress: 'P2-课下'\n        }];\n      } finally {\n        this.slowDetection.loading = false;\n      }\n    },\n    // 加载多次挂在同一个P检测数据\n    loadRepeatedFailures() {\n      this.repeatedFailures.loading = true;\n      setTimeout(() => {\n        this.repeatedFailures.data = [{\n          student_id: '20231004',\n          student_name: '赵六',\n          current_project: 'P2',\n          failure_count: 4,\n          first_failure_time: '2024-01-10 10:00:00',\n          last_failure_time: '2024-01-16 15:30:00'\n        }, {\n          student_id: '20231005',\n          student_name: '钱七',\n          current_project: 'P1',\n          failure_count: 5,\n          first_failure_time: '2024-01-08 14:20:00',\n          last_failure_time: '2024-01-16 11:10:00'\n        }];\n        this.repeatedFailures.loading = false;\n      }, 1000);\n    },\n    // 加载多次没有课上资格检测数据\n    loadQualificationFailures() {\n      this.qualificationFailures.loading = true;\n      setTimeout(() => {\n        this.qualificationFailures.data = [{\n          student_id: '20231006',\n          student_name: '孙八',\n          current_project: 'P1',\n          qualification_failure_count: 3,\n          last_exam_date: '2024-01-15',\n          participation_status: '未参加'\n        }, {\n          student_id: '20231007',\n          student_name: '周九',\n          current_project: 'P2',\n          qualification_failure_count: 2,\n          last_exam_date: '2024-01-16',\n          participation_status: '未参加'\n        }];\n        this.qualificationFailures.loading = false;\n      }, 1000);\n    },\n    // 导出功能\n    exportSlowDetection() {\n      this.exportToCSV(this.slowDetection.data, '进度慢检测结果');\n    },\n    exportRepeatedFailures() {\n      this.exportToCSV(this.repeatedFailures.data, '多次挂在同一个P检测结果');\n    },\n    exportQualificationFailures() {\n      this.exportToCSV(this.qualificationFailures.data, '多次没有课上资格检测结果');\n    },\n    exportToCSV(data, filename) {\n      if (!data.length) {\n        this.$Message.warning('没有数据可导出');\n        return;\n      }\n\n      // 简单的CSV导出实现\n      const headers = Object.keys(data[0]);\n      const csvContent = [headers.join(','), ...data.map(row => headers.map(header => row[header]).join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `${filename}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.$Message.success('导出成功');\n    }\n  }\n};", "map": {"version": 3, "mappings": "AAkHA;AAEA;EACAA;EACAC;IACA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAL;MACA;MACAM,uBACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA,GACA;QACAL;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MAEA;MACAI;QACAC;QACAT;QACAL;MACA;MACAe,0BACA;QACAR;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA,GACA;QACAL;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MAEA;MACAO;QACAF;QACAT;QACAL;MACA;MACAiB,+BACA;QACAV;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA,GACA;QACAL;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA;IAEA;EACA;EACAM;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACA;UACA;YACA;UACA;UACA;QACA;UACA;YACA;UACA;UACA;QACA;UACA;YACA;UACA;UACA;MAAA;IAEA;IAEA;IACA;MACA;QACA;QACA;MACA;MAEA;MAEA;QACA;UACAC;UACAC;QACA;QACA;QACA;MACA;QACA;QACA;QACA,2BACA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA,GACA;UACAJ;UACAC;UACAC;UACAC;UACAC;QACA,GACA;UACAJ;UACAC;UACAC;UACAC;UACAC;QACA,EACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEAC;QACA,8BACA;UACAN;UACAC;UACAM;UACAC;UACAC;UACAC;QACA,GACA;UACAV;UACAC;UACAM;UACAC;UACAC;UACAC;QACA,EACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEAL;QACA,mCACA;UACAN;UACAC;UACAM;UACAK;UACAC;UACAC;QACA,GACA;UACAd;UACAC;UACAM;UACAK;UACAC;UACAC;QACA,EACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA,oBACAC,mBACA,iEACA;MAEA;QAAAC;MAAA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;MACAD;MACAC;MAEA;IACA;EACA;AACA", "names": ["name", "data", "activeTab", "slowDetection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loading", "slowDetectionColumns", "title", "key", "width", "render", "props", "color", "repeatedFailures", "failureT<PERSON><PERSON>old", "repeatedFailuresColumns", "qualificationFailures", "qualificationFailuresColumns", "mounted", "methods", "onTabChange", "submission_threshold", "days_range", "student_id", "student_name", "submission_count", "last_submission_time", "current_progress", "loadRepeatedFailures", "setTimeout", "current_project", "failure_count", "first_failure_time", "last_failure_time", "loadQualificationFailures", "qualification_failure_count", "last_exam_date", "participation_status", "exportSlowDetection", "exportRepeatedFailures", "exportQualificationFailures", "exportToCSV", "headers", "type", "link", "document"], "sourceRoot": "src/view/exam/progress", "sources": ["progress-detection.vue"], "sourcesContent": ["<template>\n  <div>\n    <Card>\n      <p slot=\"title\">进度检测</p>\n      <Tabs v-model=\"activeTab\" @on-click=\"onTabChange\">\n        <!-- 功能1：进度慢检测 -->\n        <TabPane label=\"进度慢检测\" name=\"slow-detection\">\n          <Row :gutter=\"16\" style=\"margin-bottom: 16px\">\n            <Col span=\"6\">\n              <FormItem label=\"提交次数阈值\">\n                <InputNumber v-model=\"slowDetection.submissionThreshold\" :min=\"1\" :max=\"100\" />\n              </FormItem>\n            </Col>\n            <Col span=\"6\">\n              <FormItem label=\"统计天数范围\">\n                <InputNumber v-model=\"slowDetection.daysRange\" :min=\"1\" :max=\"30\" />\n              </FormItem>\n            </Col>\n            <Col span=\"6\">\n              <Button type=\"primary\" @click=\"loadSlowDetection\">检测</Button>\n            </Col>\n            <Col span=\"6\">\n              <Button type=\"success\" @click=\"exportSlowDetection\" :disabled=\"!slowDetection.data.length\">导出</Button>\n            </Col>\n          </Row>\n          <Table \n            :data=\"slowDetection.data\" \n            :columns=\"slowDetectionColumns\" \n            :loading=\"slowDetection.loading\"\n            stripe\n          />\n          <div v-if=\"slowDetection.data.length\" style=\"margin-top: 16px\">\n            <Alert type=\"info\" show-icon>\n              检测到 <strong>{{ slowDetection.data.length }}</strong> 名学生在最近 <strong>{{ slowDetection.daysRange }}</strong> 天内提交次数少于 <strong>{{ slowDetection.submissionThreshold }}</strong> 次\n            </Alert>\n          </div>\n          <div v-else-if=\"!slowDetection.loading && slowDetection.data.length === 0\" style=\"margin-top: 16px\">\n            <Alert type=\"success\" show-icon>\n              太棒了！所有学生的提交次数都达到了要求\n            </Alert>\n          </div>\n        </TabPane>\n\n        <!-- 功能3：多次挂在同一个P检测 -->\n        <TabPane label=\"多次挂在同一个P检测\" name=\"repeated-failures\">\n          <Row :gutter=\"16\" style=\"margin-bottom: 16px\">\n            <Col span=\"6\">\n              <FormItem label=\"失败次数阈值\">\n                <InputNumber v-model=\"repeatedFailures.failureThreshold\" :min=\"1\" :max=\"20\" />\n              </FormItem>\n            </Col>\n            <Col span=\"6\">\n              <Button type=\"primary\" @click=\"loadRepeatedFailures\">检测</Button>\n            </Col>\n            <Col span=\"6\">\n              <Button type=\"success\" @click=\"exportRepeatedFailures\" :disabled=\"!repeatedFailures.data.length\">导出</Button>\n            </Col>\n          </Row>\n          <Table \n            :data=\"repeatedFailures.data\" \n            :columns=\"repeatedFailuresColumns\" \n            :loading=\"repeatedFailures.loading\"\n            stripe\n          />\n          <div v-if=\"repeatedFailures.data.length\" style=\"margin-top: 16px\">\n            <Alert type=\"warning\" show-icon>\n              检测到 <strong>{{ repeatedFailures.data.length }}</strong> 名学生在当前P上失败次数超过 <strong>{{ repeatedFailures.failureThreshold }}</strong> 次，需要重点关注\n            </Alert>\n          </div>\n          <div v-else-if=\"!repeatedFailures.loading && repeatedFailures.data.length === 0\" style=\"margin-top: 16px\">\n            <Alert type=\"success\" show-icon>\n              很好！没有学生在同一个P上反复失败\n            </Alert>\n          </div>\n        </TabPane>\n\n        <!-- 功能4：多次没有课上资格检测 -->\n        <TabPane label=\"多次没有课上资格检测\" name=\"qualification-failures\">\n          <Row :gutter=\"16\" style=\"margin-bottom: 16px\">\n            <Col span=\"6\">\n              <FormItem label=\"失败次数阈值\">\n                <InputNumber v-model=\"qualificationFailures.failureThreshold\" :min=\"1\" :max=\"20\" />\n              </FormItem>\n            </Col>\n            <Col span=\"6\">\n              <Button type=\"primary\" @click=\"loadQualificationFailures\">检测</Button>\n            </Col>\n            <Col span=\"6\">\n              <Button type=\"success\" @click=\"exportQualificationFailures\" :disabled=\"!qualificationFailures.data.length\">导出</Button>\n            </Col>\n          </Row>\n          <Table \n            :data=\"qualificationFailures.data\" \n            :columns=\"qualificationFailuresColumns\" \n            :loading=\"qualificationFailures.loading\"\n            stripe\n          />\n          <div v-if=\"qualificationFailures.data.length\" style=\"margin-top: 16px\">\n            <Alert type=\"error\" show-icon>\n              <strong>紧急关注！</strong>检测到 <strong>{{ qualificationFailures.data.length }}</strong> 名学生多次失去课上资格，失败次数超过 <strong>{{ qualificationFailures.failureThreshold }}</strong> 次\n            </Alert>\n          </div>\n          <div v-else-if=\"!qualificationFailures.loading && qualificationFailures.data.length === 0\" style=\"margin-top: 16px\">\n            <Alert type=\"success\" show-icon>\n              优秀！所有学生都能正常获得课上资格\n            </Alert>\n          </div>\n        </TabPane>\n      </Tabs>\n    </Card>\n  </div>\n</template>\n\n<script>\nimport { getErrModalOptions } from '@/libs/util'\n\nexport default {\n  name: 'ProgressDetection',\n  data() {\n    return {\n      activeTab: 'slow-detection',\n      \n      // 进度慢检测\n      slowDetection: {\n        submissionThreshold: 5,\n        daysRange: 7,\n        loading: false,\n        data: []\n      },\n      slowDetectionColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        },\n        {\n          title: '提交次数',\n          key: 'submission_count',\n          width: 100,\n          render: (h, params) => {\n            const count = params.row.submission_count\n            const color = count < 3 ? 'red' : count < 5 ? 'orange' : 'green'\n            return h('Tag', { props: { color } }, count)\n          }\n        },\n        {\n          title: '最后提交时间',\n          key: 'last_submission_time',\n          width: 160\n        },\n        {\n          title: '当前进度',\n          key: 'current_progress',\n          width: 120\n        }\n      ],\n\n      // 多次挂在同一个P检测\n      repeatedFailures: {\n        failureThreshold: 3,\n        loading: false,\n        data: []\n      },\n      repeatedFailuresColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        },\n        {\n          title: '当前P',\n          key: 'current_project',\n          width: 150\n        },\n        {\n          title: '失败次数',\n          key: 'failure_count',\n          width: 100,\n          render: (h, params) => {\n            const count = params.row.failure_count\n            const color = count >= 5 ? 'red' : count >= 3 ? 'orange' : 'yellow'\n            return h('Tag', { props: { color } }, count)\n          }\n        },\n        {\n          title: '首次失败时间',\n          key: 'first_failure_time',\n          width: 160\n        },\n        {\n          title: '最近失败时间',\n          key: 'last_failure_time',\n          width: 160\n        }\n      ],\n\n      // 多次没有课上资格检测\n      qualificationFailures: {\n        failureThreshold: 2,\n        loading: false,\n        data: []\n      },\n      qualificationFailuresColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        },\n        {\n          title: '当前P',\n          key: 'current_project',\n          width: 150\n        },\n        {\n          title: '失去资格次数',\n          key: 'qualification_failure_count',\n          width: 120,\n          render: (h, params) => {\n            const count = params.row.qualification_failure_count\n            const color = count >= 4 ? 'red' : count >= 2 ? 'orange' : 'yellow'\n            return h('Tag', { props: { color } }, count)\n          }\n        },\n        {\n          title: '最近一次课上考试',\n          key: 'last_exam_date',\n          width: 160\n        },\n        {\n          title: '参加状态',\n          key: 'participation_status',\n          width: 100,\n          render: (h, params) => {\n            const status = params.row.participation_status\n            const color = status === '未参加' ? 'red' : 'green'\n            return h('Tag', { props: { color } }, status)\n          }\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadSlowDetection()\n  },\n  methods: {\n    onTabChange(name) {\n      this.activeTab = name\n      // 根据切换的标签页加载对应数据\n      switch (name) {\n        case 'slow-detection':\n          if (!this.slowDetection.data.length) {\n            this.loadSlowDetection()\n          }\n          break\n        case 'repeated-failures':\n          if (!this.repeatedFailures.data.length) {\n            this.loadRepeatedFailures()\n          }\n          break\n        case 'qualification-failures':\n          if (!this.qualificationFailures.data.length) {\n            this.loadQualificationFailures()\n          }\n          break\n      }\n    },\n\n    // 加载进度慢检测数据\n    async loadSlowDetection() {\n      if (!this.currentCourseId) {\n        this.$Message.warning('请先选择当前课程')\n        return\n      }\n\n      this.slowDetection.loading = true\n\n      try {\n        const params = {\n          submission_threshold: this.slowDetection.submissionThreshold,\n          days_range: this.slowDetection.daysRange\n        }\n        const res = await slowDetectionReq(this.currentCourseId, params)\n        this.slowDetection.data = res.data.students || []\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error))\n        // 如果API调用失败，使用模拟数据作为展示\n        this.slowDetection.data = [\n          {\n            student_id: '20231001',\n            student_name: '张三',\n            submission_count: 2,\n            last_submission_time: '2024-01-15 14:30:00',\n            current_progress: 'P1-课下'\n          },\n          {\n            student_id: '20231002',\n            student_name: '李四',\n            submission_count: 1,\n            last_submission_time: '2024-01-14 09:15:00',\n            current_progress: 'P1-课下'\n          },\n          {\n            student_id: '20231003',\n            student_name: '王五',\n            submission_count: 3,\n            last_submission_time: '2024-01-16 16:45:00',\n            current_progress: 'P2-课下'\n          }\n        ]\n      } finally {\n        this.slowDetection.loading = false\n      }\n    },\n\n    // 加载多次挂在同一个P检测数据\n    loadRepeatedFailures() {\n      this.repeatedFailures.loading = true\n      \n      setTimeout(() => {\n        this.repeatedFailures.data = [\n          {\n            student_id: '20231004',\n            student_name: '赵六',\n            current_project: 'P2',\n            failure_count: 4,\n            first_failure_time: '2024-01-10 10:00:00',\n            last_failure_time: '2024-01-16 15:30:00'\n          },\n          {\n            student_id: '20231005',\n            student_name: '钱七',\n            current_project: 'P1',\n            failure_count: 5,\n            first_failure_time: '2024-01-08 14:20:00',\n            last_failure_time: '2024-01-16 11:10:00'\n          }\n        ]\n        this.repeatedFailures.loading = false\n      }, 1000)\n    },\n\n    // 加载多次没有课上资格检测数据\n    loadQualificationFailures() {\n      this.qualificationFailures.loading = true\n      \n      setTimeout(() => {\n        this.qualificationFailures.data = [\n          {\n            student_id: '20231006',\n            student_name: '孙八',\n            current_project: 'P1',\n            qualification_failure_count: 3,\n            last_exam_date: '2024-01-15',\n            participation_status: '未参加'\n          },\n          {\n            student_id: '20231007',\n            student_name: '周九',\n            current_project: 'P2',\n            qualification_failure_count: 2,\n            last_exam_date: '2024-01-16',\n            participation_status: '未参加'\n          }\n        ]\n        this.qualificationFailures.loading = false\n      }, 1000)\n    },\n\n    // 导出功能\n    exportSlowDetection() {\n      this.exportToCSV(this.slowDetection.data, '进度慢检测结果')\n    },\n\n    exportRepeatedFailures() {\n      this.exportToCSV(this.repeatedFailures.data, '多次挂在同一个P检测结果')\n    },\n\n    exportQualificationFailures() {\n      this.exportToCSV(this.qualificationFailures.data, '多次没有课上资格检测结果')\n    },\n\n    exportToCSV(data, filename) {\n      if (!data.length) {\n        this.$Message.warning('没有数据可导出')\n        return\n      }\n      \n      // 简单的CSV导出实现\n      const headers = Object.keys(data[0])\n      const csvContent = [\n        headers.join(','),\n        ...data.map(row => headers.map(header => row[header]).join(','))\n      ].join('\\n')\n      \n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })\n      const link = document.createElement('a')\n      const url = URL.createObjectURL(blob)\n      link.setAttribute('href', url)\n      link.setAttribute('download', `${filename}.csv`)\n      link.style.visibility = 'hidden'\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      \n      this.$Message.success('导出成功')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ivu-card {\n  margin: 20px;\n}\n\n.ivu-table {\n  margin-top: 16px;\n}\n\n.ivu-form-item {\n  margin-bottom: 0;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}