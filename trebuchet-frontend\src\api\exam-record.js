import { getRequest } from '@/api/util'

export const examRecordReq = (method, params) => {
  return getRequest(`/api/exam-record`, method, params)
}

export const examRecordIdReq = (method, examRecordId, params) => {
  return getRequest(`/api/exam-record/${examRecordId}`, method, params)
}

export const examRecordProblemReq = (method, examRecordId, params) => {
  return getRequest(`/api/exam-record-problem-detail/${examRecordId}`, method, params)
}

export const examDurationModify = (method, params) => {
  return getRequest('/api/exam-record/delay_minute', method, params)
}
