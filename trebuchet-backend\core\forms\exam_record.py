"""
ExamRecord create and update form
"""
from django import forms

from core.models.exam_record import ExamRecord


class ExamRecordInfo(forms.Form):
    """
    validate exam-record create
    """
    student_id = forms.IntegerField()
    project_in_exam_id = forms.IntegerField()
    extend_time = forms.IntegerField(required=False)
    status = forms.IntegerField(max_value=ExamRecord.MAX_STATUS,
                                min_value=ExamRecord.MIN_STATUS, required=False)
    checked_in_at = forms.DateTimeField(required=False)
    checked_out_at = forms.DateTimeField(required=False)
    check_result = forms.IntegerField(max_value=ExamRecord.MAX_GRADE, min_value=ExamRecord.MIN_GRADE, required=False)
    check_comment = forms.CharField(max_length=1024, required=False)


class ExamRecordUpdateInfo(forms.Form):
    """
    validate exam-record update
    """
    student_id = forms.IntegerField(required=False)
    project_in_exam_id = forms.IntegerField(required=False)
    extend_time = forms.IntegerField(required=False)
    status = forms.IntegerField(max_value=ExamRecord.MAX_STATUS,
                                min_value=ExamRecord.MIN_STATUS, required=False)
    checked_in_at = forms.DateTimeField(required=False)
    checked_out_at = forms.DateTimeField(required=False)
    check_result = forms.IntegerField(max_value=ExamRecord.MAX_GRADE, min_value=ExamRecord.MIN_GRADE, required=False)
    check_comment = forms.CharField(max_length=1024, required=False)
