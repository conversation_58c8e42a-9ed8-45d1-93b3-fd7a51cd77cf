"""Student enrollment management
"""
from urllib import parse

import requests
from django.conf import settings
from django.http import HttpRequest
from django.views.decorators.http import require_POST

from core.api.auth import jwt_auth
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            response_wrapper, success_api_response)
from core.models.course import Course
from core.models.student import Student
from core.api.permissions import CORE_COURSE_CHANGE

LOGIN_URL = "http://cscore.net.cn/user_api/v1/account/login_session/"
ENROLL_URL = "http://cscore.net.cn/courses/{}/instructor/api/students_update_enrollment"
PROFILE_URL = "http://cscore.net.cn/courses/{}/instructor/api/get_students_features"


def login() -> requests.Session:
    """Login
    """
    client = requests.session()
    client.get(LOGIN_URL)
    csrf_token = client.cookies["csrftoken"]
    payload = {
        "email": settings.EDX_USERNAME,
        "password": settings.EDX_PASSWORD,
        "remember": False,
        "csrfmiddlewaretoken": csrf_token
    }
    client.post(LOGIN_URL, data=payload)
    client.get(LOGIN_URL)
    return client


def get_course_key(course_id: int):
    """[summary]

    Args:
        course_id (int): [description]
    """
    course = Course.objects.filter(pk=course_id).first()
    if course is None:
        return None, None
    course_key = "course-v1:" + parse.unquote(course.code)
    return course, course_key


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_POST
def student_enroll(request: HttpRequest, course_id: int):
    """[summary]

    Args:
        request (HttpRequest): [description]
        course_id (int): [description]
    """
    course, course_key = get_course_key(course_id)
    if course is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Bad course id.")

    data = parse_data(request)
    users = data.get("users")
    if users is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Empty users.")

    identifiers = "\n".join(users)
    student_enroll_impl(course_key, "enroll",
                        identifiers, True, False)

    return success_api_response({"result": "Ok, enroll request has been sent."})


def student_enroll_impl(course_key, action, identifiers, auto_enroll, email_students):
    """[summary]
    """
    client = login()
    csrf_token = client.cookies["csrftoken"]
    header = {
        "X-CSRFToken": csrf_token
    }
    payload = {
        "action": action,
        "identifiers": identifiers,
        "auto_enroll": auto_enroll,
        "email_students": email_students
    }
    client.post(ENROLL_URL.format(course_key), data=payload, headers=header)


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_POST
def student_unenroll(request: HttpRequest, course_id: int):
    """[summary]

    Args:
        request (HttpRequest): [description]
        course_id (int): [description]
    """
    course, course_key = get_course_key(course_id)
    if course is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Bad course id.")

    data = parse_data(request)
    users = data.get("users")
    if users is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Empty users.")

    identifiers = "\n".join(users)
    student_enroll_impl(course_key, "unenroll",
                        identifiers, False, False)

    return success_api_response({"result": "Ok, unenroll request has been sent."})


def get_students_profile(course_key):
    """[summary]

    Args:
        course_key ([type]): [description]

    Returns:
        [type]: [description]
    """
    client = login()
    csrf_token = client.cookies["csrftoken"]
    header = {
        "X-CSRFToken": csrf_token
    }
    response = client.post(PROFILE_URL.format(course_key), headers=header)
    if response.status_code != requests.codes.ok:
        return None
    result = [(data.get("username"), data.get("email"))
              for data in response.json().get("students")]
    return result


def get_qualified_students(course: Course):
    """[summary]

    Args:
        course (Course): [description]
    """
    result = []
    class_set = course.instructorclass_set.all()
    for class_instance in class_set:
        students = class_instance.student.all()
        result = result + list(students.values_list("student_id", "email"))
    return result


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_POST
def update_course_enrollment(request: HttpRequest, course_id: int):
    """[summary]

    Args:
        request (HttpRequest): HTTP POST request
        course_id (int): pk of the Course object
    """
    course, course_key = get_course_key(course_id)
    if course is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Bad course id.")

    all_enrolled_students = get_students_profile(course_key)
    qualified_students = get_qualified_students(course)

    if not all_enrolled_students:
        return failed_api_response(ErrorCode.REFUSE_ACCESS, "Please check if \"trebuchet-[dev|prod]\"\
                                   has been add into the course as stuff.")

    all_enrolled_students = dict(all_enrolled_students)
    qualified_students = dict(qualified_students)

    illegal_users = []
    enrolled_students = []
    not_enrolled_students = []

    for username, email in all_enrolled_students.items():
        if username in qualified_students:
            enrolled_students.append(username)
            if email != qualified_students[username]:
                Student.objects.filter(student_id=username).update(email=email)
        else:
            illegal_users.append(username)

    for username in qualified_students:
        if username not in all_enrolled_students:
            not_enrolled_students.append(username)

    return success_api_response({
        'enrolled_students': enrolled_students,
        'not_enrolled_students': not_enrolled_students,
        'illegal_users': illegal_users})


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_POST
def enrollment_flush(request: HttpRequest, course_id: int):
    """[summary]

    Args:
        request (HttpRequest): [description]
        course_id (int): [description]
    """
    course, course_key = get_course_key(course_id)
    if course is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Bad course id.")
    students_profile = {username for username,
                                     _ in get_students_profile(course_key)}
    qualified_students = {username for username,
                                       _ in get_qualified_students(course)}
    unenroll_set = students_profile.difference(qualified_students)
    identifiers = "\n".join(list(unenroll_set))
    student_enroll_impl(course_key, "unenroll", identifiers, False, False)
    return success_api_response({"result": "Ok, flush request has been sent."})


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_POST
def enroll_all(request: HttpRequest, course_id: int):
    """[summary]

    Args:
        request (HttpRequest): [description]
        course_id (int): [description]
    """
    course, course_key = get_course_key(course_id)
    if course is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Bad course id.")
    students_profile = {username for username,
                                     _ in get_students_profile(course_key)}
    qualified_students = {username for username,
                                       _ in get_qualified_students(course)}
    enroll_set = qualified_students.difference(students_profile)
    identifiers = "\n".join(list(enroll_set))
    student_enroll_impl(course_key, "enroll", identifiers, True, False)
    return success_api_response({"result": "Ok, enroll request has been sent."})
