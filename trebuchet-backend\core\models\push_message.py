"""
declare Push Message model
"""
from django.contrib.auth import get_user_model
from django.db import models
from django.db.models.deletion import CASCADE

from core.models.course import Course
from core.models.student import Student
from core.models.permissions import (PUSH_MSG_VIEW, PUSH_MSG_EDIT, PUSH_MSG_PUSH)

MESSAGE_TYPE_HTML = 1

PUSH_STATUS_NOT_PUSHED = 0
PUSH_STATUS_SUCCESS = 1
PUSH_STATUS_ERROR = 2


class PushMessage(models.Model):
    """This model describes PushMessage.

    此模型用于描述推送的通知。

    属性:
        course: 所属于的课程

        message_type: Choice 信息类型, 目前仅支持文本类型信息
        content: TextField 消息内容
        comment: TextField 消息注释
        with_email: 是否同时使用邮件进行通知

        recipients: MantToMany, 要接收消息的目标学生，使用中间表，同时存储确认已读状态及时间

        status: 消息状态，是否发送、发送是否成功

        created_at: DateTimeField, 创建时间
        created_by: 创建通知的用户
        pushed_at: DateTimeField, 消息正式推送的时间
    """
    MESSAGE_TYPE = [(MESSAGE_TYPE_HTML, "文本通知")]
    PUSH_STATUS_TYPE = [
        (PUSH_STATUS_NOT_PUSHED, "等待推送"),
        (PUSH_STATUS_SUCCESS, "推送成功"),
        (PUSH_STATUS_ERROR, "(部分)推送出错")
    ]

    course = models.ForeignKey(Course, on_delete=CASCADE)

    message_type = models.IntegerField(choices=MESSAGE_TYPE, default=MESSAGE_TYPE_HTML)
    title = models.CharField(max_length=50, default="计算机组成原理课程通知")
    content = models.TextField()
    comment = models.CharField(max_length=150, blank=True, null=True)
    with_email = models.BooleanField(default=False)
    with_web_notification = models.BooleanField(default=True)

    recipients = models.ManyToManyField(Student, through='PushMessageRecord')

    status = models.IntegerField(choices=PUSH_STATUS_TYPE, default=PUSH_STATUS_NOT_PUSHED)

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True)
    pushed_at = models.DateTimeField(null=True)

    class Meta:
        default_permissions = (PUSH_MSG_VIEW)
        permissions = [
            (PUSH_MSG_VIEW, PUSH_MSG_VIEW),
            (PUSH_MSG_EDIT, PUSH_MSG_EDIT),
            (PUSH_MSG_PUSH, PUSH_MSG_PUSH)
        ]
