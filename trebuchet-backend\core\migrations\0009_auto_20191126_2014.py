# Generated by Django 2.2.6 on 2019-11-26 12:14

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0008_auto_20191107_0108'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='authrecord',
            options={'default_permissions': ()},
        ),
        migrations.AlterModelOptions(
            name='course',
            options={'default_permissions': (),
                     'permissions': [('创建课程', '创建课程'), ('修改课程', '修改课程'), ('查看课程', '查看课程'), ('删除课程', '删除课程')]},
        ),
        migrations.AlterModelOptions(
            name='exam',
            options={'default_permissions': (),
                     'permissions': [('修改考试', '修改考试'), ('创建考试', '创建考试'), ('删除考试', '删除考试'), ('查看考试', '查看考试')]},
        ),
        migrations.AlterModelOptions(
            name='examqueue',
            options={'default_permissions': (),
                     'permissions': [('创建考试队列信息', '创建考试队列信息'), ('修改考试队列信息', '修改考试队列信息'), ('查看考试队列信息', '查看考试队列信息'),
                                     ('删除考试队列信息', '删除考试队列信息')]},
        ),
        migrations.AlterModelOptions(
            name='examrecord',
            options={'default_permissions': (),
                     'permissions': [('修改考试记录', '修改考试记录'), (' 创建考试记录', ' 创建考试记录'), ('删除考试记录', '删除考试记录'),
                                     ('查看考试记录', '查看考试记录'), ('修改旧考试记录', '修改旧考试记录')]},
        ),
        migrations.AlterModelOptions(
            name='instructorclass',
            options={'default_permissions': (),
                     'permissions': [('修改教务选课班级', '修改教务选课班级'), ('创建教务选课班级', '创建教务选课班级'), ('删除教务选课班级', '删除教务选课班级'),
                                     ('查看教务选课班级', '查看教务选课班级')]},
        ),
        migrations.AlterModelOptions(
            name='project',
            options={'default_permissions': (),
                     'permissions': [('修改Project', '修改Project'), ('创建Project', '创建Project'), ('删除Project', '删除Project'),
                                     ('查看Project', '查看Project')]},
        ),
        migrations.AlterModelOptions(
            name='projectinexam',
            options={'default_permissions': (),
                     'permissions': [('修改PIE', '修改PIE'), ('创建PIE', '创建PIE'), ('删除PIE', '删除PIE'), ('查看PIE', '查看PIE')]},
        ),
        migrations.AlterModelOptions(
            name='room',
            options={'default_permissions': (),
                     'permissions': [('创建考场', '创建考场'), ('查看考场', '查看考场'), ('修改考场', '修改考场'), ('删除考场', '删除考场')]},
        ),
        migrations.AlterModelOptions(
            name='seat',
            options={'default_permissions': (),
                     'permissions': [('修改座位', '修改座位'), ('创建座位', '创建座位'), ('删除座位', '删除座位'), ('查看座位', '查看座位')]},
        ),
        migrations.AlterModelOptions(
            name='student',
            options={'default_permissions': (),
                     'permissions': [('修改学生', '修改学生'), ('创建学生', '创建学生'), ('删除学生', '删除学生'), ('查看学生', '查看学生')]},
        ),
        migrations.AlterModelOptions(
            name='studentprogress',
            options={'default_permissions': (),
                     'permissions': [('修改学生进度记录', '修改学生进度记录'), ('创建学生进度记录', '创建学生进度记录'), ('删除学生进度记录', '删除学生进度记录'),
                                     ('查看学生进度记录', '查看学生进度记录')]},
        ),
        migrations.AlterModelOptions(
            name='studentseatrecord',
            options={'default_permissions': (),
                     'permissions': [('修改学生座位记录', '修改学生座位记录'), ('创建学生座位记录', '创建学生座位记录'), ('删除学生座位记录', '删除学生座位记录'),
                                     ('查看学生座位记录', '查看学生座位记录')]},
        ),
        migrations.AlterModelOptions(
            name='userprofile',
            options={'default_permissions': (),
                     'permissions': [('修改用户管理课程信息', '修改用户管理课程信息'), ('创建用户管理课程信息', '创建用户管理课程信息'),
                                     ('删除用户管理课程信息', '删除用户管理课程信息'), ('查看用户管理课程信息', '查看用户管理课程信息')]},
        ),
    ]
