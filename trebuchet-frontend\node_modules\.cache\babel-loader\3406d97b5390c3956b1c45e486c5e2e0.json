{"ast": null, "code": "import { getErrModalOptions } from '@/libs/util';\nimport moment from 'moment';\nexport default {\n  name: 'ExamProgressDetection',\n  data() {\n    return {\n      selectedExam: null,\n      examList: [],\n      isMonitoring: false,\n      monitoringTimer: null,\n      lastUpdateTime: '',\n      activeTab: 'passed',\n      loading: false,\n      showAlert: false,\n      // 考试统计数据\n      examStats: {\n        totalStudents: 0,\n        passedCount: 0,\n        passRate: 0,\n        duration: '00:00:00'\n      },\n      // 已通过学生\n      passedStudents: [],\n      passedStudentsColumns: [{\n        title: '学号',\n        key: 'student_id',\n        width: 120\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        width: 120\n      }, {\n        title: '通过时间',\n        key: 'pass_time',\n        width: 160\n      }, {\n        title: '用时',\n        key: 'duration',\n        width: 100,\n        render: (h, params) => {\n          const duration = params.row.duration;\n          const color = duration < 30 ? 'green' : duration < 60 ? 'orange' : 'red';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, duration + '分钟');\n        }\n      }, {\n        title: '项目',\n        key: 'project_name',\n        width: 150\n      }, {\n        title: '得分',\n        key: 'score',\n        width: 80,\n        render: (h, params) => {\n          const score = params.row.score;\n          const color = score >= 90 ? 'green' : score >= 70 ? 'orange' : 'red';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, score);\n        }\n      }],\n      // 未通过学生\n      failedStudents: [],\n      failedStudentsColumns: [{\n        title: '学号',\n        key: 'student_id',\n        width: 120\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        width: 120\n      }, {\n        title: '当前状态',\n        key: 'status',\n        width: 120,\n        render: (h, params) => {\n          const status = params.row.status;\n          let color = 'blue';\n          if (status === '答题中') color = 'orange';else if (status === '未开始') color = 'default';else if (status === '已提交') color = 'purple';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, status);\n        }\n      }, {\n        title: '开始时间',\n        key: 'start_time',\n        width: 160\n      }, {\n        title: '已用时',\n        key: 'elapsed_time',\n        width: 100\n      }, {\n        title: '项目',\n        key: 'project_name',\n        width: 150\n      }, {\n        title: '当前得分',\n        key: 'current_score',\n        width: 100,\n        render: (h, params) => {\n          const score = params.row.current_score;\n          if (score === null || score === undefined) {\n            return h('span', {\n              style: {\n                color: '#ccc'\n              }\n            }, '未评分');\n          }\n          const color = score >= 60 ? 'green' : 'red';\n          return h('Tag', {\n            props: {\n              color\n            }\n          }, score);\n        }\n      }]\n    };\n  },\n  mounted() {\n    this.loadExamList();\n  },\n  beforeDestroy() {\n    this.stopMonitoring();\n  },\n  methods: {\n    // 加载考试列表\n    loadExamList() {\n      // 模拟API调用\n      setTimeout(() => {\n        this.examList = [{\n          id: 1,\n          date: '2024-01-16'\n        }, {\n          id: 2,\n          date: '2024-01-15'\n        }, {\n          id: 3,\n          date: '2024-01-14'\n        }];\n      }, 500);\n    },\n    // 考试选择变化\n    onExamChange(examId) {\n      this.selectedExam = examId;\n      this.loadExamData();\n    },\n    // 加载考试数据\n    loadExamData() {\n      if (!this.selectedExam) return;\n      this.loading = true;\n\n      // 模拟API调用\n      setTimeout(() => {\n        // 模拟考试统计数据\n        this.examStats = {\n          totalStudents: 45,\n          passedCount: 12,\n          passRate: Math.round(12 / 45 * 100),\n          duration: '01:23:45'\n        };\n\n        // 模拟已通过学生数据\n        this.passedStudents = [{\n          student_id: '20231001',\n          student_name: '张三',\n          pass_time: '2024-01-16 10:30:00',\n          duration: 25,\n          project_name: 'P1-基础算法',\n          score: 95\n        }, {\n          student_id: '20231002',\n          student_name: '李四',\n          pass_time: '2024-01-16 10:45:00',\n          duration: 40,\n          project_name: 'P1-基础算法',\n          score: 88\n        }];\n\n        // 模拟未通过学生数据\n        this.failedStudents = [{\n          student_id: '20231003',\n          student_name: '王五',\n          status: '答题中',\n          start_time: '2024-01-16 09:00:00',\n          elapsed_time: '83分钟',\n          project_name: 'P1-基础算法',\n          current_score: 45\n        }, {\n          student_id: '20231004',\n          student_name: '赵六',\n          status: '未开始',\n          start_time: '-',\n          elapsed_time: '0分钟',\n          project_name: 'P1-基础算法',\n          current_score: null\n        }];\n        this.loading = false;\n\n        // 检查是否需要显示警报\n        this.checkAlert();\n      }, 1000);\n    },\n    // 开始监控\n    startMonitoring() {\n      if (!this.selectedExam) {\n        this.$Message.warning('请先选择考试');\n        return;\n      }\n      this.isMonitoring = true;\n      this.updateLastUpdateTime();\n\n      // 设置定时器，每30秒更新一次数据\n      this.monitoringTimer = setInterval(() => {\n        this.loadExamData();\n        this.updateLastUpdateTime();\n      }, 30000);\n      this.$Message.success('开始监控考试进度');\n    },\n    // 停止监控\n    stopMonitoring() {\n      if (this.monitoringTimer) {\n        clearInterval(this.monitoringTimer);\n        this.monitoringTimer = null;\n      }\n      this.isMonitoring = false;\n      this.$Message.info('已停止监控');\n    },\n    // 更新最后更新时间\n    updateLastUpdateTime() {\n      this.lastUpdateTime = moment().format('HH:mm:ss');\n    },\n    // 检查是否需要显示警报\n    checkAlert() {\n      // 如果考试进行超过30分钟且无人通过，显示警报\n      const durationParts = this.examStats.duration.split(':');\n      const totalMinutes = parseInt(durationParts[0]) * 60 + parseInt(durationParts[1]);\n      this.showAlert = totalMinutes > 30 && this.examStats.passedCount === 0;\n    },\n    // 导出通过学生名单\n    exportPassedStudents() {\n      if (!this.passedStudents.length) {\n        this.$Message.warning('没有通过的学生可导出');\n        return;\n      }\n      const headers = ['学号', '姓名', '通过时间', '用时(分钟)', '项目', '得分'];\n      const csvContent = [headers.join(','), ...this.passedStudents.map(student => [student.student_id, student.student_name, student.pass_time, student.duration, student.project_name, student.score].join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `考试${this.selectedExam}_通过名单.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.$Message.success('导出成功');\n    }\n  }\n};", "map": {"version": 3, "mappings": "AA4HA;AACA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC,wBACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA,GACA;QACAL;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA,EACA;MAEA;MACAC;MACAC,wBACA;QACAP;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;UACA,4CACA,6CACA;UACA;YAAAC;cAAAC;YAAA;UAAA;QACA;MACA,GACA;QACAL;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;QACAC;UACA;UACA;YACA;cAAAK;gBAAAH;cAAA;YAAA;UACA;UACA;UACA;YAAAD;cAAAC;YAAA;UAAA;QACA;MACA;IAEA;EACA;EAEAI;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;QACA,iBACA;UAAAC;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,EACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;;MAEA;MACAJ;QACA;QACA;UACAnB;UACAC;UACAC;UACAC;QACA;;QAEA;QACA,uBACA;UACAqB;UACAC;UACAC;UACAvB;UACAwB;UACAC;QACA,GACA;UACAJ;UACAC;UACAC;UACAvB;UACAwB;UACAC;QACA,EACA;;QAEA;QACA,uBACA;UACAJ;UACAC;UACAI;UACAC;UACAC;UACAJ;UACAK;QACA,GACA;UACAR;UACAC;UACAI;UACAC;UACAC;UACAJ;UACAK;QACA,EACA;QAEA;;QAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MAEA;MACA,oBACAC,mBACA,uCACAC,oBACAA,sBACAA,mBACAA,kBACAA,sBACAA,cACA,YACA;MAEA;QAAAC;MAAA;MACA;MACA;MACAC;MACAA;MACAA;MACAC;MACAD;MACAC;MAEA;IACA;EACA;AACA", "names": ["name", "data", "selectedExam", "examList", "isMonitoring", "monitoringTimer", "lastUpdateTime", "activeTab", "loading", "show<PERSON><PERSON><PERSON>", "examStats", "totalStudents", "passedCount", "passRate", "duration", "passedStudents", "passedStudentsColumns", "title", "key", "width", "render", "props", "color", "failedStudents", "failedStudentsColumns", "style", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "loadExamList", "setTimeout", "id", "date", "onExamChange", "loadExamData", "student_id", "student_name", "pass_time", "project_name", "score", "status", "start_time", "elapsed_time", "current_score", "startMonitoring", "stopMonitoring", "clearInterval", "updateLastUpdateTime", "<PERSON><PERSON><PERSON><PERSON>", "exportPassedStudents", "headers", "student", "type", "link", "document"], "sourceRoot": "src/view/on-exam", "sources": ["exam-progress-detection.vue"], "sourcesContent": ["<template>\n  <div>\n    <Card>\n      <p slot=\"title\">考试通过人数检测及无人通过警报</p>\n      \n      <!-- 考试选择 -->\n      <Row :gutter=\"16\" style=\"margin-bottom: 20px\">\n        <Col span=\"8\">\n          <FormItem label=\"选择考试\">\n            <Select v-model=\"selectedExam\" @on-change=\"onExamChange\" placeholder=\"请选择考试\">\n              <Option v-for=\"exam in examList\" :key=\"exam.id\" :value=\"exam.id\">\n                {{ exam.id }} - {{ exam.date }}\n              </Option>\n            </Select>\n          </FormItem>\n        </Col>\n        <Col span=\"4\">\n          <Button type=\"primary\" @click=\"startMonitoring\" :disabled=\"!selectedExam\">开始监控</Button>\n        </Col>\n        <Col span=\"4\">\n          <Button @click=\"stopMonitoring\" :disabled=\"!isMonitoring\">停止监控</Button>\n        </Col>\n        <Col span=\"4\">\n          <Button type=\"success\" @click=\"exportPassedStudents\" :disabled=\"!passedStudents.length\">导出通过名单</Button>\n        </Col>\n      </Row>\n\n      <!-- 实时状态显示 -->\n      <Row :gutter=\"16\" style=\"margin-bottom: 20px\">\n        <Col span=\"6\">\n          <Card>\n            <div style=\"text-align: center\">\n              <h2 style=\"color: #2d8cf0; margin: 0\">{{ examStats.totalStudents }}</h2>\n              <p style=\"margin: 5px 0 0 0; color: #666\">总参考人数</p>\n            </div>\n          </Card>\n        </Col>\n        <Col span=\"6\">\n          <Card>\n            <div style=\"text-align: center\">\n              <h2 style=\"color: #19be6b; margin: 0\">{{ examStats.passedCount }}</h2>\n              <p style=\"margin: 5px 0 0 0; color: #666\">已通过人数</p>\n            </div>\n          </Card>\n        </Col>\n        <Col span=\"6\">\n          <Card>\n            <div style=\"text-align: center\">\n              <h2 style=\"color: #ff9900; margin: 0\">{{ examStats.passRate }}%</h2>\n              <p style=\"margin: 5px 0 0 0; color: #666\">通过率</p>\n            </div>\n          </Card>\n        </Col>\n        <Col span=\"6\">\n          <Card>\n            <div style=\"text-align: center\">\n              <h2 style=\"color: #ed4014; margin: 0\">{{ examStats.duration }}</h2>\n              <p style=\"margin: 5px 0 0 0; color: #666\">考试持续时间</p>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      <!-- 警报区域 -->\n      <div v-if=\"showAlert\" style=\"margin-bottom: 20px\">\n        <Alert type=\"error\" show-icon banner>\n          <template slot=\"desc\">\n            <strong>警报：</strong>考试已进行 {{ examStats.duration }}，当前无人通过！\n            <br>\n            建议检查考试难度或学生准备情况。\n          </template>\n        </Alert>\n      </div>\n\n      <!-- 监控状态指示 -->\n      <div v-if=\"isMonitoring\" style=\"margin-bottom: 20px\">\n        <Alert type=\"info\" show-icon>\n          <Icon type=\"ios-pulse\" />\n          正在实时监控考试 {{ selectedExam }} 的通过情况...\n          <span style=\"float: right\">最后更新：{{ lastUpdateTime }}</span>\n        </Alert>\n      </div>\n\n      <!-- 通过学生列表 -->\n      <Tabs v-model=\"activeTab\">\n        <TabPane label=\"已通过学生\" name=\"passed\">\n          <Table \n            :data=\"passedStudents\" \n            :columns=\"passedStudentsColumns\" \n            :loading=\"loading\"\n            stripe\n          />\n          <div v-if=\"passedStudents.length\" style=\"margin-top: 16px\">\n            <Tag color=\"green\">{{ passedStudents.length }} 名学生已通过考试</Tag>\n          </div>\n        </TabPane>\n        \n        <TabPane label=\"未通过学生\" name=\"failed\">\n          <Table \n            :data=\"failedStudents\" \n            :columns=\"failedStudentsColumns\" \n            :loading=\"loading\"\n            stripe\n          />\n          <div v-if=\"failedStudents.length\" style=\"margin-top: 16px\">\n            <Tag color=\"red\">{{ failedStudents.length }} 名学生尚未通过考试</Tag>\n          </div>\n        </TabPane>\n\n        <TabPane label=\"考试进度统计\" name=\"progress\">\n          <div style=\"height: 300px; display: flex; align-items: center; justify-content: center\">\n            <div style=\"text-align: center\">\n              <Icon type=\"ios-stats\" size=\"60\" style=\"color: #ccc\" />\n              <p style=\"margin-top: 20px; color: #999\">考试进度图表区域</p>\n              <p style=\"color: #999\">（此处可集成图表组件显示实时进度）</p>\n            </div>\n          </div>\n        </TabPane>\n      </Tabs>\n    </Card>\n  </div>\n</template>\n\n<script>\nimport { getErrModalOptions } from '@/libs/util'\nimport moment from 'moment'\n\nexport default {\n  name: 'ExamProgressDetection',\n  data() {\n    return {\n      selectedExam: null,\n      examList: [],\n      isMonitoring: false,\n      monitoringTimer: null,\n      lastUpdateTime: '',\n      activeTab: 'passed',\n      loading: false,\n      showAlert: false,\n      \n      // 考试统计数据\n      examStats: {\n        totalStudents: 0,\n        passedCount: 0,\n        passRate: 0,\n        duration: '00:00:00'\n      },\n      \n      // 已通过学生\n      passedStudents: [],\n      passedStudentsColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        },\n        {\n          title: '通过时间',\n          key: 'pass_time',\n          width: 160\n        },\n        {\n          title: '用时',\n          key: 'duration',\n          width: 100,\n          render: (h, params) => {\n            const duration = params.row.duration\n            const color = duration < 30 ? 'green' : duration < 60 ? 'orange' : 'red'\n            return h('Tag', { props: { color } }, duration + '分钟')\n          }\n        },\n        {\n          title: '项目',\n          key: 'project_name',\n          width: 150\n        },\n        {\n          title: '得分',\n          key: 'score',\n          width: 80,\n          render: (h, params) => {\n            const score = params.row.score\n            const color = score >= 90 ? 'green' : score >= 70 ? 'orange' : 'red'\n            return h('Tag', { props: { color } }, score)\n          }\n        }\n      ],\n      \n      // 未通过学生\n      failedStudents: [],\n      failedStudentsColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          width: 120\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          width: 120\n        },\n        {\n          title: '当前状态',\n          key: 'status',\n          width: 120,\n          render: (h, params) => {\n            const status = params.row.status\n            let color = 'blue'\n            if (status === '答题中') color = 'orange'\n            else if (status === '未开始') color = 'default'\n            else if (status === '已提交') color = 'purple'\n            return h('Tag', { props: { color } }, status)\n          }\n        },\n        {\n          title: '开始时间',\n          key: 'start_time',\n          width: 160\n        },\n        {\n          title: '已用时',\n          key: 'elapsed_time',\n          width: 100\n        },\n        {\n          title: '项目',\n          key: 'project_name',\n          width: 150\n        },\n        {\n          title: '当前得分',\n          key: 'current_score',\n          width: 100,\n          render: (h, params) => {\n            const score = params.row.current_score\n            if (score === null || score === undefined) {\n              return h('span', { style: { color: '#ccc' } }, '未评分')\n            }\n            const color = score >= 60 ? 'green' : 'red'\n            return h('Tag', { props: { color } }, score)\n          }\n        }\n      ]\n    }\n  },\n  \n  mounted() {\n    this.loadExamList()\n  },\n  \n  beforeDestroy() {\n    this.stopMonitoring()\n  },\n  \n  methods: {\n    // 加载考试列表\n    loadExamList() {\n      // 模拟API调用\n      setTimeout(() => {\n        this.examList = [\n          { id: 1, date: '2024-01-16' },\n          { id: 2, date: '2024-01-15' },\n          { id: 3, date: '2024-01-14' }\n        ]\n      }, 500)\n    },\n    \n    // 考试选择变化\n    onExamChange(examId) {\n      this.selectedExam = examId\n      this.loadExamData()\n    },\n    \n    // 加载考试数据\n    loadExamData() {\n      if (!this.selectedExam) return\n      \n      this.loading = true\n      \n      // 模拟API调用\n      setTimeout(() => {\n        // 模拟考试统计数据\n        this.examStats = {\n          totalStudents: 45,\n          passedCount: 12,\n          passRate: Math.round((12 / 45) * 100),\n          duration: '01:23:45'\n        }\n        \n        // 模拟已通过学生数据\n        this.passedStudents = [\n          {\n            student_id: '20231001',\n            student_name: '张三',\n            pass_time: '2024-01-16 10:30:00',\n            duration: 25,\n            project_name: 'P1-基础算法',\n            score: 95\n          },\n          {\n            student_id: '20231002',\n            student_name: '李四',\n            pass_time: '2024-01-16 10:45:00',\n            duration: 40,\n            project_name: 'P1-基础算法',\n            score: 88\n          }\n        ]\n        \n        // 模拟未通过学生数据\n        this.failedStudents = [\n          {\n            student_id: '20231003',\n            student_name: '王五',\n            status: '答题中',\n            start_time: '2024-01-16 09:00:00',\n            elapsed_time: '83分钟',\n            project_name: 'P1-基础算法',\n            current_score: 45\n          },\n          {\n            student_id: '20231004',\n            student_name: '赵六',\n            status: '未开始',\n            start_time: '-',\n            elapsed_time: '0分钟',\n            project_name: 'P1-基础算法',\n            current_score: null\n          }\n        ]\n        \n        this.loading = false\n        \n        // 检查是否需要显示警报\n        this.checkAlert()\n      }, 1000)\n    },\n    \n    // 开始监控\n    startMonitoring() {\n      if (!this.selectedExam) {\n        this.$Message.warning('请先选择考试')\n        return\n      }\n      \n      this.isMonitoring = true\n      this.updateLastUpdateTime()\n      \n      // 设置定时器，每30秒更新一次数据\n      this.monitoringTimer = setInterval(() => {\n        this.loadExamData()\n        this.updateLastUpdateTime()\n      }, 30000)\n      \n      this.$Message.success('开始监控考试进度')\n    },\n    \n    // 停止监控\n    stopMonitoring() {\n      if (this.monitoringTimer) {\n        clearInterval(this.monitoringTimer)\n        this.monitoringTimer = null\n      }\n      this.isMonitoring = false\n      this.$Message.info('已停止监控')\n    },\n    \n    // 更新最后更新时间\n    updateLastUpdateTime() {\n      this.lastUpdateTime = moment().format('HH:mm:ss')\n    },\n    \n    // 检查是否需要显示警报\n    checkAlert() {\n      // 如果考试进行超过30分钟且无人通过，显示警报\n      const durationParts = this.examStats.duration.split(':')\n      const totalMinutes = parseInt(durationParts[0]) * 60 + parseInt(durationParts[1])\n      \n      this.showAlert = totalMinutes > 30 && this.examStats.passedCount === 0\n    },\n    \n    // 导出通过学生名单\n    exportPassedStudents() {\n      if (!this.passedStudents.length) {\n        this.$Message.warning('没有通过的学生可导出')\n        return\n      }\n      \n      const headers = ['学号', '姓名', '通过时间', '用时(分钟)', '项目', '得分']\n      const csvContent = [\n        headers.join(','),\n        ...this.passedStudents.map(student => [\n          student.student_id,\n          student.student_name,\n          student.pass_time,\n          student.duration,\n          student.project_name,\n          student.score\n        ].join(','))\n      ].join('\\n')\n      \n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })\n      const link = document.createElement('a')\n      const url = URL.createObjectURL(blob)\n      link.setAttribute('href', url)\n      link.setAttribute('download', `考试${this.selectedExam}_通过名单.csv`)\n      link.style.visibility = 'hidden'\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      \n      this.$Message.success('导出成功')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ivu-card {\n  margin: 20px;\n}\n\n.ivu-table {\n  margin-top: 16px;\n}\n\n.ivu-form-item {\n  margin-bottom: 0;\n}\n\n.ivu-alert {\n  margin-bottom: 16px;\n}\n\n.ivu-card .ivu-card-body {\n  padding: 16px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}