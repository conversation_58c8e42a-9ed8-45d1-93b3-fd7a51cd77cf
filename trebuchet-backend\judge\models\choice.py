"""
declare Choices model
"""
from django.db import models
from judge.models.problem import Problem


class Choice(models.Model):
    """
    Choice for SingleChoice and Mult-Choise Question
    "A: this is a choice"
    "A" for answr and "this is a choice" for description
    """
    problem = models.ForeignKey(Problem, on_delete=models.CASCADE, db_index=True)
    answer = models.TextField(default='')
    description = models.TextField(default='')
