from django.core.paginator import Paginator
from django.http import HttpRequest
from django.views.decorators.http import require_http_methods

from core.api.auth import jwt_auth
from core.api.permissions import CORE_QUESTION_VIEW, CORE_QUESTION_EDIT
from core.api.query_utils import query_filter, query_page
from core.api.utils import response_wrapper, require_course_permission, success_api_response, parse_data, ErrorCode, \
    failed_api_response, wrapped_api
from core.models.course import Course
from core.models.project import Project
from core.models.question import Question


@response_wrapper
@jwt_auth(perms=[CORE_QUESTION_VIEW])
@query_filter(fields=[('id', int), ('name', str), ('project__id', int), ('project__name', str), ('enabled', bool)])
@require_course_permission
@query_page(default=10)
def list_questions(request: HttpRequest, course: Course, *args, **kwargs):
    filters = kwargs.get("filter")

    query = Question.objects.filter(filters).filter(project__course=course) \
        .values("id", "name", "project__name", "project__id", "content", "hint", "enabled", "created_at")

    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(query, page_size)

    ret = list(paginator.get_page(page).object_list)
    return success_api_response({"questions": ret,
                                 "paginator": {"page_all": paginator.num_pages, "count": paginator.count}})


@response_wrapper
@jwt_auth(perms=[CORE_QUESTION_VIEW])
@require_course_permission
def get_question(request: HttpRequest, course: Course, *args, **kwargs):
    query = request.GET.dict()
    question_id: int = int(query["id"])
    try:
        obj = Question.objects.filter(id=question_id, course=course) \
            .values("id", "name", "project__name", "project__id", "content", "hint", "enabled", "created_at") \
            .get()
    except Project.DoesNotExist:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到指定的问题")
    return success_api_response(obj)


@response_wrapper
@jwt_auth(perms=[CORE_QUESTION_EDIT])
@require_http_methods(["POST"])
@require_course_permission
def create_question(request: HttpRequest, course: Course):
    data: dict = parse_data(request)
    try:
        project = Project.objects.filter(pk=data["project_id"], course=course).get()
    except Project.DoesNotExist:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该课程中没有指定的项目")
    obj = Question.objects.create(name=data["name"],
                                  content=data["content"],
                                  hint=data["hint"],
                                  enabled=data["enabled"],
                                  project=project)
    return success_api_response({"id": obj.id})


@response_wrapper
@jwt_auth(perms=[CORE_QUESTION_EDIT])
@require_http_methods(["PUT"])
@require_course_permission
def edit_question(request: HttpRequest, course: Course):
    data: dict = parse_data(request)
    try:
        project = Project.objects.filter(pk=data["project_id"], course=course).get()
    except Project.DoesNotExist:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该课程中没有指定的项目")
    cnt = Question.objects.filter(id=data["id"]) \
        .update(name=data["name"], content=data["content"], hint=data["hint"], enabled=data["enabled"], project=project)
    if cnt <= 0:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到指定的问题")
    return success_api_response({})


@response_wrapper
@jwt_auth(perms=[CORE_QUESTION_EDIT])
@require_http_methods(["DELETE"])
@require_course_permission
def delete_questions(request: HttpRequest, course: Course):
    data: dict = parse_data(request)
    del_result = Question.objects.filter(id__in=data["ids"], project__course=course).delete()
    if del_result[0] <= 0:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到指定的问题")
    return success_api_response({"deleted": del_result[0]})


QUESTION_LIST_API = wrapped_api({
    'get': list_questions
})

QUESTION_CRUD_API = wrapped_api({
    'get': get_question,
    'post': create_question,
    'put': edit_question,
    'delete': delete_questions
})
