<template>
  <div>
    <Modal
      v-model="modal"
      title="添加学生"
      ok-text="提交"
      cancel-text="取消"
      @on-ok="onADD()"
      @on-cancel="modal = false"
    >
      <Form ref="classUpdate1" :model="studentUpdate" :rules="studentUpdateRule" :label-width="80">
        <form-item
          v-for="(stu, index) in studentUpdate.student"
          :key="index"
          :label="'学号' + (index + 1)"
          :prop="'student.' + index + '.value'"
          :rules="{ required: true, message: '学生不能为空', trigger: 'blur' }"
        >
          <Row>
            <Col span="18">
              <Input v-model="stu.value" type="text" />
            </Col>
            <Col span="4" offset="1">
              <Button type="primary" @click="handleRemove(index)">删除</Button>
            </Col>
          </Row>
        </form-item>
        <form-item>
          <Row>
            <Col span="12">
              <Button type="dashed" long icon="plus-round" @click="handleAdd">新增</Button>
            </Col>
          </Row>
        </form-item>
      </Form>
    </Modal>
    <Card title="班级信息" style="margin: 0">
      <Form ref="classInf" :model="classInf">
        <Row>
          <Col span="5">
            <Form-item prop="name" :rules="{ required: true, message: '信息不能为空', trigger: 'blur' }">
              名称：
              <Input v-model="classInf.name" type="text" :disabled="!isRevising" style="width: 70%" />
            </Form-item>
          </Col>
          <Col span="5">
            <Form-item prop="teacher" :rules="{ required: true, message: '信息不能为空', trigger: 'blur' }">
              教师：
              <Input v-model="classInf.teacher" type="text" :disabled="!isRevising" style="width: 70%" />
            </Form-item>
          </Col>
          <Col span="5">
            <Form-item>
              <Button type="primary" @click="onRevise">{{ buttonText }}</Button>
              <Button :style="{ display: isHidden }" @click="onReviseCancel">取消</Button>
            </Form-item>
          </Col>
        </Row>
      </Form>
    </Card>
    <br />
    <Card title="学生管理" style="margin: 0">
      <Row>
        <Col span="5">
          学号：
          <Input v-model="search.student_id" placeholder="学号" style="width: 70%" @keyup.enter.native="onSearch()" />
        </Col>
        <Col span="5">
          姓名：
          <Input v-model="search.name" placeholder="姓名" style="width: 70%" @keyup.enter.native="onSearch()" />
        </Col>
        <Col span="5">
          学院：
          <Select v-model="search.department" placeholder="学院" style="width: 70%" @on-change="onSearch()">
            <Option :value="0">全部</Option>
            <Option v-for="item in departments" :key="item.label" :value="item.value">
              {{ item.label }}
            </Option>
          </Select>
        </Col>
        <Col span="5">
          <Button type="primary" @click="onSearch()">筛选</Button>
          <Button @click="clearFilter" style="margin-left: 10px">清空筛选条件</Button>
        </Col>
        <Col offset="1" span="2">
          <Tooltip content="CSV 必须包含‘学号’列">
            <Upload :before-upload="beforeUpload" action="">
              <Button icon="ios-cloud-upload-outline" style="width: 100%">重置学生名单</Button>
            </Upload>
          </Tooltip>
        </Col>
      </Row>
      <br />
      <Row>
        <Table ref="table" :highlight-row="true" size="large" :columns="columns" :data="pageData" />
      </Row>
      <br />
      <Row>
        <Col span="18">
          <Page
            :current="pageNo"
            :total="totalCount"
            :page-size="pageSize"
            show-elevator
            show-total
            show-sizer
            @on-change="pageChange"
            @on-page-size-change="pageSizeChange"
          />
        </Col>
        <Col span="6">
          <Col offset="7" span="8">
            <Button type="success" style="width: 100%" @click="modal = true">添加学生</Button>
          </Col>
          <Col offset="1" span="8">
            <Button type="primary" style="width: 100%" @click="exportData">导出 CSV</Button>
          </Col>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { studentReq } from '@/api/student'
import { classIdReq } from '@/api/class'
import { getArrayFromFile, getErrModalOptions, getTableDataFromArray } from '@/libs/util'
import { ActionButton, Spacer, WhitePre } from '@/libs/render-item'

const updateType = {
  ADD: 1,
  DELETE: 2,
  CHANGE: 3,
  CLEAR: 4
}
export default {
  data() {
    return {
      columns: [
        {
          title: 'ID',
          render: (h, params) => h('span', params.index + (this.pageNo - 1) * this.pageSize + 1)
        },
        {
          title: '学号',
          render: (h, params) => WhitePre(h, params.row.student_id)
        },
        {
          title: '姓名',
          render: (h, params) => WhitePre(h, params.row.name)
        },
        {
          title: '学院',
          render: (h, params) => WhitePre(h, params.row.department)
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) =>
            h('div', [
              ActionButton(h, () => this.onView(params.row), '查看', false),
              Spacer(h),
              ActionButton(h, () => this.onDelete(params.row), '删除', false)
            ])
        }
      ],
      search: {},
      apiData: {},
      tableData: [],
      pageData: [],
      departments: [],
      classInf: {
        id: null,
        name: null,
        teacher: null,
        belong_to: null
      },
      totalCount: 0, // 共多少条数据
      pageNo: 1, // 当前第几页
      pageSize: 10, // 每页显示多少条数据
      isRevising: false,
      modal: false,
      studentUpdate: {
        student: [
          {
            value: ''
          }
        ]
      },
      studentUpdateRule: {
        type: [{ required: true }]
      },
      expectedColumnNames: ['学号'],
      columnNames: []
    }
  },
  computed: {
    isHidden() {
      return this.isRevising ? 'inline-block' : 'none'
    },
    buttonText() {
      return this.isRevising ? '确认' : '修改'
    },
    uploadFileReady() {
      if (!this.columnNames) {
        return false
      }
      const result = []
      for (let i = 0; i < this.expectedColumnNames.length; i++) {
        const temp = this.expectedColumnNames[i]
        for (let j = 0; j < this.columnNames.length; j++) {
          if (temp === this.columnNames[j]) {
            result.push(temp)
            break
          }
        }
      }
      return this.expectedColumnNames.every((item, index) => item === result[index])
    }
  },
  mounted() {
    this.getClassData()
  },
  methods: {
    getClassData() {
      classIdReq('get', this.$route.params.id)
        .then((res) => {
          this.apiData = res.data
          this.classInf.id = this.apiData.id
          this.classInf.name = this.apiData.name
          this.classInf.teacher = this.apiData.teacher
          this.classInf.belong_to = this.apiData.belong_to
          for (let i = 0; i < res.data.student.length; i++) {
            const option = {
              value: Number(res.data.student[i].department),
              label: Number(res.data.student[i].department)
            }
            if (JSON.stringify(this.departments).indexOf(JSON.stringify(option)) === -1) {
              this.departments.push(option)
            }
          }
          this.onSearch()
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    loadPage() {
      const newPageData = []
      for (let i = (this.pageNo - 1) * this.pageSize; i < this.pageNo * this.pageSize && i < this.tableData.length; ) {
        newPageData.push(this.tableData[i++])
      }
      this.pageData = newPageData
    },
    onRevise() {
      if (this.isRevising) {
        this.$refs['classInf'].validate((valid) => {
          if (valid) {
            classIdReq('put', this.$route.params.id, {
              type: updateType.CHANGE,
              name: this.classInf.name,
              teacher: this.classInf.teacher
            })
              .then(() => {
                this.isRevising = false
                this.$Notice.success({ title: '提交成功' })
              })
              .catch((error) => {
                this.$Modal.error(getErrModalOptions(error))
              })
          } else {
            this.$Modal.error('输入信息不能为空')
          }
        })
      } else {
        this.isRevising = true
      }
    },
    onReviseCancel() {
      this.isRevising = false
      this.classInf.id = this.apiData.id
      this.classInf.name = this.apiData.name
      this.classInf.teacher = this.apiData.teacher
      this.classInf.belong_to = this.apiData.belong_to
    },
    onSearch() {
      const newTableData = []
      this.apiData.student.forEach((element) => {
        if (this.search.student_id !== undefined && this.search.student_id.length !== 0) {
          if (element.student_id.indexOf(this.search.student_id) === -1) {
            return
          }
        }
        if (this.search.name !== undefined && this.search.name.length !== 0) {
          if (element.name.indexOf(this.search.name) === -1) {
            return
          }
        }
        if (this.search.department !== undefined && this.search.department !== 0) {
          if (element.department !== this.search.department) {
            return
          }
        }
        newTableData.push(element)
      })
      this.tableData = newTableData
      this.totalCount = this.tableData.length
      this.loadPage()
    },
    onView(student) {
      studentReq('get', student.student_id).then((res) => {
        this.$Modal.warning({
          title: `详细信息`,
          content:
            '学号：' +
            res.data.student_id +
            '<br>' +
            '姓名：' +
            res.data.name +
            '<br>' +
            '学院：' +
            res.data.department +
            '<br>' +
            '行政班级：' +
            res.data.official_class +
            '<br>' +
            '邮箱：' +
            res.data.email
        })
      })
    },
    onADD() {
      if (this.studentUpdate.student.length === 0) {
        this.$Notice.warning({ title: '未添加学生' })
        return
      }
      classIdReq('put', this.$route.params.id, {
        type: updateType.ADD,
        student: this.studentUpdate.student.map((data) => data.value)
      })
        .then(() => {
          this.getClassData()
          this.$Notice.success({ title: '添加成功' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onDelete(student) {
      this.$Modal.confirm({
        title: `确认删除`,
        content:
          '学号：' + student.student_id + '<br>' + '姓名：' + student.name + '<br>' + '学院：' + student.department,
        onOk: () => {
          classIdReq('put', this.$route.params.id, {
            type: updateType.DELETE,
            student: [student.student_id]
          })
            .then(() => {
              this.getClassData()
              this.$Notice.success({ title: '删除成功' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel() {}
      })
    },
    clearFilter() {
      this.search = {}
      this.tableData = this.apiData.student
      this.pageChange(1)
      this.loadPage()
      // this.loadFilterHeader()
    },
    exportData() {
      this.$refs.table.exportCsv({
        filename: 'student_exported',
        columns: [
          {
            title: '学号',
            key: 'student_id'
          },
          {
            title: '姓名',
            key: 'name'
          },
          {
            title: '学院',
            key: 'department',
            align: 'center'
          }
        ],
        data: this.tableData
      })
    },
    pageChange(pageNo) {
      this.pageNo = pageNo
      this.loadPage()
    },
    pageSizeChange(pageSize) {
      this.pageSize = pageSize
      this.loadPage()
    },
    handleAdd() {
      this.studentUpdate.student = this.studentUpdate.student.concat([{ value: '' }])
    },
    handleRemove(number) {
      this.studentUpdate.student = this.studentUpdate.student.filter((element, index) => {
        return index !== number
      })
    },
    async beforeUpload(file) {
      let newStudentList
      try {
        const data = await getArrayFromFile(file)
        const { columns, tableData } = getTableDataFromArray(data)
        this.columnNames = columns.map((item) => item.title)
        if (this.uploadFileReady) {
          newStudentList = tableData.map((item) => item[this.expectedColumnNames[0]])
        }
      } catch (err) {
        this.$Notice.warning({ title: '文件格式错误' })
        return // 同上
      }
      this.$Modal.confirm({
        title: '确认操作',
        content: '该操作导入新的学生名单并覆盖旧名单，确认进行该操作？',
        onOk: () => {
          classIdReq('put', this.$route.params.id, {
            type: updateType.CLEAR
          }).catch((error) => {
            setTimeout(() => {
              this.$Modal.error(getErrModalOptions(error))
            }, 500)
          })
          classIdReq('put', this.$route.params.id, {
            type: updateType.ADD,
            student: newStudentList
          })
            .then(() => {
              this.getClassData()
              this.$Notice.success({ title: '重置成功' })
            })
            .catch((error) => {
              setTimeout(() => {
                this.$Modal.error(getErrModalOptions(error))
              }, 500)
            })
        },
        onCancel() {}
      })
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Notice.success({ title: '提交成功' })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
          return
        }
      })
    }
  }
}
</script>
