"""
declare Problem model
"""
import datetime
from django.contrib.auth import get_user_model
from django.db import models
from django.utils.timezone import utc

from judge.models.admin_uploaded_file import AdminUploadedFile
from judge.models.permissions import (CHANGE_PROBLEM, CREATE_PROBLEM,
                                      DELETE_PROBLEM, VIEW_PROBLEM)
from judge.models.test_case import TestCase

PROBLEM_TYPE_SUBMIT_FILE = 0
PROBLEM_TYPE_SINGLE_CHOICE = 1
PROBLEM_TYPE_MULTIPLE_CHOICE = 2
PROBLEM_TYPE_BLANK_FILLING = 3


class Problem(models.Model):
    """
    A list of test point for a specific problem.
    """
    PROBLEM_TYPE = [
        (PROBLEM_TYPE_SUBMIT_FILE, '提交文件题'),
        (PROBLEM_TYPE_SINGLE_CHOICE, '单选题'),
        (PROBLEM_TYPE_MULTIPLE_CHOICE, '多选题'),
        (PROBLEM_TYPE_BLANK_FILLING, '填空题'),
    ]

    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True)
    reset_time = models.DateTimeField(default=datetime.datetime(1970, 1, 1, 0, 0, tzinfo=utc))

    test_cases = models.ManyToManyField(TestCase, blank=True)
    problem_data = models.ForeignKey(AdminUploadedFile, on_delete=models.SET_NULL, null=True)
    type = models.IntegerField(choices=PROBLEM_TYPE, default=PROBLEM_TYPE_SUBMIT_FILE)
    answer = models.TextField(null=True)
    interval = models.IntegerField(blank=True, null=True)
    max_submission = models.IntegerField(blank=True, null=True)
    explain = models.TextField(null=True)

    def __str__(self):
        return self.name

    class Meta:
        default_permissions = ()
        permissions = [
            (CHANGE_PROBLEM, CHANGE_PROBLEM),
            (CREATE_PROBLEM, CREATE_PROBLEM),
            (DELETE_PROBLEM, DELETE_PROBLEM),
            (VIEW_PROBLEM, VIEW_PROBLEM)
        ]
