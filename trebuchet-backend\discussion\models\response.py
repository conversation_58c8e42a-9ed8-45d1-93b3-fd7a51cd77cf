"""
declare Response model
"""

from django.contrib.auth.models import User
from django.db import models

from discussion.models import Discussion


class Response(models.Model):
    author = models.ForeignKey(
        User, on_delete=models.CASCADE, null=False, db_index=True
    )
    content = models.TextField()
    discussion = models.ForeignKey(
        Discussion, on_delete=models.CASCADE, default=False, db_index=True
    )
    authority = models.BooleanField(default=False)
    deleted = models.BooleanField(default=False, null=False)
    created_at = models.DateTimeField(blank=False, null=False, auto_now_add=True)
