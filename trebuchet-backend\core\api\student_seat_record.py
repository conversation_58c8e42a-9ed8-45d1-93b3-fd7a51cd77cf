"""
student seat arrangement
"""
import pandas
from django.db import transaction
from django.http import HttpResponse
from django.http.request import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.exam_queue import _get_course_current_exam
from core.api.permissions import (CORE_SEAT_RECORD_CHANGE,
                                  CORE_SEAT_RECORD_CREATE,
                                  CORE_SEAT_RECORD_DELETE,
                                  CORE_SEAT_RECORD_VIEW)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            response_wrapper, success_api_response,
                            validate_args, wrapped_api, assign_exam_object)
from core.models.exam import Exam
from core.models.seat import Seat
from core.models.student import Student
from core.models.student_seat_record import StudentSeatRecord
from core.models.user_profile import UserProfile


def validate_create_request(request: HttpRequest) -> bool:
    """validate seat arrangement request
    """
    data: dict = parse_data(request)
    allowed_fields = {'name', 'data'}
    if data is None or not data.keys() <= allowed_fields:
        return False
    pairs = data.pop('data', None)
    if pairs is not None:
        # 输入重复出现学生或座位
        students = list(map(lambda x: x.get('student'), pairs))
        seats = list(map(lambda x: x.get('seat'), pairs))
        if len(students) != len(set(students)) or len(seats) != len(set(seats)):
            return False
        # 输入学生不存在
        if len(students) != Student.objects.filter(student_id__in=students).count():
            return False
        # 输入座位不存在
        if len(seats) != Seat.objects.filter(pk__in=seats).count():
            return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_RECORD_CREATE])
@require_POST
@validate_args(func=validate_create_request)
def create_exam_arrangement(request, exam_id):
    """create seat arrangement for exam

    [method]: POST

    [route]: /api/seat_arrangements/<int:exam_id>
    """
    body = parse_data(request)
    exam = Exam.objects.get(pk=exam_id)
    for pair in body.get('data'):
        new_student = Student.objects.get(student_id=pair.get('student'))
        new_seat = Seat.objects.get(pk=pair.get('seat'))
        StudentSeatRecord.objects.create(
            student=new_student, seat=new_seat, exam=exam)
    return success_api_response({'result': 'OK, student seat record added'})


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_RECORD_VIEW])
@require_GET
def get_exam_arrangement(request, exam_id):
    """get an arrangement table

    [method]: GET

    [route]: /api/seat_arrangements/<int:exam_id>
    """
    exam = Exam.objects.get(pk=exam_id)
    records = StudentSeatRecord.objects.filter(exam=exam)
    res = []
    for record in records:
        pair = {
            "student": record.student.student_id,
            "seat": {
                "id": record.seat.id,
                "name": record.seat.name,
                "room": {
                    "id": record.seat.room.id,
                    "name": record.seat.room.name,
                }
            }
        }
        res.append(pair)
    data = {
        "exam": exam.id,
        "data": res
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_RECORD_DELETE])
@require_http_methods(['DELETE'])
def delete_exam_arrangement(request, exam_id):
    """delete an arrangement

    [method]: DELETE

    [route]: /api/seat_arrangements/<int:exam_id>
    """
    exam = Exam.objects.get(pk=exam_id)
    StudentSeatRecord.objects.filter(exam=exam).delete()
    return success_api_response({"result": "OK, this arrangement was deleted"})


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_RECORD_CHANGE])
@require_http_methods(['PUT'])
def put_student_seat(request, student_id):
    """update single student seat record

    [method]: PUT

    [route]: /api/student-seat/<str:student_id>
    """
    student = Student.objects.get(student_id=student_id)
    if student is None:
        return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)
    body = parse_data(request)
    course_id = UserProfile.objects.filter(user=request.user).first().course_id
    exam: Exam = _get_course_current_exam(course_id)
    if exam is None:
        return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)

    with transaction.atomic():
        seat = Seat.objects.select_for_update().get(pk=body.get('seat'))

        if StudentSeatRecord.objects.filter(exam=exam, seat=seat).count() > 0:
            return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该座位上已经有学生，请刷新后重试")

        ssr = StudentSeatRecord.objects.select_for_update().get(student=student, exam=exam)
        ssr.seat = seat
        ssr.save()

    return success_api_response({"result": "OK, the record updated"})


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_RECORD_VIEW])
@require_http_methods(['GET'])
@assign_exam_object
def get_student_seat(request, exam: Exam, student_id):
    """query single student seat record

    [method]: GET

    [route]: /api/student-seat/<str:student_id>
    """
    student = Student.objects.get(student_id=student_id)
    if student is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "no such student")
    ssr = StudentSeatRecord.objects.filter(student=student, exam=exam).first()
    if ssr is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "the students has no seat in the exam")
    seat = ssr.seat
    return success_api_response({"room_id": seat.room.id,
                                 "room_name": seat.room.name,
                                 "name": seat.name,
                                 "pos_x": seat.pos_x,
                                 "pos_y": seat.pos_y})


@jwt_auth(perms=[CORE_SEAT_RECORD_VIEW])
@require_GET
def get_student_seat_csv(request: HttpRequest, exam_id: int):
    """get student seat arrangement, and render it as panda

    [method]: GET

    [route]: /api/seat_arrangements/<int:exam_id>/csv
    """
    exam = Exam.objects.filter(pk=exam_id).first()
    if exam is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "No such exam.")
    records = StudentSeatRecord.objects.filter(exam=exam)
    data = list(records.values("seat__room__name", "seat__name", "seat__pos_x",
                               "seat__pos_y", "student__student_id", "student__name"))
    return HttpResponse(pandas.DataFrame(data).to_csv())


ARRANGEMENT_DETAIL_API = wrapped_api({
    "post": create_exam_arrangement,
    "get": get_exam_arrangement,
    "delete": delete_exam_arrangement
})

STUDENT_SEAT_API = wrapped_api({
    'get': get_student_seat,
    'put': put_student_seat,
})
