"""
declare RejudgedTestCaseJudgeRecord model
"""
from django.db import models
from nose.tools import nottest

from judge.models.test_case import TestCase
from judge.constants import TEST_CASE_RESULT_TAG_CHOICES


@nottest
class RejudgedTestCaseJudgeRecord(models.Model):
    """
    Result of single test point.
    """
    problem_judge_record = models.ForeignKey('judge.RejudgedProblemJudgeRecord', on_delete=models.CASCADE)
    test_case = models.ForeignKey(TestCase, on_delete=models.CASCADE)
    judge_result = models.IntegerField(choices=TEST_CASE_RESULT_TAG_CHOICES)
    raw_output = models.TextField(blank=True, null=True)
    comment = models.TextField()
    started_at = models.DateTimeField(blank=True, null=True)
    finished_at = models.DateTimeField(blank=True, null=True)
