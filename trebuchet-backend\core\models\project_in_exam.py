# pylint: skip-file
"""
govern the many-to-many relationship between the Exam and the Project
"""
from django.db import models

from core.models.exam import Exam
from core.models.permissions import (PIE_CHANGE, PIE_CREATE, PIE_DELETE,
                                     PIE_VIEW)
from core.models.project import Project


class ProjectInExam(models.Model):
    """This model describes a project in the exam.

    Attributes:
        begin_time: A TimeField storing the begin time of the exam. (The date
            is stored in the Exam)
        duration: A IntegerField storing the duration of the exam in minutes.
        pass_requirement: A Char<PERSON>ield storing the rule set that used
            to determine if a student is eligible to pass the exam.
        question: A TextField storing the questions of the exam in json.
    """
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    begin_time = models.TimeField()
    duration = models.IntegerField(default=0)
    pass_requirement = models.CharField(max_length=200, null=True)
    mark_requirement = models.CharField(max_length=200, null=True)
    problems = models.ManyToManyField(to='judge.problem')
    download_problems = models.ManyToManyField(to='judge.problem', related_name='download_problems')
    description = models.TextField(null=True)
    is_open_for_students = models.BooleanField(default=True)
    question = models.TextField(null=True)
    # 错误分析
    fail_analysis = models.TextField(null=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (PIE_CHANGE, PIE_CHANGE),
            (PIE_CREATE, PIE_CREATE),
            (PIE_DELETE, PIE_DELETE),
            (PIE_VIEW, PIE_VIEW)
        ]
