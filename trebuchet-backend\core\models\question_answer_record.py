from django.db import models

from core.models.exam_record import ExamRecord
from core.models.question import Question


class QuestionAnswerRecord(models.Model):
    """
    课上检查问题的回答记录
    """
    constraints = [
        models.UniqueConstraint(fields=['question', 'student_record'], name='每次记录同样的问题只能回答一次')
    ]
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    student_record = models.ForeignKey(ExamRecord, on_delete=models.CASCADE)
    grade = models.IntegerField(default=50)
    comment = models.TextField()
