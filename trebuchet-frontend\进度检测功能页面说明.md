# 进度检测功能页面说明

## 概述

根据《进度检测功能设计报告》，已创建了两个前端页面来展示4个进度检测功能：

## 1. 考试管理-进度检测页面

**路径**: `/exam/progress-detection`  
**文件**: `trebuchet-frontend/src/view/exam/progress/progress-detection.vue`

### 包含功能：

#### 功能1：进度慢检测
- **参数设置**: 提交次数阈值、统计天数范围
- **数据展示**: 学号、姓名、提交次数、最后提交时间、当前进度
- **特色**: 
  - 提交次数用不同颜色标签显示（红色<3次，橙色3-5次，绿色>5次）
  - 支持一键导出CSV
  - 显示检测结果统计

#### 功能3：多次挂在同一个P检测
- **参数设置**: 失败次数阈值
- **数据展示**: 学号、姓名、当前P、失败次数、首次/最近失败时间
- **特色**: 
  - 失败次数用颜色标签区分严重程度
  - 支持导出功能

#### 功能4：多次没有课上资格检测
- **参数设置**: 失败次数阈值
- **数据展示**: 学号、姓名、当前P、失去资格次数、最近考试情况
- **特色**: 
  - 参加状态用红绿标签区分
  - 失去资格次数颜色编码

## 2. 课上信息-进度检测页面

**路径**: `/on-exam/exam-progress-detection`  
**文件**: `trebuchet-frontend/src/view/on-exam/exam-progress-detection.vue`

### 包含功能：

#### 功能2：考试通过人数检测及无人通过警报
- **考试选择**: 下拉选择具体考试
- **实时监控**: 
  - 总参考人数、已通过人数、通过率、考试持续时间
  - 30秒自动刷新数据
  - 开始/停止监控按钮
- **警报系统**: 
  - 考试超过30分钟无人通过时显示红色警报
  - 实时状态指示器
- **数据展示**:
  - **已通过学生**: 学号、姓名、通过时间、用时、项目、得分
  - **未通过学生**: 学号、姓名、当前状态、开始时间、已用时、当前得分
  - **考试进度统计**: 预留图表展示区域

## 页面特色

### UI设计特点：
1. **统一风格**: 使用iView组件库，与现有系统保持一致
2. **响应式布局**: 使用栅格系统，适配不同屏幕
3. **颜色编码**: 用不同颜色标签直观显示数据状态
4. **交互友好**: 加载状态、操作反馈、数据验证

### 功能特点：
1. **模拟数据**: 所有数据都是精心设计的占位内容
2. **完整交互**: 按钮点击、标签切换、数据刷新等
3. **导出功能**: CSV导出功能已实现
4. **实时监控**: 定时器模拟实时数据更新

## 访问方式

### 开发环境访问：
1. 启动前端项目：`npm run serve-dev-local`
2. 访问考试管理页面：导航栏 → 考试管理 → 进度检测
3. 访问课上信息页面：导航栏 → 课上信息 → 进度检测

### 演示数据说明：
- 所有学生数据、考试数据、统计数据均为模拟数据
- 数据设计贴近真实场景，便于展示功能效果
- 包含各种边界情况（如无数据、异常状态等）

## 技术实现

### 组件使用：
- `Card`: 页面容器
- `Tabs/TabPane`: 功能分组
- `Table`: 数据展示
- `Form/FormItem`: 参数设置
- `Button`: 操作按钮
- `Alert`: 警报提示
- `Tag`: 状态标签
- `Modal`: 弹窗提示

### 数据处理：
- 模拟异步加载（setTimeout）
- 数据状态管理
- 错误处理机制
- CSV导出实现

## 后续开发建议

当后端API开发完成后，只需要：
1. 创建对应的API请求函数
2. 替换模拟数据加载逻辑
3. 添加错误处理
4. 调整数据字段映射

页面结构和UI交互无需修改，可直接对接真实API。
