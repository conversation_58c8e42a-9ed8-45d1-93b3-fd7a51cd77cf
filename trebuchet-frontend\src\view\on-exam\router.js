import Main from '@/view/index/main'

export const onExamRouter = {
  path: '/on-exam',
  name: 'on-exam',
  component: Main,
  meta: {
    title: '课上信息',
    icon: 'ios-time',
    jumpRoute: '/on-exam/overview'
  },
  children: [
    {
      path: 'overview',
      name: 'overview',
      meta: {
        title: '考场信息总览'
      },
      component: () => import('@/view/on-exam/overview')
    },
    {
      path: 'timetable',
      name: 'timetable',
      meta: {
        title: '考场信息展示'
      },
      component: () => import('@/view/on-exam/timetable')
    },
    {
      path: 'exam-progress-detection',
      name: 'exam_progress_detection',
      meta: {
        title: '进度检测'
      },
      component: () => import('@/view/on-exam/exam-progress-detection')
    }
  ]
}
