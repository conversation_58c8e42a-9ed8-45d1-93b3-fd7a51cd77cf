# Generated by Django 2.2.6 on 2019-12-10 12:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('judge', '0003_auto_20191204_2222'),
    ]

    operations = [
        migrations.CreateModel(
            name='RejudgedProblemJudgeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin_id', models.IntegerField(blank=True, null=True)),
                ('edx_username', models.CharField(max_length=50)),
                ('student_comment', models.CharField(blank=True, max_length=400, null=True)),
                ('judger_identifier', models.CharField(blank=True, max_length=20, null=True)),
                ('judge_result',
                 models.IntegerField(choices=[(-1, 'Judging'), (0, 'Passed'), (1, 'Failed')], default=-1)),
                ('course_code', models.CharField(blank=True, max_length=100, null=True)),
                ('chapter_code', models.CharField(blank=True, max_length=100, null=True)),
                ('project_in_exam', models.IntegerField(default=-1)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('attachment', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                 to='judge.UserSubmittedFile')),
                ('original_record',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='judge.ProblemJudgeRecord')),
                ('problem', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='judge.Problem')),
            ],
            options={
                'permissions': [('重新评测', '重新评测')],
                'default_permissions': (),
            },
        ),
        migrations.CreateModel(
            name='RejudgedTestCaseJudgeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('judge_result', models.IntegerField(
                    choices=[(-1, 'Judging'), (0, 'Accepted'), (1, 'Wrong Answer'), (2, 'Runtime Error'),
                             (3, 'Memory Limit Exceeded'), (4, 'Time Limit Exceeded'), (5, 'Function Limit Violation'),
                             (6, 'Compilation Error'), (7, 'Presentation Error'), (8, 'Unknown Error')])),
                ('raw_output', models.TextField(blank=True, null=True)),
                ('comment', models.TextField()),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('problem_judge_record',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='judge.RejudgedProblemJudgeRecord')),
                ('test_case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='judge.TestCase')),
            ],
        ),
    ]
