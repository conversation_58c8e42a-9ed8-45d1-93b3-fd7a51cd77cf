import Main from '@/view/index/main'
import SubMain from '@/view/index/sub-main'

export const judgeRouter = {
  path: '/judge',
  name: 'judge',
  component: Main,
  meta: {
    title: '评测相关',
    icon: 'ios-stats',
    jumpRoute: '/judge/judge-table'
  },
  children: [
    {
      path: 'judge-table',
      name: 'judge_table',
      meta: {
        title: '评测记录'
      },
      component: () => import('@/view/judge/record/judge-table')
    },
    {
      path: 'judge-detail/:id',
      name: 'judge_detail',
      meta: {
        title: '详细信息',
        hideInMenu: true
      },
      component: () => import('@/view/judge/record/judge-detail')
    },
    {
      path: 'problem-table',
      name: 'problem_table',
      meta: {
        title: '题目列表'
      },
      component: () => import('@/view/judge/problem/problem-table')
    },
    {
      path: 'problem-detail/:id',
      name: 'problem_detail',
      meta: {
        title: '题目详情',
        hideInMenu: true
      },
      component: () => import('@/view/judge/problem/problem-detail')
    },
    {
      path: 'testcase-table',
      name: 'testcase_table',
      meta: {
        title: '测试点列表'
      },
      component: () => import('@/view/judge/testcase/testcase-table')
    },
    {
      path: 'testcase-detail/:id',
      name: 'testcase_detail',
      meta: {
        title: '测试点详情',
        hideInMenu: true
      },
      component: () => import('@/view/judge/testcase/testcase-detail')
    },
    {
      path: 'statistics',
      name: 'statistics',
      component: SubMain,
      meta: {
        title: '评测数据',
        jumpRoute: '/judge/statistics/statistics-index'
      },
      children: [
        {
          path: 'statistics-index',
          name: 'statistics_index',
          meta: {
            title: '考试情况面板'
          },
          component: () => import('@/view/judge/statistics/statistics-index')
        },
        {
          path: 'project-statistics',
          name: 'project_statistics',
          meta: {
            title: '考试信息统计'
          },
          component: () => import('@/view/judge/statistics/project-index')
        },
        {
          path: 'statistic-detail/:id',
          name: 'statistics_detail',
          meta: {
            title: '评测数据统计',
            hideInMenu: true
          },
          component: () => import('@/view/judge/statistics/statistic-detail')
        }
      ]
    },
    {
      path: 'rejudge',
      name: 'rejudge',
      component: SubMain,
      meta: {
        title: '重测相关',
        jumpRoute: '/judge/rejudge/batch-rejudge'
      },
      children: [
        {
          path: 'batch-rejudge',
          name: 'batch_rejudge',
          meta: {
            title: '批量重测'
          },
          component: () => import('@/view/judge/rejudge/batch-rejudge')
        },
        {
          path: 'rejudge-record-list',
          name: 'rejudge_record_list',
          meta: {
            title: '重测记录总览'
          },
          component: () => import('@/view/judge/rejudge/rejudge-record-list')
        }
      ]
    }
  ]
}
