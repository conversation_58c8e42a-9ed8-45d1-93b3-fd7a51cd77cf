{"ast": null, "code": "import { getRequest } from '@/api/util';\nexport const modifyProgressReq = (method, cid, params) => {\n  return getRequest(`/api/progress/${cid}`, method, params);\n};\n\n// 进度检测相关API\nexport const slowDetectionReq = (coursePk, params) => {\n  return getRequest(`/api/progress/slow-detection/${coursePk}`, 'get', params);\n};\nexport const examPassDetectionReq = examPk => {\n  return getRequest(`/api/progress/exam-pass-detection/${examPk}`, 'get');\n};\nexport const repeatedFailuresReq = (coursePk, params) => {\n  return getRequest(`/api/progress/repeated-failures/${coursePk}`, 'get', params);\n};\nexport const qualificationFailuresReq = (coursePk, params) => {\n  return getRequest(`/api/progress/qualification-failures/${coursePk}`, 'get', params);\n};", "map": {"version": 3, "names": ["getRequest", "modifyProgressReq", "method", "cid", "params", "slowDetectionReq", "coursePk", "examPassDetectionReq", "examPk", "repeatedFailuresReq", "qualificationFailuresReq"], "sources": ["E:/CO/助教/dev projects/trebuchet-frontend/src/api/progress.js"], "sourcesContent": ["import { getRequest } from '@/api/util'\r\n\r\nexport const modifyProgressReq = (method, cid, params) => {\r\n  return getRequest(`/api/progress/${cid}`, method, params)\r\n}\r\n\r\n// 进度检测相关API\r\nexport const slowDetectionReq = (coursePk, params) => {\r\n  return getRequest(`/api/progress/slow-detection/${coursePk}`, 'get', params)\r\n}\r\n\r\nexport const examPassDetectionReq = (examPk) => {\r\n  return getRequest(`/api/progress/exam-pass-detection/${examPk}`, 'get')\r\n}\r\n\r\nexport const repeatedFailuresReq = (coursePk, params) => {\r\n  return getRequest(`/api/progress/repeated-failures/${coursePk}`, 'get', params)\r\n}\r\n\r\nexport const qualificationFailuresReq = (coursePk, params) => {\r\n  return getRequest(`/api/progress/qualification-failures/${coursePk}`, 'get', params)\r\n}\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,YAAY;AAEvC,OAAO,MAAMC,iBAAiB,GAAG,CAACC,MAAM,EAAEC,GAAG,EAAEC,MAAM,KAAK;EACxD,OAAOJ,UAAU,CAAE,iBAAgBG,GAAI,EAAC,EAAED,MAAM,EAAEE,MAAM,CAAC;AAC3D,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,CAACC,QAAQ,EAAEF,MAAM,KAAK;EACpD,OAAOJ,UAAU,CAAE,gCAA+BM,QAAS,EAAC,EAAE,KAAK,EAAEF,MAAM,CAAC;AAC9E,CAAC;AAED,OAAO,MAAMG,oBAAoB,GAAIC,MAAM,IAAK;EAC9C,OAAOR,UAAU,CAAE,qCAAoCQ,MAAO,EAAC,EAAE,KAAK,CAAC;AACzE,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,CAACH,QAAQ,EAAEF,MAAM,KAAK;EACvD,OAAOJ,UAAU,CAAE,mCAAkCM,QAAS,EAAC,EAAE,KAAK,EAAEF,MAAM,CAAC;AACjF,CAAC;AAED,OAAO,MAAMM,wBAAwB,GAAG,CAACJ,QAAQ,EAAEF,MAAM,KAAK;EAC5D,OAAOJ,UAAU,CAAE,wCAAuCM,QAAS,EAAC,EAAE,KAAK,EAAEF,MAAM,CAAC;AACtF,CAAC"}, "metadata": {}, "sourceType": "module"}