"""
form definitions of core models
"""

from django import forms


class StudentBasicInfo(forms.Form):
    """
    for uploaded Student object
    """
    student_id = forms.CharField(max_length=25, required=True)
    name = forms.CharField(max_length=50, required=True)
    department = forms.IntegerField(required=True)
    official_class = forms.CharField(max_length=25, required=True)
    email = forms.EmailField(max_length=50, required=False)
