<template>
  <Card>
    <Row>
      <Col span="6">
        <Row>
          <Card>
            <p slot="title">基本信息</p>
            <p><b>用户 ID: </b>{{ user.id }}</p>
            <p><b>用户名: </b>{{ user.username }}</p>
            <p><b>邮箱: </b>{{ user.email }}</p>
          </Card>
        </Row>
        <br />
        <br />
        <Row>
          <Card>
            <p slot="title">设置用户资料</p>
            <Form :label-width="80">
              <form-item label="当前课程">
                <Select v-model="nowCourse" @on-change="onChange">
                  <Option v-for="course in allCourse" :key="course.id" :value="course.id">
                    {{ course.name }}
                  </Option>
                </Select>
              </form-item>
              <form-item label="全名">
                <Input v-model="fullName" placeholder="例: 张三" />
              </form-item>
              <form-item label="角色">
                <Input v-model="roleName" placeholder="例: 助教" />
              </form-item>
              <form-item>
                <Button type="primary" @click="handleCourse">提交</Button>
              </form-item>
            </Form>
          </Card>
        </Row>
      </Col>
      <Col offset="1" span="16">
        <Card>
          <p slot="title">修改权限</p>
          <Form :label-width="40">
            <form-item>
              有权限课程：
              <Select v-model="authorizedCourses" multiple style="width: 81%">
                <Option v-for="course in allCourse" :key="course.id" :value="course.id">
                  {{ course.name }}
                </Option>
              </Select>
            </form-item>
            <form-item>
              <Transfer
                :list-style="{ width: '300px' }"
                :data="allGroups"
                :target-keys="user.groups"
                :titles="['可用分组', '当前分组']"
                filterable
                @on-change="handleGroupChange"
              />
            </form-item>
            <form-item>
              <Transfer
                :list-style="{ width: '300px' }"
                :data="allRoles"
                :target-keys="user.permissions"
                :titles="['剩余可选权限', '现有权限']"
                filterable
                @on-change="handleChange"
              />
            </form-item>
            <form-item>
              <Button type="primary" @click="handleSubmit">提交</Button>
            </form-item>
          </Form>
        </Card>
      </Col>
    </Row>
  </Card>
</template>

<script>
import { getErrModalOptions } from '@/libs/util'
import {
  userIdReq,
  authorizedCoursesReq,
  getUserRole,
  userGroupListReq,
  userIdProfileReq,
  userProfileReq
} from '@/api/user'
import { courseReq } from '@/api/course'
import _ from 'lodash'

export default {
  name: 'UserDetail',
  data() {
    return {
      user: {},
      allRoles: [],
      allGroups: [],
      userPermission: [],
      allCourse: [],
      authorizedCourses: [],
      nowCourse: null,
      fullName: null,
      roleName: null
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      userGroupListReq()
        .then((res) => {
          this.allGroups = _.map(res.data.groups, ({ id, name }) => ({ key: id, label: name }))
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      userIdReq('get', this.$route.params.id, {})
        .then((res) => {
          this.user = {
            ...res.data,
            groups: _.map(res.data.groups, ({ id }) => id)
          }
          getUserRole()
            .then((res) => {
              this.allRoles = res.data.all.map((item) => {
                return {
                  key: item,
                  label: item
                }
              })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      courseReq('get', {})
        .then((res) => {
          this.allCourse = res.data.data
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      userIdProfileReq('get', this.$route.params.id)
        .then((res) => {
          this.fullName = res.data.first_name
          this.roleName = res.data.last_name
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.nowCourse = res.data.course.id
          } else {
            this.$Modal.info({
              title: '请选择当前课程'
            })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      authorizedCoursesReq('get', this.$route.params.id).then((res) => {
        this.authorizedCourses = res.data.courses.map((item) => item.id)
      })
    },
    onChange(id) {
      this.nowCourse = id
    },
    async handleCourse() {
      try {
        if (this.nowCourse === null) {
          this.$Notice.warning({ title: '未选择课程' })
        } else {
          await userIdProfileReq('put', this.$route.params.id, {
            course_id: this.nowCourse,
            first_name: this.fullName,
            last_name: this.roleName
          })
          this.$Notice.success({ title: '设置成功' })
          let ret = await userProfileReq('get')
          if (ret.data.course !== null && Object.keys(ret.data.course).length !== 0) {
            this.$store.commit('user/setUserDefaultCourse', ret.data.course.id)
          }
        }
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    handleChange(targetKeys) {
      this.user.permissions = targetKeys
    },
    handleGroupChange(targetKeys) {
      this.user.groups = targetKeys
    },
    async handleSubmit() {
      try {
        await userIdReq('put', this.$route.params.id, {
          permission: this.user.permissions,
          groups: this.user.groups
        })
        await authorizedCoursesReq('put', this.$route.params.id, {
          courses: this.authorizedCourses
        })
        this.$Notice.success({ title: '修改成功' })
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    }
  }
}
</script>
