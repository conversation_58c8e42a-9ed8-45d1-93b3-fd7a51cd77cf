<template>
  <div>
    <Table
      ref="table"
      :columns="columns"
      :data="includeRetakers ? tableData : tableDataNoRetaker"
      :span-method="handleSpan"
      border
      stripe
    />
    <Button type="primary" size="large" @click="exportData" style="margin-top: 18px">导出归档数据</Button>
  </div>
</template>

<script>
import _ from 'lodash'

const getTotalCount = (obj) => {
  return Object.values(obj).reduce((a, b) => a + b, 0)
}

export default {
  name: 'PassrateTable',
  props: {
    examData: {
      type: Object,
      required: true
    },
    departmentCnt: {
      type: Object,
      required: true
    },
    courseName: {
      type: String,
      required: true
    },
    includeRetakers: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      CourseList: [],
      tableData: [],
      tableDataNoRetaker: [],
      columns: [],
      spanData: {},
      passed: {},
      total: {}
    }
  },
  created() {
    this.init()
    this.calculatePassRate(false)
    this.passed = {}
    this.total = {}
    this.calculatePassRate(true)
    this.getSpan()
  },
  methods: {
    compareDepartment(a, b) {
      let priority = { 6: 0, 23: 1, 18: 2 }
      if (a === 'total' || b === 'total') {
        return a === 'total' ? 1 : -1
      } else if (priority[b] === undefined && priority[a] === undefined) {
        return parseInt(a) - parseInt(b)
      } else if (priority[b] === undefined) {
        return -1
      } else if (priority[a] === undefined) {
        return 1
      } else {
        return priority[a] - priority[b]
      }
    },
    initProjectPassedData(project, department, data) {
      this.passed[project][department] = {
        normal_student: data[department].normal_student.passed.count,
        retaker: data[department].retaker.passed.count,
        total: data[department].normal_student.passed.count + data[department].retaker.passed.count
      }
    },
    calculatePassData(project, department, data) {
      if (!this.passed[project][department]) {
        this.initProjectPassedData(project, department, data)
      } else {
        this.passed[project][department].normal_student += data[department].normal_student.passed.count
        this.passed[project][department].retaker += data[department].retaker.passed.count
        this.passed[project][department].total =
          this.passed[project][department].normal_student + this.passed[project][department].retaker
      }
      this.total[project].normal_student += data[department].normal_student.passed.count
      this.total[project].retaker += data[department].retaker.passed.count
      this.total[project].total = this.total[project].normal_student + this.total[project].retaker
    },
    calculatePassRate(calcRetaker) {
      const projectsData = {}
      let tmpColumns = [
        { title: 'project', key: 'project', width: 80, fixed: 'left' },
        { title: '院系', key: 'department', width: 80, fixed: 'left' }
      ]
      let tmpTableData = []
      for (const exam in this.examData) {
        tmpColumns.push({
          title: this.examData[exam].date,
          children: [
            { title: '通过人数', key: this.examData[exam].date + '_passed', minWidth: 80 },
            { title: '通过率', key: this.examData[exam].date + '_passrate', minWidth: 80 }
          ]
        })
        for (const project in this.examData[exam]) {
          if (project === 'date') continue
          if (!projectsData[project]) projectsData[project] = {}
          if (!this.passed[project]) {
            this.passed[project] = {}
            this.total[project] = { normal_student: 0, retaker: 0, total: 0 }
          }
          let data = this.examData[exam][project]
          for (const department in data) {
            if (department === 'passed' || department === 'total' || department === 'retaker') continue
            if (!projectsData[project][department]) projectsData[project][department] = {}
            this.calculatePassData(project, department, data)
            let passed = calcRetaker
              ? this.passed[project][department].total
              : this.passed[project][department].normal_student
            let total = this.departmentCnt.normal[department]
            if (calcRetaker) {
              total += this.departmentCnt.retakers[department]
            }
            let passrate = this.toPercent(passed / total)
            projectsData[project][department][this.examData[exam].date + '_passed'] = passed
            projectsData[project][department][this.examData[exam].date + '_passrate'] = passrate
          }
          if (!projectsData[project]['total']) projectsData[project]['total'] = {}
          let passed_cnt = calcRetaker ? this.total[project].total : this.total[project].normal_student
          projectsData[project]['total'][this.examData[exam].date + '_passed'] = passed_cnt
          let total = getTotalCount(this.departmentCnt.normal)
          if (calcRetaker) {
            total += getTotalCount(this.departmentCnt.retakers)
          }
          projectsData[project]['total'][this.examData[exam].date + '_passrate'] = this.toPercent(passed_cnt / total)
        }
      }
      for (const item in projectsData) {
        let departmentsData = projectsData[item]
        for (const v in departmentsData) {
          tmpTableData.push(_.assign({ project: item, department: v }, departmentsData[v]))
        }
        tmpTableData = tmpTableData.sort((a, b) =>
          a.project.localeCompare(b.project) === 0
            ? this.compareDepartment(a.department, b.department)
            : a.project.localeCompare(b.project)
        )
      }
      for (const line in tmpTableData) {
        if (tmpTableData[line].project === 'Pre') continue
        let flag = false
        let lastExamId
        for (const examId in this.examData) {
          let date = this.examData[examId].date
          if (tmpTableData[line][date + '_passed'] === undefined && flag) {
            let lastDate = this.examData[lastExamId].date
            tmpTableData[line][date + '_passed'] = tmpTableData[line][lastDate + '_passed']
            tmpTableData[line][date + '_passrate'] = tmpTableData[line][lastDate + '_passrate']
          } else {
            flag = true
          }
          lastExamId = examId
        }
      }
      this.columns = tmpColumns
      if (calcRetaker) {
        this.tableData = tmpTableData
      } else {
        this.tableDataNoRetaker = tmpTableData
      }
    },
    init() {
      this.tableData = []
      this.tableDataNoRetaker = []
      this.columns = []
      this.spanData = {}
      this.passed = {}
      this.total = {}
    },
    getSpan() {
      let data = {}
      this.tableData.forEach((item, index) => {
        if (data[item.project] === undefined) {
          data[item.project] = {}
          data[item.project].start = index
        }
        if (index + 1 < this.tableData.length && this.tableData[index + 1].project !== item.project) {
          data[item.project].end = index
        }
        if (index + 1 === this.tableData.length) {
          data[item.project].end = index
        }
      })
      this.spanData = data
    },
    handleSpan({ row, rowIndex, columnIndex }) {
      let data = this.spanData
      if (columnIndex === 0) {
        if (data[row.project].start === rowIndex) {
          return [data[row.project].end - data[row.project].start + 1, 1]
        } else {
          return [0, 1]
        }
      }
    },
    toPercent(point) {
      let percent = Number(point * 100).toFixed(1)
      percent += '%'
      return percent
    },
    exportData() {
      const filename = this.includeRetakers ? ' (含重修生)' : ' (不含重修生)'
      this.$refs.table.exportCsv({ filename: this.courseName + filename })
    }
  }
}
</script>
