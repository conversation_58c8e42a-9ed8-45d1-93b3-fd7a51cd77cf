"""
define judge record related validate form
"""
from django import forms
from nose.tools import nottest

from judge.constants import USERNAME_MAX_LENGTH, STUDENT_COMMENT_LENGTH, \
    JUDGER_IDENTIFIER_MAX_LENGTH, JUDGER_COURSE_CODE_LENGTH, \
    JUDGER_CHAPTER_CODE_LENGTH


class ProblemJudgeRecordInfo(forms.Form):
    """
    define problem judge record related validate form
    """
    origin_id = forms.IntegerField(required=False)
    edx_username = forms.CharField(max_length=USERNAME_MAX_LENGTH)
    problem = forms.IntegerField(required=False)
    attachment = forms.IntegerField(required=False)
    student_comment = forms.CharField(max_length=STUDENT_COMMENT_LENGTH, required=False)
    judger_identifier = forms.CharField(max_length=JUDGER_IDENTIFIER_MAX_LENGTH, required=False)
    judge_result = forms.Integer<PERSON>ield(required=False)
    course_code = forms.Char<PERSON><PERSON>(max_length=JUDGER_COURSE_CODE_LENGTH, required=False)
    chapter_code = forms.CharField(max_length=JUDGER_CHAPTER_CODE_LENGTH, required=False)


@nottest
class TestCaseJudgeRecordInfo(forms.Form):
    """
    define test case judge record related validate form
    """
    problem_judge_record = forms.IntegerField()
    test_case = forms.IntegerField()
    judge_result = forms.IntegerField()
    raw_output = forms.CharField(max_length=1048576, required=False)
    comment = forms.CharField(max_length=1024)
