"""
define problem's view-layer functions
"""

from django.core.exceptions import FieldError
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from judge.api.file import generate_oss_token, s3_upload, _validate_upload_file_without_filename
from judge.api.permissions import (JUDGE_CHANGE_PROBLEM, JUDGE_CREATE_PROBLEM,
                                   JUDGE_DELETE_PROBLEM, JUDGE_VIEW_PROBLEM)
from judge.models import AdminUploadedFile, Problem, TestCase, Choice
from judge.models.git_hash import GitHash
from judge.models.problem import PROBLEM_TYPE_SINGLE_CHOICE, PROBLEM_TYPE_MULTIPLE_CHOICE, PROBLEM_TYPE_SUBMIT_FILE
from judge.models.user_submitted_file import FileTypes
from trebuchet.settings import GIT_TOKEN


def _validate_create_problem(request: HttpRequest) -> bool:
    """
    validate create problem
    Args:
        request:

    Returns:

    """
    fields = [field.name for field in Problem._meta.get_fields()]
    data: dict = parse_data(request)
    if data is None:
        return False
    for key in data.keys():
        if key not in fields or key in ['created_by', 'created_at']:
            return False
    problem_data_id: int = data.get('problem_data', None)
    test_cases_id: list = data.get('test_cases', None)
    if problem_data_id is not None:
        if not AdminUploadedFile.objects.filter(id=problem_data_id).exist():
            return False
    if test_cases_id is not None:
        test_cases = TestCase.objects.filter(pk__in=test_cases_id)
        return len(test_cases) == len(test_cases_id)
    return True


def _validate_update_problem(request: HttpRequest) -> bool:
    """
    validate update problem
    Args:
        request:

    Returns:

    """
    data: dict = parse_data(request)
    query_id = data.get('problem_data', None)
    if query_id is not None:
        if not AdminUploadedFile.objects.filter(id=query_id).exists():
            return False
    fields = [field.name for field in Problem._meta.get_fields()]
    for key in data.keys():
        if (key not in fields or key in ['created_by', 'created_at']) and key not in ['add', 'del']:
            return False
    add = list(set(data.get('add', [])))
    delete = list(set(data.get('del', [])))

    if len(add) != len(data.get('add', [])) or len(delete) != len(data.get('del', [])) \
            or set(add) & set(delete):
        return False

    for value in add:
        if not isinstance(value, int) or not TestCase.objects.filter(id=value).exists():
            return False
    for value in delete:
        if not isinstance(value, int) or not TestCase.objects.filter(id=value).exists():
            return False
    return True


@response_wrapper
@jwt_auth(perms=[JUDGE_CREATE_PROBLEM])
@require_POST
@validate_args(func=_validate_create_problem)
def create_problem(request: HttpRequest):
    """
    create problem
    :param request:
    :return:
    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # data: dict = parse_data(request)
    # model_id = data.get('problem_data', None)
    # if model_id is not None:
    #     data['problem_data'] = AdminUploadedFile.objects.get(id=model_id)
    # test_cases = data.get('test_cases', None)
    # if test_cases is not None:
    #     data.pop('test_case')
    # new_problem = Problem.objects.create(**data)
    # if test_cases is not None:
    #     new_problem.test_cases.add(*test_cases)
    # new_problem.created_by = request.user
    # new_problem.save()
    # return success_api_response({'success': True,
    #                              'problem_id': new_problem.pk
    #                              })


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROBLEM])
@require_GET
@require_item_exist(model=Problem, field='id', item='id')
def get_problem(request: HttpRequest, query_id: int):
    """
    get problem
    :param request:
    :param query_id:
    :return:
    """
    problem = Problem.objects.get(**{'id': query_id})
    data: dict = model_to_dict(problem)
    data['created_at'] = problem.created_at
    data['test_cases'] = []
    test_cases = problem.test_cases.all().order_by("id")
    for case in test_cases:
        test_case = model_to_dict(case)
        test_case['judge_data__filename'] = None if case.judge_data is None else case.judge_data.filename
        data['test_cases'].append(test_case)

    data['choices'] = []
    choices = Choice.objects.filter(problem=problem)
    for choice_info in choices:
        choice = model_to_dict(choice_info)
        data['choices'].append(choice)

    data['problem_data__filename'] = None if problem.problem_data is None else problem.problem_data.filename
    data['created_by__username'] = None if problem.created_by is None else problem.created_by.username
    data['interval'] = problem.interval
    data['max_submission'] = problem.max_submission
    data['reset_time'] = problem.reset_time
    data["explain"] = problem.explain
    return success_api_response(data)


@response_wrapper
@require_POST
def git_delete_problem(request: HttpRequest):
    """
    [POST] /api/judge/git/problem/delete
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")
    data: dict = parse_data(request)
    # 如果没找到这个题目，不报错
    if Problem.objects.filter(name=data["name"]).exists():
        Problem.objects.filter(name=data["name"]).delete()
        return success_api_response({"delete": True})
    return success_api_response({"delete": False})


@response_wrapper
@require_POST
def git_update_problem(request: HttpRequest):
    """
    [POST] /api/judge/git/problem/update
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")
    data: dict = parse_data(request)
    # 不进行各种检查，因为发送前已经检查过了
    if data["reset_time"] is None:
        data["reset_time"] = "1970-01-01 08:00:00+08:00"
    if not Problem.objects.filter(name=data["name"]).exists():
        # 不存在，新建
        problem = Problem.objects.create(name=data["name"], description=data["description"], answer=data["answer"],
                                         interval=data["interval"], max_submission=data["max_submission"],
                                         reset_time=data["reset_time"], type=data["type"], explain=data["explain"])
        # 更新选项
        if problem.type in (PROBLEM_TYPE_SINGLE_CHOICE, PROBLEM_TYPE_MULTIPLE_CHOICE):
            for choice in data["choice"]:
                Choice.objects.create(problem=problem, answer=choice["answer"],
                                      description=choice["description"])
    else:
        Problem.objects.filter(name=data["name"]).update(description=data["description"],
                                                         answer=data["answer"], interval=data["interval"],
                                                         max_submission=data["max_submission"],
                                                         reset_time=data["reset_time"], type=data["type"],
                                                         explain=data["explain"])
        problem = Problem.objects.filter(name=data["name"]).first()
        # 更新选项
        if problem.type in (PROBLEM_TYPE_SINGLE_CHOICE, PROBLEM_TYPE_MULTIPLE_CHOICE):
            for choice in data["choice"]:
                if Choice.objects.filter(problem=problem, answer=choice["answer"]).exists():
                    Choice.objects.filter(problem=problem, answer=choice["answer"]).update(
                        description=choice["description"])
                else:
                    Choice.objects.create(problem=problem, answer=choice["answer"],
                                          description=choice["description"])
    return success_api_response({"id": problem.id})


@response_wrapper
@require_POST
@validate_args(func=_validate_upload_file_without_filename)
def git_update_problem_file(request: HttpRequest, problem_name):
    """
    [POST] /api/judge/git/problem/file/<str:problem_name>
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")

    if not Problem.objects.filter(name=problem_name).exists():
        # 不存在先新建
        problem = Problem.objects.create(name=problem_name, type=PROBLEM_TYPE_SUBMIT_FILE)
    else:
        problem = Problem.objects.filter(name=problem_name).first()

    filename = request.FILES.get("file").name
    data = {
        "filename": filename,
        "oss_token": generate_oss_token(problem.id, filename),
        "type": FileTypes.PROBLEM_DATA
    }
    if problem.problem_data is None:
        new_file = AdminUploadedFile.objects.create(**data)
        problem.problem_data = new_file
        problem.save()
    else:
        file_id = problem.problem_data.id
        AdminUploadedFile.objects.filter(pk=file_id).update(**data)
    try:
        s3_upload(data["oss_token"], request, FileTypes.PROBLEM_DATA)
    except Exception as exception:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, str(exception))
    return success_api_response({"id": problem.problem_data_id})


@response_wrapper
@require_GET
def get_latest_hash(request: HttpRequest, branch):
    """
    [GET] /api/judge/git/hash/<str:branch>
    """
    if not GitHash.objects.filter(branch=branch).exists():
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "找不到分支")
    hash_code = GitHash.objects.filter(branch=branch).order_by("-created_at").first().hash
    return success_api_response({"hash": hash_code})


@response_wrapper
@require_POST
def update_hash(request: HttpRequest, branch):
    """
    [POST] /api/judge/git/hash/<str:branch>
    """
    if request.META.get("HTTP_GIT_TOKEN", None) is None or request.META.get("HTTP_GIT_TOKEN") != GIT_TOKEN:
        return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "token error")
    data = parse_data(request)
    git_hash = GitHash.objects.create(hash=data["hash"], branch=branch)
    return success_api_response({"id": git_hash.id})


# pylint: disable=W0613
@response_wrapper
@jwt_auth(perms=[JUDGE_CHANGE_PROBLEM])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_problem)
@require_item_exist(model=Problem, field='id', item='id')
def update_problem(request: HttpRequest, query_id: int):
    """
    update problem
    :param request:
    :param query_id:
    :return:
    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # problem = Problem.objects.get(**{'id': query_id})
    # data: dict = parse_data(request)
    # add = data.get('add', [])
    # delete = data.get('del', [])
    # test_cases = problem.test_cases
    # for value in delete:
    #     case = TestCase.objects.get(id=value)
    #     test_cases.remove(case)
    # for value in add:
    #     case = TestCase.objects.get(id=value)
    #     test_cases.add(case)
    #
    # problem_data = data.get('problem_data', None)
    # if problem_data is not None:
    #     problem_data = AdminUploadedFile.objects.get(id=problem_data)
    #     problem.problem_data = problem_data
    #
    # for value in ['add', 'del', 'problem_data']:
    #     if data.get(value, None) is not None:
    #         del data[value]
    #
    # problem_info = {}
    # for key in data.keys():
    #     if data[key] is None and key not in ['interval', 'max_submission']:
    #         problem_info.update({key: "ignore update due to illegal param!"})
    #         continue
    #     setattr(problem, key, data[key])
    #     problem_info.update({key: getattr(problem, key)})
    # problem.save()
    # return success_api_response(problem_info)


# pylint: disable=W0613
@response_wrapper
@jwt_auth(perms=[JUDGE_DELETE_PROBLEM])
@require_http_methods(['DELETE'])
@require_item_exist(model=Problem, field='id', item='id')
def delete_problem(request: HttpRequest, query_id: int) -> dict:
    """
    delete problem
    :param request:
    :param query_id:
    :return:
    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # model = Problem.objects.get(**{'id': query_id})
    # info = model_to_dict(model)
    # info['created_at'] = model.created_at
    # model.delete()
    # return success_api_response(info)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PROBLEM])
@require_GET
@query_filter(fields=[("name", str), ("description", str), ("id", int), ("type", int)])
@query_distinct(fields=["name", 'description'], model=Problem)
@query_order_by(fields=["name", 'created_at'])
@query_page(default=10)
def list_problems(request: HttpRequest, *args, **kwargs):
    """
    list problems
    :param request:
    :param args:
    :param kwargs:
    :return:
    """
    models_all = Problem.objects.count()
    models: QuerySet = Problem.objects.all()
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    # order by
    order_by = kwargs.get('order_by')
    if order_by is not None:
        models = models.order_by(*order_by)
    else:
        models = models.order_by('-id')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'name', 'description', 'created_by', 'created_by__username', 'created_at',
                'problem_data__filename', 'problem_data__id', 'type', 'answer', 'interval', 'max_submission',
                'reset_time'
            )
        )
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[JUDGE_CHANGE_PROBLEM])
@require_http_methods(["POST"])
def update_choice(request: HttpRequest):
    """
    create problem
    :param request:
    :return:
    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # data: dict = parse_data(request)
    # try:
    #     problem = Problem.objects.get(id=data.get('problem_id', None))
    # except Problem.DoesNotExist:
    #     return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "没有找到该problem")
    #
    # add_set = data.get('add', None)
    #
    # Choice.objects.filter(problem=problem).delete()
    #
    # for add_info in add_set:
    #     Choice.objects.create(answer=add_info["answer"],
    #                           description=add_info["description"],
    #                           problem=problem
    #                           )
    # return success_api_response({'success': True})


PROBLEM_SET_API = wrapped_api({
    "post": create_problem,
    "get": list_problems,
})

PROBLEM_DETAIL_API = wrapped_api({
    "get": get_problem,
    "put": update_problem,
    "delete": delete_problem,
})

CHOICE_ADD_API = wrapped_api({
    "post": update_choice
})

GIT_HASH_API = wrapped_api({
    "get": get_latest_hash,
    "post": update_hash,
})
