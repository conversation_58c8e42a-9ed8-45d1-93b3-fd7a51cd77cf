<template>
  <Card>
    <Row>
      <Select v-model="selectCourse" style="width: 200px; float: left" multiple>
        <Option v-for="item in CourseList" :key="item.id" :value="item.id">{{ item.id }} : {{ item.name }}</Option>
      </Select>
      <Button type="primary" :loading="loading" @click="loadCourseData" style="margin-left: 12px">
        {{ loading ? '请耐心等待加载' : '加载' }}
      </Button>
      <div style="display: inline-block; margin-left: 10px; vertical-align: middle">包括重修生：</div>
      <i-switch v-model="includeRetakers" :disabled="loading">
        <span slot="open">开</span>
        <span slot="close">关</span>
      </i-switch>
      <div style="display: inline-block; margin-left: 10px; vertical-align: middle">
        注：表格中没有数据代表该场考试中，这个学院没有在这个P的考试记录
      </div>
    </Row>
    <Row v-for="course in selectCourse" :key="course" :value="course">
      <div v-if="loaded && examsData[course] !== undefined">
        {{ `${CourseList.find((item) => item.id === course).name} : ` }}
        <PassrateTable
          v-if="loaded"
          :exam-data="examsData[course]"
          :department-cnt="departmentsCnt[course]"
          :course-name="CourseList.find((item) => item.id === course).name"
          :include-retakers="includeRetakers"
        />
      </div>
    </Row>
  </Card>
</template>

<script>
import PassrateTable from './passrate-table'
import { getErrModalOptions } from '@/libs/util'
import { courseIdReq, courseReq } from '@/api/course'
import { examReq, statisticsCombineReq } from '@/api/exam'

export default {
  name: 'ProjectPassrate',
  components: { PassrateTable },
  data() {
    return {
      selectCourse: [],
      CourseList: [],
      loading: false,
      loaded: false,
      examsData: {},
      departmentsCnt: {},
      includeRetakers: true
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      courseReq('get', { page_size: 1000 })
        .then((res) => {
          this.CourseList = res.data.data
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    async loadCourseData() {
      try {
        this.loading = true
        await Promise.all(
          this.selectCourse.map(async (course) => {
            const courseInfo = await courseIdReq('get', course, {})
            try {
              if (this.examsData[course] === undefined) {
                let under_class = courseInfo.data.under_class_exam
                let examsInfo = await examReq('get', { page_size: 1000, course_id: course })
                let examlist = examsInfo.data.exams.filter((item) => item.id !== under_class)
                let combine = await statisticsCombineReq({
                  exam_id_list: '[' + examlist.map((item) => item.id) + ']',
                  course_id: course
                })
                this.examsData[course] = combine.data['exam_statistics']
                examlist.forEach((item) => {
                  this.examsData[course][item.id].date = item.date
                })
                this.departmentsCnt[course] = {
                  normal: combine.data['department_cnt_normal'],
                  retakers: combine.data['department_cnt_retakers']
                }
                this.loaded = false
                this.$nextTick(() => {
                  this.loaded = true
                })
              }
              return new Promise((resolve) => resolve())
            } catch (error) {
              let name = courseInfo.data.name
              throw { err: error, courseName: name }
            }
          })
        )
      } catch (error) {
        let title = error.courseName + ' 出错:' + getErrModalOptions(error.err).title
        let content = getErrModalOptions(error.err).content
        this.$Modal.error({ title, content })
      }
      this.loading = false
    }
  }
}
</script>
