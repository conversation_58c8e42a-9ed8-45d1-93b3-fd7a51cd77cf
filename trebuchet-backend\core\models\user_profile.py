"""
define middle form between user and the course he/she is managing
"""
from django.contrib.auth import get_user_model

from django.db import models

from core.models.course import Course
from core.models.permissions import (USER_PROFILE_CHANGE, USER_PROFILE_CREATE,
                                     USER_PROFILE_DELETE, USER_PROFILE_VIEW)


class UserProfile(models.Model):
    """
    middle form between user and the course he/she is managing
    """
    user = models.OneToOneField(get_user_model(), on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.SET_NULL, null=True)
    profile_photo = models.TextField(null=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (USER_PROFILE_CHANGE, USER_PROFILE_CHANGE),
            (USER_PROFILE_CREATE, USER_PROFILE_CREATE),
            (USER_PROFILE_DELETE, USER_PROFILE_DELETE),
            (USER_PROFILE_VIEW, USER_PROFILE_VIEW)
        ]
