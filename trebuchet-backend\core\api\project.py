"""
define project api
"""
from queue import Queue

from django.core.exceptions import FieldError
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_PROJECT_CHANGE, CORE_PROJECT_CREATE,
                                  CORE_PROJECT_DELETE, CORE_PROJECT_VIEW)
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.utils import (ErrorCode, SetType, failed_api_response,
                            parse_data, require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from core.models.course import Course
from core.models.project import Project
from core.models.student import Student
from core.models.user_profile import UserProfile
from judge.models.problem import Problem


def _validate_update_project(request: HttpRequest) -> bool:
    data: dict = parse_data(request)
    query_id = data.get('parent_project', None)
    if query_id is not None and \
            (not Project.objects.filter(id=query_id).exists() and query_id != SetType.SET_NONE.value):
        return False
    query_id = data.get('course', None)
    if query_id is not None and not Course.objects.filter(id=query_id).exists():
        return False
    fields = [field.name for field in Project._meta.get_fields()]
    extend_args = filter(lambda key: key not in fields and key not in ['add', 'del'], data.keys())
    if list(extend_args):
        return False
    add = set(data.get('add', []))
    delete = set(data.get('del', []))

    if len(add) != len(data.get('add', [])) or len(delete) != len(data.get('del', [])) \
            or add & delete:
        return False
    if list(filter(lambda value: not isinstance(value, int), add | delete)):
        return False
    return data.get('depth', None) is None


def _bfs_update_depth(root: Project):
    queue = Queue()
    queue.put(root)
    while not queue.empty():
        project: Project = queue.get()
        if hasattr(project, 'project_set'):
            children = project.project_set.all()
            for child in children:
                child.depth = project.depth + 1
                child.save()
                queue.put(child)


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_CREATE])
@require_POST
@validate_args(model=Project, exclude=['parent_project'])
def create_project(request: HttpRequest):
    """
    create project
    :param request:
    :return:
    """
    data: dict = parse_data(request)
    if data.get('depth', None) is not None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'Can Not Set Depth')
    parent_project_id = data.get('parent_project', None)
    if parent_project_id is not None:
        project = Project.objects.filter(id=parent_project_id).first()
        if project is None:
            return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'No Such Parent_project!')
        data['parent_project'] = project
        data['depth'] = project.depth + 1
    else:
        data['depth'] = 0
    course_id = data['course']
    course = Course.objects.filter(id=course_id).first()
    if course is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'No Such Course!')
    data.pop("course", None)
    project_temp = Project(**data)
    project_temp.course = course
    project_temp.save()
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_VIEW])
@require_GET
@require_item_exist(model=Project, field='id', item='id')
def get_project(request: HttpRequest, query_id: int):
    """
    get project
    :param request:
    :param query_id:
    :return:
    """
    project = Project.objects.get(id=query_id)
    project = model_to_dict(project)
    project['student_whitelist'] = list(map(
        lambda student: model_to_dict(
            student, fields=['name', 'student_id', 'department']),
        project['student_whitelist']
    ))
    return success_api_response(project)


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_CHANGE])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_project)
@require_item_exist(model=Project, field='id', item='id')
def update_project(request: HttpRequest, query_id: int):
    """
    update project
    :param request:
    :param query_id:
    :return:
    """
    project: Project = Project.objects.get(id=query_id)
    data: dict = parse_data(request)
    add = data.get('add', [])
    delete = data.get('del', [])
    students_whitelist = project.student_whitelist
    for value in delete:
        student = Student.objects.filter(id=value).first()
        if not student:
            return failed_api_response(ErrorCode.ITEM_NOT_FOUND)
        students_whitelist.remove(student)
    for value in add:
        student = Student.objects.filter(id=value).first()
        if not student:
            return failed_api_response(ErrorCode.ITEM_NOT_FOUND)
        students_whitelist.add(student)

    parent_project = data.get('parent_project', SetType.NONE.value)
    need_bfs = False
    if parent_project != SetType.NONE.value:
        need_bfs = True
        if parent_project != SetType.SET_NONE.value:
            parent_project = Project.objects.get(id=parent_project)
            project.parent_project = parent_project
            project.depth = parent_project.depth + 1
        else:
            project.parent_project = None
            project.depth = 0

    course = data.get('course', None)
    if course is not None:
        if course == query_id:
            return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
        course = Course.objects.get(id=course)
        project.course = course
    if data.get('name', None) is not None:
        project.name = data['name']
    if need_bfs:
        _bfs_update_depth(project)
    project.save()
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_DELETE])
@require_http_methods(['DELETE'])
@require_item_exist(model=Project, field='id', item='id')
def delete_project(request: HttpRequest, query_id: int) -> dict:
    """
    delete project
    :param request:
    :param query_id:
    :return:
    """
    model = Project.objects.get(id=query_id)
    for child in model.project_set.all():
        if child == model:
            continue
        child.depth = 0
        child.save()
        _bfs_update_depth(child)
    info = model_to_dict(model)
    info['student_whitelist'] = list(map(
        lambda student: model_to_dict(
            student, fields=['name', 'student_id', 'department']),
        info['student_whitelist']
    ))
    model.delete()
    return success_api_response(info)


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_VIEW])
@require_GET
@query_filter(fields=[("name", str), ("depth", int), ("course__id", int)])
@query_distinct(fields=["name", 'depth', "course__id"], model=Project)
@query_order_by(fields=["name", 'depth'])
@query_page(default=10)
def list_projects(request: HttpRequest, *args, **kwargs):
    """
    list projects
    :param request:
    :param args:
    :param kwargs:
    :return:
    """
    models_all = Project.objects.count()
    user_profile = UserProfile.objects.filter(user=request.user).first()
    models: QuerySet = Project.objects.filter(course=user_profile.course)
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    # order by
    order_by = kwargs.get('order_by')
    try:
        if order_by is not None:
            models = models.order_by(*order_by)
        else:
            models = models.order_by('depth')
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   'Unsupported Order Method')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'name', 'course', 'parent_project', 'depth', 'course__name'
            )
        )
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_CHANGE])
@require_http_methods(["PUT"])
@require_item_exist(model=Project, field="id", item="id")
def update_problems_in_project(request: HttpRequest, query_id: int):
    """update problems set of specified project

    [method]: PUT

    [route]: /api/project/<int:id>/problems

    Args:
        request (HttpRequest): put request
        query_id (int): project id
    """
    data: dict = parse_data(request)
    model = Project.objects.get(pk=query_id)
    problems_add_ids = data.get("add")
    problems_del_ids = data.get("del")
    problems_add = Problem.objects.filter(pk__in=problems_add_ids)
    problems_del = Problem.objects.filter(pk__in=problems_del_ids)
    model.problems.add(*problems_add)
    model.problems.remove(*problems_del)
    model.save()
    return success_api_response({"result": "Ok, problems of the project have been updated."})


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_VIEW])
@require_GET
@require_item_exist(model=Project, field="id", item="id")
def retrieve_problems_in_project(request: HttpRequest, query_id: int):
    """retrieve problems set of specified project

    [method]: GET

    [route]: /api/project/<int:id>/problems

    Args:
        request (HttpRequest): get request
        query_id (int): project id
    """
    model = Project.objects.get(pk=query_id)
    problems = list(model.problems.all().values("id", "name"))
    data = {
        "problems": problems
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_CHANGE])
@require_POST
@require_item_exist(model=Project, field="id", item="id")
def upload_project_whitelist(request: HttpRequest, query_id: int):
    """upload whitelist of a specified project

    [method]: POST

    [route]: /api/project/<int:id>/whitelist
    """
    data: dict = parse_data(request)
    project = Project.objects.get(pk=query_id)
    whitelist = data.get("whitelist")
    if whitelist is None or not isinstance(whitelist, list):
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Invalid whitelist")
    students = Student.objects.filter(student_id__in=whitelist)
    project.student_whitelist.clear()
    project.student_whitelist.add(*students)
    return success_api_response({"result": "Ok, whitelist uploaded."})


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_VIEW])
@require_GET
@require_item_exist(model=Project, field="id", item="id")
def retrieve_project_whitelist(request: HttpRequest, query_id: int):
    """retrieve whitelist of a specified project

    [method]: GET

    [route]: /api/project/<int:id>/whitelist
    """
    project = Project.objects.get(pk=query_id)
    students = project.student_whitelist.all()
    whitelist = list(students.values("id", "student_id", "name"))
    data = {
        "whitelist": whitelist
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PROJECT_CHANGE])
@require_http_methods(["DELETE"])
@require_item_exist(model=Project, field="id", item="id")
def delete_project_whitelist(request: HttpRequest, query_id: int):
    """delete whitelist of a specified project

    [method]: DELETE

    [route]: /api/project/<int:id>/whitelist
    """
    project = Project.objects.get(pk=query_id)
    project.student_whitelist.clear()
    return success_api_response({"result": "Ok, whitelist deleted."})


PROJECT_DETAIL_API = wrapped_api({
    "get": get_project,
    "put": update_project,
    "delete": delete_project
})

PROJECT_SET_API = wrapped_api({
    'get': list_projects,
    'post': create_project
})

PROJECT_WHITELIST_API = wrapped_api({
    "post": upload_project_whitelist,
    "get": retrieve_project_whitelist,
    "delete": delete_project_whitelist
})
