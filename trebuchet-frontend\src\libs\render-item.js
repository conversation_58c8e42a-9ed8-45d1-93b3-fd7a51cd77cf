import { getPercent } from '@/libs/util'

export const WhitePre = (h, text) => {
  return h('div', { style: { whiteSpace: 'pre' } }, text)
}

export const Tag = (h, color, text) => {
  return h('Tag', { props: { color: color } }, text)
}

export const TagByObj = (h, obj) => {
  return h('Tag', { props: { color: obj.color } }, obj.name)
}

export const Link = (h, id, route) => {
  return h('router-link', { props: { to: { name: route, params: { id: id } } } }, id)
}

export const Spacer = (h) => {
  return h('span', { style: { marginLeft: '10px' } }, '')
}

export const ActionButton = (h, callback, text, disable) => {
  return h('Button', { on: { click: callback }, props: { type: 'primary', disabled: disable } }, text)
}

export const LinkButton = (h, id, route, text, disable) => {
  const color = disable ? 'gray' : 'white'
  return h('router-link', { props: { to: { name: route, params: { id: id } } }, style: { color: color } }, [
    h('Button', { props: { type: 'primary', disabled: disable } }, text)
  ])
}

export const LinkDownload = (h, callback, text, disable) => {
  return disable ? h('div', '无') : h('a', { on: { click: callback } }, text)
}

export const TextTooltip = (h, inner, outer) => {
  return h('Tooltip', { props: { placement: 'top', content: inner } }, outer)
}

export const PercentTooltip = (h, value, total) => {
  return TextTooltip(h, value + '/' + total, getPercent(value, total))
}
