# Generated by Django 2.2.6 on 2019-10-07 14:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('judge', '0002_problemjudgerecord_project_in_exam'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0003_auto_20190911_1309'),
    ]

    operations = [
        migrations.AddField(
            model_name='project',
            name='problems',
            field=models.ManyToManyField(to='judge.Problem'),
        ),
        migrations.AddField(
            model_name='projectinexam',
            name='pass_requirement',
            field=models.CharField(max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='projectinexam',
            name='problems',
            field=models.ManyToManyField(to='judge.Problem'),
        ),
        migrations.CreateModel(
            name='StudentProgress',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qualified', models.BooleanField(default=False)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Course')),
                ('current_project',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='current_project',
                                   to='core.ProjectInExam')),
                ('passed_project',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='passed_project',
                                   to='core.ProjectInExam')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Student')),
            ],
        ),
        migrations.CreateModel(
            name='ExamRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('extend_time', models.IntegerField(default=0)),
                ('status', models.IntegerField(choices=[(0, 'NOT_CHECKED_IN'), (1, 'IN_PROGRESS'), (2, 'CHECKED_OUT')],
                                               default=0)),
                ('checked_in_at', models.DateTimeField()),
                ('checked_out_at', models.DateTimeField()),
                ('check_result',
                 models.IntegerField(choices=[(4, 'A+'), (3, 'A'), (2, 'B'), (1, 'C'), (0, 'D'), (-1, 'F')],
                                     default=-1)),
                ('check_comment', models.TextField(default='')),
                ('examinant', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                to=settings.AUTH_USER_MODEL)),
                ('project_in_exam',
                 models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.ProjectInExam')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.Student')),
            ],
        ),
        migrations.CreateModel(
            name='ExamQueue',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pushed_at', models.DateTimeField(auto_now_add=True)),
                ('popped_at', models.DateTimeField(auto_now=True)),
                ('valid', models.BooleanField(default=True)),
                ('exam', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Exam')),
                ('project_in_exam',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.ProjectInExam')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Room')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Student')),
            ],
        ),
        migrations.AddConstraint(
            model_name='examqueue',
            constraint=models.UniqueConstraint(fields=('student', 'room', 'exam'), name='queue_student_index'),
        ),
    ]
