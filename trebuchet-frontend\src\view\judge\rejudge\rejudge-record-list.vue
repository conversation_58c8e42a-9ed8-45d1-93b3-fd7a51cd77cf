<template>
  <div>
    <Row>
      <Col span="12" offset="6">
        <Card>
          <filter-table
            :data="batchIdPage"
            :columns="batchIdColumns"
            size="small"
            :default-filter="
              this.$store.state.app.tableFilter.rejudgeIdTable ? this.$store.state.app.tableFilter.rejudgeIdTable : {}
            "
            @on-search="onSearch"
          />
          <Page
            :total="totalCnt"
            :current="currentBatchIdPage"
            :page-size="batchIdPageSize"
            show-elevator
            show-total
            @on-change="changePage"
          >
            <p>总数{{ totalCnt }}条</p>
          </Page>
        </Card>
      </Col>
    </Row>
    <Row>
      <Col span="12" offset="6">
        <Table :data="rejudgeRecords" :columns="columns" no-data-text="没有结果变化的record" />
      </Col>
    </Row>
  </div>
</template>

<script>
import { ActionButton, Tag } from '@/libs/render-item'
import FilterTable from '@/view/filter-table/filter-table'
import { batchRejudgeReq, batchRejudgeIdReq } from '@/api/judge'

export default {
  name: 'RejudgeRecordList',
  components: { FilterTable },
  data() {
    return {
      rejudgeRecords: [],
      columns: [
        { key: 'origin_recordId', title: '原记录ID' },
        { key: 'user_name', title: '学生' },
        {
          title: '原结果',
          render: (h, params) => (!params.row.origin_result ? Tag(h, 'green', 'PASSED') : Tag(h, 'red', 'FAILED'))
        },
        {
          title: '新结果',
          render: (h, params) => (!params.row.rejudge_result ? Tag(h, 'green', 'PASSED') : Tag(h, 'red', 'FAILED'))
        },
        { key: 'problem_id', title: '题目ID' }
      ],
      originBatchIdList: [],
      batchIdList: [],
      batchIdPage: [],
      batchIdColumns: [
        { key: 'batch_id', title: 'batch_id', filter: { type: 'Input' } },
        {
          title: ' ',
          render: (h, params) => ActionButton(h, () => this.checkRecord(params.row.batch_id), '查看', false)
        }
      ],
      currentBatchIdPage: 1,
      batchIdPageSize: 10
    }
  },
  computed: {
    totalCnt() {
      return this.batchIdList.length
    }
  },
  mounted() {
    this.loadBatchId()
  },
  methods: {
    loadBatchId() {
      batchRejudgeIdReq('get').then((res) => {
        let array = res.data['batch_id_list'].sort((a, b) => b.localeCompare(a)) // 时间倒序
        array.forEach((item) => this.batchIdList.push({ batch_id: item }))
        array.forEach((item) => this.originBatchIdList.push({ batch_id: item }))
        this.batchIdPage = this.batchIdList.slice(0, this.batchIdPageSize - 1)
      })
    },
    showData(data) {
      this.rejudgeRecords = []
      const records = data['records']
      for (const item in records) {
        this.rejudgeRecords.push({
          origin_recordId: records[item]['judge_record_data']['id'],
          user_name: records[item]['judge_record_data']['edx_username'],
          origin_result: records[item]['judge_record_data']['judge_result'],
          rejudge_result: records[item]['rejudge_record_data']['judge_result'],
          problem_id: records[item]['judge_record_data']['problem']
        })
      }
    },
    changePage(index) {
      this.batchIdPage = this.batchIdList.slice((index - 1) * this.batchIdPageSize, index * this.batchIdPageSize - 1)
      this.currentBatchIdPage = index
    },
    checkRecord(batch_id) {
      batchRejudgeReq('get', { batch_id__exact: batch_id }).then((res) => {
        this.finished = true
        this.showData(res.data)
        this.$Notice.success({ title: '加载成功' })
      })
    },
    onSearch(search) {
      let filter = search.batch_id === undefined ? '' : search.batch_id
      this.batchIdList = this.originBatchIdList.filter((item) => item.batch_id.indexOf(filter) !== -1)
      this.changePage(1)
    }
  }
}
</script>
