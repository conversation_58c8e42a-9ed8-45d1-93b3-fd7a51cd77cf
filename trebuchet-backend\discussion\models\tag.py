"""
declare Tag model
"""

from django.db import models

from discussion.models.discussion import Discussion
from core.models.permissions import (TAG_VIEW, TAG_CREATE, TAG_DELETE, TAG_UPDATE)


class Tag(models.Model):
    name = models.TextField(max_length=100, unique=True)
    discussion = models.ManyToManyField(to=Discussion, through="DiscussionTag")
    priority = models.PositiveIntegerField()

    class Meta:
        default_permissions = ()
        permissions = [
            (TAG_VIEW, TAG_VIEW),
            (TAG_CREATE, TAG_CREATE),
            (TAG_DELETE, TAG_DELETE),
            (TAG_UPDATE, TAG_UPDATE)
        ]
