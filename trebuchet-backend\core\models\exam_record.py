"""
declare ExamRecord model
"""

from django.db import models
from django.contrib.auth import get_user_model

from core.models.permissions import (EXAM_RECORD_CHANGE, EXAM_RECORD_CREATE,
                                     EXAM_RECORD_DELETE, EXAM_RECORD_EXPORT,
                                     EXAM_RECORD_OLD_CHANGE, EXAM_RECORD_VIEW, EXAM_RECORD_STATISTICS)
from core.models.project_in_exam import ProjectInExam
from core.models.student import Student

GRADE_A_PLUS = 4
GRADE_A = 3
GRADE_B = 2
GRADE_C = 1
GRADE_D = 0
GRADE_F = -1

STATUS_NOT_CHECKED_IN = 0
STATUS_IN_PROGRESS = 1
STATUS_CHECKED_OUT = 2
STATUS_WAITING_IN_QUEUE = 3


class ExamRecord(models.Model):
    """This model describes the relationships between students and projects.

    When a student participates in an exam-project,
    a record will be created to save static & dynastic information about this project instance.

    Attributes:
        student: A Database ForeignKey to Student.
        project_in_exam: A Database ForeignKey to ProjectInExam.
        extend_time: An IntegerField represents the delay accurate to minute.
        status: An IntegerField represents the student' s status in this project.
                NOT_CHECKED_IN / IN_PROGRESS / CHECKED_OUT
        check_result: A IntegerField indicating the score of a student.
        examinant: A Database ForeignKey to User, representing the examinant.
        check_comment: A TextField represents the comment from examinant.
        question_answer_record: A TextField recording the grade and note of quiz in json.
    """

    # now we only use A, B, C, F
    RESULT_TYPE = [
        (GRADE_A_PLUS, 'A+'),
        (GRADE_A, 'A'),
        (GRADE_B, 'B'),
        (GRADE_C, 'C'),
        (GRADE_D, 'D'),
        (GRADE_F, 'F'),
    ]

    STATUS = [
        (STATUS_NOT_CHECKED_IN, 'NOT_CHECKED_IN'),
        (STATUS_IN_PROGRESS, 'IN_PROGRESS'),
        (STATUS_CHECKED_OUT, 'CHECKED_OUT'),
        (STATUS_WAITING_IN_QUEUE, 'WAITING_IN_QUEUE'),
    ]

    MAX_GRADE = RESULT_TYPE[0][0]
    MIN_GRADE = RESULT_TYPE[-1][0]

    MAX_STATUS = STATUS[-1][0]
    MIN_STATUS = STATUS[0][0]

    student = models.ForeignKey(to=Student, on_delete=models.PROTECT)
    project_in_exam = models.ForeignKey(
        to=ProjectInExam, on_delete=models.PROTECT)

    # how many minutes delayed.
    # can be minus(means early stopping)
    extend_time = models.IntegerField(default=0)

    status = models.IntegerField(choices=STATUS, default=0)
    checked_in_at = models.DateTimeField(null=True)
    checked_out_at = models.DateTimeField(null=True)
    check_result = models.IntegerField(choices=RESULT_TYPE, default=-1)
    examinant = models.ForeignKey(
        to=get_user_model(), on_delete=models.SET_NULL, null=True)
    check_comment = models.TextField(default='')
    question_answer_record = models.TextField(null=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (EXAM_RECORD_CHANGE, EXAM_RECORD_CHANGE),
            (EXAM_RECORD_CREATE, EXAM_RECORD_CREATE),
            (EXAM_RECORD_DELETE, EXAM_RECORD_DELETE),
            (EXAM_RECORD_VIEW, EXAM_RECORD_VIEW),
            (EXAM_RECORD_OLD_CHANGE, EXAM_RECORD_OLD_CHANGE),
            (EXAM_RECORD_EXPORT, EXAM_RECORD_EXPORT),
            (EXAM_RECORD_STATISTICS, EXAM_RECORD_STATISTICS)
        ]
