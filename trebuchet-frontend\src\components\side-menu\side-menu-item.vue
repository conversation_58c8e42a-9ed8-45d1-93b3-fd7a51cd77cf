<template>
  <Submenu :name="`${parentName}`">
    <span slot="title">
      <common-icon :type="parentItem['icon'] || ''" style="margin-bottom: 2px" />
      <span>{{ showTitle(parentItem) }}</span>
    </span>
    <template v-for="item in children">
      <template v-if="item.children && item.children.length === 1">
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item" />
        <menu-item v-else :to="item.fullPath" :key="`menu-${item.children[0].name}`" :name="getNameOrHref(item, true)">
          <common-icon :type="item.children[0].icon || ''" /><span>{{ showTitle(item.children[0]) }}</span>
        </menu-item>
      </template>
      <template v-else>
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item" />
        <menu-item v-else :to="item.fullPath" :key="`menu-${item.name}`" :name="getNameOrHref(item)">
          <common-icon :type="item.icon || ''" /><span>{{ showTitle(item) }}</span>
        </menu-item>
      </template>
    </template>
  </Submenu>
</template>

<script>
import mixin from './mixin'
import itemMixin from './item-mixin'

export default {
  name: 'SideMenuItem',
  mixins: [mixin, itemMixin]
}
</script>
