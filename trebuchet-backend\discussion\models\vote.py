"""
declare Vote model
"""

from django.contrib.auth.models import User
from django.db import models

from discussion.models import Response


class Vote(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=False, db_index=True
    )

    response = models.ForeignKey(
        Response, on_delete=models.CASCADE, null=False, db_index=True
    )

    created_at = models.DateTimeField(blank=False, null=False, auto_now_add=True)

    class Meta:
        unique_together = ("user_id", "response_id")
