import Vue from 'vue'
import iView from 'iview'
import VueRouter from 'vue-router'

import store from '@/store'
import routes from '@/router/routers'
import { getToken } from '@/libs/tools'
import { canTurnTo, setTitle } from '@/libs/util'

const originalPush = VueRouter.prototype.push
const originalReplace = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch((err) => err)
}

VueRouter.prototype.replace = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
  return originalReplace.call(this, location).catch((err) => err)
}

Vue.use(VueRouter)

const router = new VueRouter({
  routes: routes,
  mode: 'hash'
})

router.beforeEach((to, from, next) => {
  iView.LoadingBar.start()
  const token = getToken()
  if (to.name === 'login') {
    if (token === null || token === '') {
      next()
    } else {
      next({ name: 'home' })
    }
  } else {
    if (token === null || token === '') {
      next({ name: 'login', query: { redirect: to.fullPath } })
    } else if (!canTurnTo(to.name, store.state.user.access, routes)) {
      next({ name: 'error_401', replace: true })
    } else {
      next()
    }
  }
})

router.afterEach((to) => {
  setTitle(to)
  iView.LoadingBar.finish()
})

export default router
