{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { examPassDetectionReq } from '@/api/progress';\nimport { examReq } from '@/api/exam';\nimport { getErrModalOptions } from '@/libs/util';\nimport { WhitePre, Tag } from '@/libs/render-item';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ExamProgressDetection',\n  data() {\n    return {\n      selectedExam: null,\n      examList: [],\n      activeTab: 'passed',\n      loading: false,\n      autoRefresh: false,\n      refreshTimer: null,\n      alertThreshold: 30,\n      // 统计数据\n      passedCount: 0,\n      totalCount: 0,\n      examDuration: '00:00:00',\n      examStartTime: '',\n      noPassAlert: false,\n      lastPassTime: null,\n      // 学生数据\n      passedStudents: [],\n      notPassedStudents: [],\n      // 表格列定义\n      passedStudentsColumns: [{\n        title: '学号',\n        key: 'student_id',\n        render: (h, params) => WhitePre(h, params.row.student_id)\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        render: (h, params) => WhitePre(h, params.row.student_name)\n      }, {\n        title: '班级',\n        key: 'class_name'\n      }, {\n        title: '通过时间',\n        key: 'pass_time'\n      }, {\n        title: '用时',\n        key: 'duration',\n        render: (h, params) => Tag(h, 'green', params.row.duration)\n      }, {\n        title: '状态',\n        key: 'status',\n        render: (h, params) => Tag(h, 'success', '已通过')\n      }],\n      notPassedStudentsColumns: [{\n        title: '学号',\n        key: 'student_id',\n        render: (h, params) => WhitePre(h, params.row.student_id)\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        render: (h, params) => WhitePre(h, params.row.student_name)\n      }, {\n        title: '班级',\n        key: 'class_name'\n      }, {\n        title: '当前进度',\n        key: 'current_progress'\n      }, {\n        title: '已用时间',\n        key: 'elapsed_time',\n        render: (h, params) => Tag(h, 'blue', params.row.elapsed_time)\n      }, {\n        title: '状态',\n        key: 'status',\n        render: (h, params) => Tag(h, 'warning', '进行中')\n      }],\n      // 图表数据\n      trendChart: null,\n      trendData: []\n    };\n  },\n  computed: {\n    passRate() {\n      return this.totalCount > 0 ? Math.round(this.passedCount / this.totalCount * 100) : 0;\n    }\n  },\n  mounted() {\n    this.loadExams();\n    this.initTrendChart();\n  },\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n    if (this.trendChart) {\n      this.trendChart.dispose();\n    }\n  },\n  methods: {\n    async loadExams() {\n      try {\n        const res = await examReq('get', {\n          order_by: '-date'\n        });\n        this.examList = res.data.exams || [];\n        if (this.examList.length > 0) {\n          // 优先选择激活的考试\n          const activeExam = this.examList.find(exam => exam.active);\n          this.selectedExam = activeExam ? activeExam.id : this.examList[0].id;\n          this.loadExamData();\n        }\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n      }\n    },\n    onExamChange() {\n      this.loadExamData();\n    },\n    onTabChange(name) {\n      this.activeTab = name;\n      if (name === 'trend') {\n        this.$nextTick(() => {\n          this.updateTrendChart();\n        });\n      }\n    },\n    async loadExamData() {\n      if (!this.selectedExam) return;\n      this.loading = true;\n      try {\n        // 模拟API调用\n        await new Promise(resolve => setTimeout(resolve, 800));\n\n        // 模拟数据\n        this.passedCount = 45;\n        this.totalCount = 120;\n        this.examStartTime = '2024-01-16 14:00:00';\n        this.examDuration = '01:23:45';\n        this.lastPassTime = new Date(Date.now() - 5 * 60 * 1000); // 5分钟前\n\n        this.passedStudents = [{\n          student_id: '2021001',\n          student_name: '张三',\n          class_name: '计科21-1',\n          pass_time: '14:25:30',\n          duration: '25分30秒'\n        }, {\n          student_id: '2021002',\n          student_name: '李四',\n          class_name: '计科21-1',\n          pass_time: '14:32:15',\n          duration: '32分15秒'\n        }, {\n          student_id: '2021003',\n          student_name: '王五',\n          class_name: '计科21-2',\n          pass_time: '14:18:45',\n          duration: '18分45秒'\n        }];\n        this.notPassedStudents = [{\n          student_id: '2021004',\n          student_name: '赵六',\n          class_name: '计科21-2',\n          current_progress: 'P2-第3题',\n          elapsed_time: '1小时23分'\n        }, {\n          student_id: '2021005',\n          student_name: '钱七',\n          class_name: '计科21-3',\n          current_progress: 'P1-第2题',\n          elapsed_time: '45分钟'\n        }];\n        this.checkNoPassAlert();\n        this.updateTrendData();\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n      } finally {\n        this.loading = false;\n      }\n    },\n    refreshData() {\n      this.loadExamData();\n    },\n    toggleAutoRefresh(enabled) {\n      if (enabled) {\n        this.refreshTimer = setInterval(() => {\n          this.loadExamData();\n        }, 30000); // 30秒刷新一次\n        this.$Message.success('已开启自动刷新');\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer);\n          this.refreshTimer = null;\n        }\n        this.$Message.info('已关闭自动刷新');\n      }\n    },\n    updateAlertThreshold() {\n      this.checkNoPassAlert();\n    },\n    checkNoPassAlert() {\n      if (this.lastPassTime) {\n        const timeDiff = (Date.now() - this.lastPassTime.getTime()) / (1000 * 60); // 分钟\n        this.noPassAlert = timeDiff > this.alertThreshold;\n      }\n    },\n    initTrendChart() {\n      this.$nextTick(() => {\n        if (this.$refs.trendChart) {\n          this.trendChart = echarts.init(this.$refs.trendChart);\n          this.updateTrendChart();\n        }\n      });\n    },\n    updateTrendChart() {\n      if (!this.trendChart) return;\n      const option = {\n        title: {\n          text: '考试通过人数趋势'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: this.trendData.map(item => item.time)\n        },\n        yAxis: {\n          type: 'value',\n          name: '通过人数'\n        },\n        series: [{\n          data: this.trendData.map(item => item.count),\n          type: 'line',\n          smooth: true,\n          areaStyle: {\n            opacity: 0.3\n          }\n        }]\n      };\n      this.trendChart.setOption(option);\n    },\n    updateTrendData() {\n      // 模拟趋势数据\n      const now = new Date();\n      this.trendData = [];\n      for (let i = 0; i < 10; i++) {\n        const time = new Date(now.getTime() - (9 - i) * 10 * 60 * 1000);\n        this.trendData.push({\n          time: time.toLocaleTimeString().slice(0, 5),\n          count: Math.floor(Math.random() * 20) + i * 5\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAoIA;AACA;AACA;AACA;AACA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC,wBACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MAEAC,2BACA;QACAH;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MAEA;MACAE;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;MACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;MACA;QACA;UAAAC;QAAA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IAEA;MACA;MAEA;MACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;;QAEA,uBACA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA,GACA;UACAJ;UACAC;UACAC;UACAC;UACAC;QACA,GACA;UACAJ;UACAC;UACAC;UACAC;UACAC;QACA,EACA;QAEA,0BACA;UACAJ;UACAC;UACAC;UACAG;UACAC;QACA,GACA;UACAN;UACAC;UACAC;UACAG;UACAC;QACA,EACA;QAEA;QACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACA;UACA;QACA;QACA;MACA;QACA;UACAb;UACA;QACA;QACA;MACA;IACA;IAEAc;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MAEA;QACA3B;UACA4B;QACA;QACAC;UACAC;QACA;QACAC;UACAC;UACAjD;QACA;QACAkD;UACAD;UACAlD;QACA;QACAoD;UACAnD;UACAiD;UACAG;UACAC;YACAC;UACA;QACA;MACA;MAEA;IACA;IAEAC;MACA;MACA;MACA;MACA;QACA;QACA;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "selectedExam", "examList", "activeTab", "loading", "autoRefresh", "refreshTimer", "alertThreshold", "passedCount", "totalCount", "examDuration", "examStartTime", "noPass<PERSON><PERSON><PERSON>", "lastPassTime", "passedStudents", "notPassedStudents", "passedStudentsColumns", "title", "key", "render", "notPassedStudentsColumns", "trendChart", "trendData", "computed", "passRate", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "order_by", "onExamChange", "onTabChange", "student_id", "student_name", "class_name", "pass_time", "duration", "current_progress", "elapsed_time", "refreshData", "toggleAutoRefresh", "updateAlertThreshold", "checkNoPassAlert", "initTrendChart", "updateTrendChart", "text", "tooltip", "trigger", "xAxis", "type", "yAxis", "series", "smooth", "areaStyle", "opacity", "updateTrendData", "time", "count"], "sourceRoot": "src/view/on-exam", "sources": ["exam-progress-detection.vue"], "sourcesContent": ["<template>\n  <div>\n    <Card>\n      <h2 slot=\"title\">考试通过人数检测</h2>\n      <div slot=\"extra\">\n        <Select v-model=\"selectedExam\" style=\"width: 250px\" @on-change=\"onExamChange\">\n          <Option v-for=\"exam in examList\" :key=\"exam.id\" :value=\"exam.id\">\n            {{ exam.id }} : {{ exam.date }} - {{ exam.name }}\n          </Option>\n        </Select>\n      </div>\n\n      <Row :gutter=\"16\" style=\"margin-bottom: 16px\">\n        <Col span=\"8\">\n          <Card>\n            <Statistic title=\"当前通过人数\" :value=\"passedCount\" :value-style=\"{ color: '#3f8600' }\">\n              <template slot=\"suffix\">\n                <Icon type=\"ios-people\" />\n              </template>\n            </Statistic>\n          </Card>\n        </Col>\n        <Col span=\"8\">\n          <Card>\n            <Statistic title=\"总参考人数\" :value=\"totalCount\" :value-style=\"{ color: '#1890ff' }\">\n              <template slot=\"suffix\">\n                <Icon type=\"ios-person\" />\n              </template>\n            </Statistic>\n          </Card>\n        </Col>\n        <Col span=\"8\">\n          <Card>\n            <Statistic title=\"通过率\" :value=\"passRate\" suffix=\"%\" :value-style=\"{ color: passRate > 50 ? '#3f8600' : '#cf1322' }\">\n              <template slot=\"suffix\">\n                <Icon type=\"ios-trending-up\" />\n              </template>\n            </Statistic>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row :gutter=\"16\" style=\"margin-bottom: 16px\">\n        <Col span=\"12\">\n          <Card>\n            <h3 slot=\"title\">考试持续时间</h3>\n            <div style=\"font-size: 24px; color: #1890ff;\">\n              {{ examDuration }}\n            </div>\n            <div style=\"color: #666; margin-top: 8px;\">\n              开始时间: {{ examStartTime }}\n            </div>\n          </Card>\n        </Col>\n        <Col span=\"12\">\n          <Card>\n            <h3 slot=\"title\">无人通过警报</h3>\n            <div v-if=\"noPassAlert\" style=\"color: #cf1322; font-size: 18px;\">\n              <Icon type=\"ios-warning\" style=\"margin-right: 8px;\" />\n              已超过 {{ alertThreshold }} 分钟无人通过！\n            </div>\n            <div v-else style=\"color: #3f8600; font-size: 18px;\">\n              <Icon type=\"ios-checkmark-circle\" style=\"margin-right: 8px;\" />\n              考试进行正常\n            </div>\n            <div style=\"margin-top: 12px;\">\n              <span>警报阈值: </span>\n              <InputNumber \n                v-model=\"alertThreshold\" \n                :min=\"5\" \n                :max=\"60\" \n                size=\"small\" \n                style=\"width: 80px;\"\n                @on-change=\"updateAlertThreshold\"\n              />\n              <span> 分钟</span>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div slot=\"title\">\n          <span>实时通过情况</span>\n          <Button \n            type=\"primary\" \n            size=\"small\" \n            @click=\"refreshData\" \n            :loading=\"loading\"\n            style=\"margin-left: 16px;\"\n          >\n            刷新数据\n          </Button>\n          <Switch \n            v-model=\"autoRefresh\" \n            @on-change=\"toggleAutoRefresh\"\n            style=\"margin-left: 16px;\"\n          >\n            <span slot=\"open\">自动</span>\n            <span slot=\"close\">手动</span>\n          </Switch>\n        </div>\n\n        <Tabs :value=\"activeTab\" @on-click=\"onTabChange\">\n          <TabPane label=\"通过学生列表\" name=\"passed\">\n            <Table \n              :data=\"passedStudents\" \n              :columns=\"passedStudentsColumns\" \n              :loading=\"loading\"\n              size=\"small\"\n            />\n          </TabPane>\n          \n          <TabPane label=\"未通过学生列表\" name=\"not-passed\">\n            <Table \n              :data=\"notPassedStudents\" \n              :columns=\"notPassedStudentsColumns\" \n              :loading=\"loading\"\n              size=\"small\"\n            />\n          </TabPane>\n          \n          <TabPane label=\"通过趋势图\" name=\"trend\">\n            <div ref=\"trendChart\" style=\"height: 400px;\"></div>\n          </TabPane>\n        </Tabs>\n      </Card>\n    </Card>\n  </div>\n</template>\n\n<script>\nimport { examPassDetectionReq } from '@/api/progress'\nimport { examReq } from '@/api/exam'\nimport { getErrModalOptions } from '@/libs/util'\nimport { WhitePre, Tag } from '@/libs/render-item'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ExamProgressDetection',\n  data() {\n    return {\n      selectedExam: null,\n      examList: [],\n      activeTab: 'passed',\n      loading: false,\n      autoRefresh: false,\n      refreshTimer: null,\n      alertThreshold: 30,\n      \n      // 统计数据\n      passedCount: 0,\n      totalCount: 0,\n      examDuration: '00:00:00',\n      examStartTime: '',\n      noPassAlert: false,\n      lastPassTime: null,\n      \n      // 学生数据\n      passedStudents: [],\n      notPassedStudents: [],\n      \n      // 表格列定义\n      passedStudentsColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          render: (h, params) => WhitePre(h, params.row.student_id)\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          render: (h, params) => WhitePre(h, params.row.student_name)\n        },\n        {\n          title: '班级',\n          key: 'class_name'\n        },\n        {\n          title: '通过时间',\n          key: 'pass_time'\n        },\n        {\n          title: '用时',\n          key: 'duration',\n          render: (h, params) => Tag(h, 'green', params.row.duration)\n        },\n        {\n          title: '状态',\n          key: 'status',\n          render: (h, params) => Tag(h, 'success', '已通过')\n        }\n      ],\n      \n      notPassedStudentsColumns: [\n        {\n          title: '学号',\n          key: 'student_id',\n          render: (h, params) => WhitePre(h, params.row.student_id)\n        },\n        {\n          title: '姓名',\n          key: 'student_name',\n          render: (h, params) => WhitePre(h, params.row.student_name)\n        },\n        {\n          title: '班级',\n          key: 'class_name'\n        },\n        {\n          title: '当前进度',\n          key: 'current_progress'\n        },\n        {\n          title: '已用时间',\n          key: 'elapsed_time',\n          render: (h, params) => Tag(h, 'blue', params.row.elapsed_time)\n        },\n        {\n          title: '状态',\n          key: 'status',\n          render: (h, params) => Tag(h, 'warning', '进行中')\n        }\n      ],\n      \n      // 图表数据\n      trendChart: null,\n      trendData: []\n    }\n  },\n  computed: {\n    passRate() {\n      return this.totalCount > 0 ? Math.round((this.passedCount / this.totalCount) * 100) : 0\n    }\n  },\n  mounted() {\n    this.loadExams()\n    this.initTrendChart()\n  },\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer)\n    }\n    if (this.trendChart) {\n      this.trendChart.dispose()\n    }\n  },\n  methods: {\n    async loadExams() {\n      try {\n        const res = await examReq('get', { order_by: '-date' })\n        this.examList = res.data.exams || []\n        if (this.examList.length > 0) {\n          // 优先选择激活的考试\n          const activeExam = this.examList.find(exam => exam.active)\n          this.selectedExam = activeExam ? activeExam.id : this.examList[0].id\n          this.loadExamData()\n        }\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error))\n      }\n    },\n    \n    onExamChange() {\n      this.loadExamData()\n    },\n    \n    onTabChange(name) {\n      this.activeTab = name\n      if (name === 'trend') {\n        this.$nextTick(() => {\n          this.updateTrendChart()\n        })\n      }\n    },\n    \n    async loadExamData() {\n      if (!this.selectedExam) return\n      \n      this.loading = true\n      try {\n        // 模拟API调用\n        await new Promise(resolve => setTimeout(resolve, 800))\n        \n        // 模拟数据\n        this.passedCount = 45\n        this.totalCount = 120\n        this.examStartTime = '2024-01-16 14:00:00'\n        this.examDuration = '01:23:45'\n        this.lastPassTime = new Date(Date.now() - 5 * 60 * 1000) // 5分钟前\n        \n        this.passedStudents = [\n          {\n            student_id: '2021001',\n            student_name: '张三',\n            class_name: '计科21-1',\n            pass_time: '14:25:30',\n            duration: '25分30秒'\n          },\n          {\n            student_id: '2021002',\n            student_name: '李四',\n            class_name: '计科21-1',\n            pass_time: '14:32:15',\n            duration: '32分15秒'\n          },\n          {\n            student_id: '2021003',\n            student_name: '王五',\n            class_name: '计科21-2',\n            pass_time: '14:18:45',\n            duration: '18分45秒'\n          }\n        ]\n        \n        this.notPassedStudents = [\n          {\n            student_id: '2021004',\n            student_name: '赵六',\n            class_name: '计科21-2',\n            current_progress: 'P2-第3题',\n            elapsed_time: '1小时23分'\n          },\n          {\n            student_id: '2021005',\n            student_name: '钱七',\n            class_name: '计科21-3',\n            current_progress: 'P1-第2题',\n            elapsed_time: '45分钟'\n          }\n        ]\n        \n        this.checkNoPassAlert()\n        this.updateTrendData()\n        \n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error))\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    refreshData() {\n      this.loadExamData()\n    },\n    \n    toggleAutoRefresh(enabled) {\n      if (enabled) {\n        this.refreshTimer = setInterval(() => {\n          this.loadExamData()\n        }, 30000) // 30秒刷新一次\n        this.$Message.success('已开启自动刷新')\n      } else {\n        if (this.refreshTimer) {\n          clearInterval(this.refreshTimer)\n          this.refreshTimer = null\n        }\n        this.$Message.info('已关闭自动刷新')\n      }\n    },\n    \n    updateAlertThreshold() {\n      this.checkNoPassAlert()\n    },\n    \n    checkNoPassAlert() {\n      if (this.lastPassTime) {\n        const timeDiff = (Date.now() - this.lastPassTime.getTime()) / (1000 * 60) // 分钟\n        this.noPassAlert = timeDiff > this.alertThreshold\n      }\n    },\n    \n    initTrendChart() {\n      this.$nextTick(() => {\n        if (this.$refs.trendChart) {\n          this.trendChart = echarts.init(this.$refs.trendChart)\n          this.updateTrendChart()\n        }\n      })\n    },\n    \n    updateTrendChart() {\n      if (!this.trendChart) return\n      \n      const option = {\n        title: {\n          text: '考试通过人数趋势'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: this.trendData.map(item => item.time)\n        },\n        yAxis: {\n          type: 'value',\n          name: '通过人数'\n        },\n        series: [{\n          data: this.trendData.map(item => item.count),\n          type: 'line',\n          smooth: true,\n          areaStyle: {\n            opacity: 0.3\n          }\n        }]\n      }\n      \n      this.trendChart.setOption(option)\n    },\n    \n    updateTrendData() {\n      // 模拟趋势数据\n      const now = new Date()\n      this.trendData = []\n      for (let i = 0; i < 10; i++) {\n        const time = new Date(now.getTime() - (9 - i) * 10 * 60 * 1000)\n        this.trendData.push({\n          time: time.toLocaleTimeString().slice(0, 5),\n          count: Math.floor(Math.random() * 20) + i * 5\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.ivu-statistic {\n  text-align: center;\n}\n\n.ivu-card {\n  margin-bottom: 16px;\n}\n</style>\n"]}, "metadata": {}, "sourceType": "module"}