"""declare permissions
"""

from judge.models.permissions import (CHAN<PERSON>_PJR, <PERSON>AN<PERSON>_PROBLEM, CHANGE_TCJR,
                                      CHANGE_TESTCASE, CREATE_PJR,
                                      CREATE_PROBLEM, CREATE_TCJR,
                                      CREATE_TESTCASE, <PERSON>LETE_PJR,
                                      DELE<PERSON>_PROBLEM, <PERSON>LETE_TCJR,
                                      DELETE_TESTCASE, DOWNLOAD_PJR_ATTACHMENT,
                                      DOWNLOAD_PROBLEM_ATTACHMENT,
                                      DOWNLOAD_TESTCASE_ATTACHMENT, REJUD<PERSON>,
                                      UPLOAD_PROBLEM_ATTACHMENT,
                                      UPLOAD_TESTCASE_ATTACHMENT,
                                      VIEW_EXAM_PASS, VIEW_PASS_RANK, VIEW_PJR,
                                      VIEW_PROBLEM, VIEW_PROBLEM_PASS,
                                      VIEW_PROJECT_PASS, VIEW_TCJR,
                                      VIEW_TESTCASE, VIEW_TESTCASE_PASS)

# Problem

JUDGE_DOWNLOAD_PROBLEM_ATTACHMENT = "judge." + DOWNLOAD_PROBLEM_ATTACHMENT
JUDGE_UPLOAD_PROBLEM_ATTACHMENT = "judge." + UPLOAD_PROBLEM_ATTACHMENT
JUDGE_VIEW_PROBLEM_PASS = "judge." + VIEW_PROBLEM_PASS
JUDGE_CREATE_PROBLEM = "judge." + CREATE_PROBLEM
JUDGE_VIEW_PROBLEM = "judge." + VIEW_PROBLEM
JUDGE_CHANGE_PROBLEM = "judge." + CHANGE_PROBLEM
JUDGE_DELETE_PROBLEM = "judge." + DELETE_PROBLEM

# TestCase

JUDGE_VIEW_TESTCASE = "judge." + VIEW_TESTCASE
JUDGE_CREATE_TESTCASE = "judge." + CREATE_TESTCASE
JUDGE_DELETE_TESTCASE = "judge." + DELETE_TESTCASE
JUDGE_CHANGE_TESTCASE = "judge." + CHANGE_TESTCASE
JUDGE_UPLOAD_TESTCASE_ATTACHMENT = "judge." + UPLOAD_TESTCASE_ATTACHMENT
JUDGE_DOWNLOAD_TESTCASE_ATTACHMENT = "judge." + DOWNLOAD_TESTCASE_ATTACHMENT
JUDGE_VIEW_TESTCASE_PASS = "judge." + VIEW_TESTCASE_PASS

# ProblemJudgeRecord

JUDGE_DOWNLOAD_PJR_ATTACHMENT = "judge." + DOWNLOAD_PJR_ATTACHMENT
JUDGE_VIEW_PJR = "judge." + VIEW_PJR
JUDGE_CREATE_PJR = "judge." + CREATE_PJR
JUDGE_CHANGE_PJR = "judge." + CHANGE_PJR
JUDGE_DELETE_PJR = "judge." + DELETE_PJR
JUDGE_REJUDGE = "judge." + REJUDGE
JUDGE_VIEW_EXAM_PASS = "judge." + VIEW_EXAM_PASS
JUDGE_VIEW_PROJECT_PASS = "judge." + VIEW_PROJECT_PASS
JUDGE_VIEW_PASS_RANK = "judge." + VIEW_PASS_RANK
JUDGE_VIEW_PROJECT_SUBMIT = "judge." + VIEW_PJR

# TestCaseJudgeRecord

JUDGE_CREATE_TCJR = "judge." + CREATE_TCJR
JUDGE_VIEW_TCJR = "judge." + VIEW_TCJR
JUDGE_CHANGE_TCJR = "judge." + CHANGE_TCJR
JUDGE_DELETE_TCJR = "judge." + DELETE_TCJR
