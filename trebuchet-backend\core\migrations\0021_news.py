# Generated by Django 3.1.7 on 2021-05-12 07:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0020_projectinexam_description'),
    ]

    operations = [
        migrations.CreateModel(
            name='News',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('active', models.BooleanField(default=True)),
                ('content', models.TextField(null=True)),
                ('star', models.BooleanField(default=False)),
                ('created_by',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('pie',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.projectinexam', null=True)),
                ('exam', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.exam'))
            ],
            options={
                'permissions': [('修改通知', '修改通知'), ('创建通知', '创建通知'), ('删除通知', '删除通知'), ('查看通知', '查看通知')],
                'default_permissions': (),
            },
        ),
    ]
