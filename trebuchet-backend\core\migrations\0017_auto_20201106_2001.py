# Generated by Django 2.2.6 on 2020-11-06 12:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0016_auto_20201028_1535'),
    ]

    operations = [
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=150)),
                ('content', models.TextField()),
                ('hint', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('enabled', models.BooleanField()),
            ],
            options={
                'permissions': [('查看课上问题', '查看课上问题'), ('编辑课上问题', '编辑课上问题')],
                'default_permissions': '查看课上问题',
            },
        ),
        migrations.CreateModel(
            name='QuestionAnswerRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grade', models.IntegerField(default=50)),
                ('comment', models.TextField()),
            ],
        ),
        migrations.AddConstraint(
            model_name='studentprogress',
            constraint=models.UniqueConstraint(fields=('course', 'student'),
                                               name='no duplicate student progress in a course'),
        ),
        migrations.AddField(
            model_name='questionanswerrecord',
            name='question',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Question'),
        ),
        migrations.AddField(
            model_name='questionanswerrecord',
            name='student_record',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.ExamRecord'),
        ),
        migrations.AddField(
            model_name='question',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.Project'),
        ),
    ]
