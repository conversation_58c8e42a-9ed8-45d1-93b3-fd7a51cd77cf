"""
    course management api
"""
from datetime import timezone, datetime

import pandas
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import QuerySet, Subquery, OuterRef, Count, Max
from django.http import HttpResponse
from django.http.request import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)
from django_redis import get_redis_connection

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_COURSE_CHANGE, CORE_COURSE_CREATE,
                                  CORE_COURSE_DELETE, CORE_COURSE_VIEW, CORE_COURSE_EXPORT_FINAL_RESULTS)
from core.api.utils import (parse_data, require_item_exist, require_item_miss,
                            response_wrapper, success_api_response,
                            validate_args, wrapped_api, failed_api_response, require_course_permission)
from core.forms.course import CourseInfo
from core.models import ProjectInExam
from core.models.announcement import Announcement
from core.models.course import Course, DEFAULT_TUTORIAL_SITE
from core.models.exam import Exam
from core.models.exam_record import ExamRecord, GRADE_F
from core.models.instructor_class import InstructorClass
from core.models.project import Project
from core.models.student import Student
from judge.api.file import s3_download_url
from judge.constants import PASSED
from judge.models import ProblemJudgeRecord

ALLOWED_FIELD = {"name", "code", "tutorial_site", "opening"}


def validate_course_request(request: HttpRequest) -> bool:
    """validate class post/put request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"name", "code", "active", "date", "under_class_exam", "tutorial_site", "opening",
                      "tutorial_opening"}
    if len((data.keys() - allowed_fields)) != 0:
        return False
    # check exam
    under_class_exam_id = data.get('under_class_exam', None)
    if under_class_exam_id is not None:
        try:
            Exam.objects.get(pk=under_class_exam_id)
        except ObjectDoesNotExist:
            return False
    # check form
    info = CourseInfo(data)
    if not info.is_valid():
        return False
    return True


def get_tutorial_opening(course):
    redis = get_redis_connection("default")
    value = redis.get(f"tutorial_url${course.tutorial_site}")
    if value is None:
        return True
    value = value.decode('utf-8')
    if value == "false":
        return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_VIEW])
@require_GET
@require_item_exist(model=Course, field='id', item='course_id')
def get_course(request, course_id):
    """get course by id

    [method]: GET

    [route]: /api/courses/<int:course_id>
    """
    course = Course.objects.get(pk=course_id)
    data = {
        'id': course.id,
        'code': course.code,
        'name': course.name,
        'under_class_exam': -1 if course.under_class_exam is None else course.under_class_exam.id,
        'tutorial_site': course.tutorial_site,
        'opening': course.opening,
        "tutorial_opening": get_tutorial_opening(course),
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_http_methods(['PUT'])
@validate_args(func=validate_course_request)
@require_item_exist(model=Course, field='id', item='course_id')
def put_course(request, course_id):
    """update course['id']

    [method]: PUT

    [route]: /api/courses/<int:course_id>
    """
    body = parse_data(request)
    course = Course.objects.get(pk=course_id)
    for key in body.keys() & ALLOWED_FIELD:
        setattr(course, key, body.get(key))
    course.save()
    under_class_exam_id = body.get('under_class_exam', -1)
    if under_class_exam_id != -1:
        if under_class_exam_id is not None:
            exam = Exam.objects.get(pk=under_class_exam_id)
            exam.course = course
            exam.save()
            course.under_class_exam = exam
            course.save()
        else:
            course.under_class_exam = None
            course.save()
    # 教程开关
    tutorial_opening = body.get("tutorial_opening", None)
    if tutorial_opening is not None:
        redis = get_redis_connection("default")
        redis_key = f"tutorial_url${course.tutorial_site}"
        if tutorial_opening:
            redis.set(redis_key, "true")
        else:
            redis.set(redis_key, "false")
    return success_api_response({'result': 'OK, course updated'})


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_DELETE])
@require_http_methods(['DELETE'])
@require_item_exist(model=Course, field='id', item='course_id')
def delete_course(request, course_id):
    """delete course by id

    [method]: DELETE

    [route]: /api/courses/<int:course_id>
    """
    course = Course.objects.get(pk=course_id)
    course.delete()
    return success_api_response({'result': 'OK, course deleted'})


COURSE_DETAIL_API = wrapped_api({
    "get": get_course,
    "put": put_course,
    "delete": delete_course
})


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_VIEW])
@require_GET
def get_course_list(request):
    """get all courses

    [method]: GET

    [route]: /api/courses
    """
    data = {
        'data': list(
            Course.objects.all().order_by("-id").values('id', 'code', 'name', 'under_class_exam', 'tutorial_site',
                                                        'opening'))
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CREATE])
@require_POST
@validate_args(func=validate_course_request)
@require_item_miss(model=Course, field='code')
def create_course(request):
    """create a course

    [method]: POST

    [route]: /api/courses
    """
    body: dict = parse_data(request)
    course_body = {
        "code": body.get("code"),
        "name": body.get("name"),
        "tutorial_site": body.get("tutorial_site", DEFAULT_TUTORIAL_SITE),
        "opening": body.get("opening", False)
    }
    new_course = Course.objects.create(**course_body)

    under_class_exam_id = body.get('under_class_exam', None)
    if under_class_exam_id is None:
        exam_body = {
            "active": False,
            "course": new_course,
            "date": body.get("date")
        }
        exam: Exam = Exam.objects.create(**exam_body)
    else:
        exam = Exam.objects.get(pk=under_class_exam_id)
    new_course.under_class_exam = exam
    new_course.save()
    return success_api_response({'course_id': new_course.id, 'exam_id': exam.id})


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_http_methods(['PUT'])
def add_retake_student(request, course_id):
    """create a course

    [method]: POST

    [route]: /api/courses/<int:course_id>/retake_student
    """
    target_course = Course.objects.get(pk=course_id)
    body: dict = parse_data(request)
    student = body['student']
    if not isinstance(student, list):
        return failed_api_response(400, "student 参数必须为数组。")
    students = Student.objects.filter(student_id__in=student)
    target_course.retake_students.add(*students)
    return success_api_response({'result': 'OK, added'})


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_http_methods(['DELETE'])
def remove_retake_student(request, course_id):
    """remove retake students

    [method]: POST

    [route]: /api/courses/<int:course_id>/retake_student
    """
    target_course = Course.objects.get(pk=course_id)
    body: dict = parse_data(request)
    if 'all' in body and body['all']:
        target_course.retake_students.clear()
        return success_api_response({'result': 'OK, cleared'})
    student = body['student']
    if not isinstance(student, list):
        return failed_api_response(400, "student 参数必须为数组。")
    student = Student.objects.filter(student_id__in=student)
    target_course.retake_students.remove(*student)
    return success_api_response({'result': 'OK, removed'})


@response_wrapper
@require_GET
@jwt_auth(perms=[CORE_COURSE_EXPORT_FINAL_RESULTS])
def course_export_final_results(request: HttpRequest, course_id: int):
    course = Course.objects.get(pk=course_id)
    students: QuerySet = Student.objects.all()
    students = students \
        .filter(instructorclass__belong_to=course.id) \
        .distinct()
    student_list = []
    projects = list(Project.objects.filter(course=course).order_by('id'))
    retakers = list(course.retake_students.values_list("id", flat=True))
    pie_problem_count = dict(ProjectInExam.objects.filter(project__course=course)
                             .annotate(problem_count=Count('problems'))
                             .values_list("id", "problem_count"))

    def get_check_result_display(record):
        return ExamRecord(check_result=record["check_result"]).get_check_result_display()

    def get_project_name(proj):
        return next(filter(lambda p: p.id == proj, projects)).name

    for stu in students:
        vals = list(ExamRecord.objects \
                    .filter(student=stu, project_in_exam__project__course=course) \
                    .annotate(
            passed_problem_count=Subquery(ProblemJudgeRecord.objects.filter(
                edx_username=stu.student_id,
                project_in_exam__id=OuterRef('project_in_exam_id'),
                judge_result=PASSED
            ).order_by('problem', '-id')
                                          .values('project_in_exam')
                                          # Coalesce: if none is found, return 0
                                          .annotate(cnt=Count('problem_id', distinct=True))
                                          .values('cnt'))) \
                    .values('id', 'passed_problem_count',
                            'check_result', 'project_in_exam_id',
                            'project_in_exam__project_id', 'check_comment'))
        for val in vals:
            if val["passed_problem_count"] is None:
                val["passed_problem_count"] = 0

        final_exam_record = max(filter(lambda v: v["check_result"] > GRADE_F, vals), key=lambda p: p["id"],
                                default=None)
        if final_exam_record is None:
            final_project_name = None
            final_check_result = None
            final_problem_count = None
            final_passed_problem_count = None
        else:
            final_project_name = get_project_name(final_exam_record["project_in_exam__project_id"])
            final_check_result = get_check_result_display(final_exam_record)
            final_problem_count = str(pie_problem_count[final_exam_record["project_in_exam_id"]])
            final_passed_problem_count = str(final_exam_record["passed_problem_count"])
        last_submission_time = \
            ProblemJudgeRecord.objects.filter(edx_username=stu.student_id,
                                              project_in_exam__project__course_id=course_id) \
                .aggregate(Max('submitted_at'))["submitted_at__max"]
        if last_submission_time is not None:
            last_submission_time = last_submission_time.replace(tzinfo=timezone.utc).astimezone(tz=None)

        instructor_class = InstructorClass.objects.filter(belong_to=course, student=stu).get()
        is_retaker = stu.id in retakers
        result = {"学号": stu.student_id, "姓名": stu.name, "教学班级": instructor_class.name,
                  "最终通过项目": final_project_name,
                  "最终通过项目的总题数": final_problem_count,
                  "最终通过项目的 AC 题数": final_passed_problem_count,
                  "最终问答评级": final_check_result,
                  "最后一次提交时间": last_submission_time,
                  "课程期间是否有提交": "是" if last_submission_time is not None else "否",
                  "是否为重修生": "是" if is_retaker else "否"}
        for project in projects:
            # pylint: disable=cell-var-from-loop
            project_records = list(filter(lambda v: v["project_in_exam__project_id"] == project.id,
                                          vals))
            if project_records:  # has record
                max_passed_record = max(project_records, key=lambda p: p["passed_problem_count"])
                max_passed_count = max_passed_record["passed_problem_count"]
                max_passed_total = pie_problem_count[max_passed_record["project_in_exam_id"]]
                project_final_record = \
                    max(filter(lambda v: v["check_result"] > GRADE_F, project_records),
                        key=lambda p: p["id"],
                        default=None)
                if project_final_record is not None:
                    project_check_result = get_check_result_display(project_final_record)
                else:
                    project_check_result = "未通过"
                project_exam_count = len(project_records)
            else:
                max_passed_count = None
                max_passed_total = None
                project_check_result = None
                project_exam_count = 0
            result["{} 问答成绩".format(project.name)] = project_check_result
            result["{} 考试次数".format(project.name)] = project_exam_count
            result["{} 课上通过题数".format(project.name)] = max_passed_count
            result["{} 课上总题数".format(project.name)] = max_passed_total

        student_list.append(result)

    dat_string = pandas.DataFrame(student_list).to_csv(index=False, na_rep='')

    rep = HttpResponse(dat_string, content_type='text/csv')
    return rep


@response_wrapper
@require_GET
@jwt_auth(perms=[CORE_COURSE_EXPORT_FINAL_RESULTS])
def course_archive(request: HttpRequest, course_id: int):
    """
    导出课程归档所需的 json 文件
    """
    since_time = datetime.fromtimestamp(int(request.GET.get('since', '0')), tz=timezone.utc)
    course = Course.objects.get(pk=course_id)
    all_pies = ProjectInExam.objects.filter(project__course=course)
    pjrs = ProblemJudgeRecord.objects.filter(project_in_exam__in=all_pies).filter(submitted_at__gte=since_time)
    if request.GET.get('until', None) is not None:
        until_time = datetime.fromtimestamp(int(request.GET.get('until')), tz=timezone.utc)
        pjrs = pjrs.filter(submitted_at__lte=until_time)
    students = Student.objects.filter(instructorclass__belong_to=course)
    info_list: list = []
    for stu_obj in students:
        stu_teacher = InstructorClass.objects.filter(student=stu_obj, belong_to=course).get().teacher
        problem_list = pjrs.filter(edx_username=stu_obj.student_id).values('problem').distinct()
        for problem in problem_list:
            pjr = pjrs.filter(edx_username=stu_obj.student_id, problem=problem['problem']) \
                      .order_by('-id')[0:1].get()
            if pjr.attachment:
                data = {
                    'student_id': stu_obj.student_id,
                    'teacher': stu_teacher,
                    'official_class': stu_obj.official_class,
                    'problem_id': pjr.problem.id,
                    'problem_name': pjr.problem.name,
                    'download_url':
                        s3_download_url(pjr.attachment.oss_token, pjr.attachment.type),
                    'download_filename': pjr.attachment.filename,
                    'pass': pjr.judge_result == 0,
                    'file_type': None \
                        if pjr.attachment.filename is None \
                        else str(pjr.attachment.filename).rsplit('.', maxsplit=1)[-1]
                }
                info_list.append(data)
    return success_api_response({"list": info_list})


COURSE_SET_API = wrapped_api({
    "get": get_course_list,
    "post": create_course
})

RETAKE_STUDENT_API = wrapped_api({
    "put": add_retake_student,
    "delete": remove_retake_student
})


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_http_methods(['PUT'])
@require_course_permission
def add_course_announcement(request: HttpRequest, course: Course, *args, **kwargs):
    """add announcement for course['id']

    [method]: PUT

    [route]: /api/announcement
    """
    body = parse_data(request)
    text = body.get('announcement_text', None)
    announcement = Announcement(content=text, course=course)
    announcement.save()
    return success_api_response({'result': 'OK, course announcement added',
                                 'data': list(
                                     Announcement.objects.filter(pk=announcement.pk).values('id', 'created_at',
                                                                                            'updated_at',
                                                                                            'content',
                                                                                            'course'))
                                 })


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_http_methods(['POST'])
@require_course_permission
def update_course_announcement(request: HttpRequest, course: Course, *args, **kwargs):
    """update course['id'] with announcement

    [method]: POST

    [route]: /api/announcement
    """
    body = parse_data(request)
    announcement_id = body.get('announcement_id', None)
    announcement_content = body.get('announcement_content', None)
    try:
        announcement = course.announcement_set.all().get(pk=announcement_id)
    except ObjectDoesNotExist:
        return failed_api_response(400, "announcement_id 不存在。")
    announcement.content = announcement_content
    announcement.save()
    return success_api_response({'result': 'OK, course announcement updated',
                                 'data': list(
                                     Announcement.objects.filter(pk=announcement.pk).values('id', 'created_at',
                                                                                            'updated_at',
                                                                                            'content',
                                                                                            'course'))
                                 })


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_VIEW])
@require_GET
@require_course_permission
def get_course_announcement(request: HttpRequest, course: Course, *args, **kwargs):
    """get announcement list of course['id']

    [method]: GET

    [route]: /api/announcement
    """
    data = {
        'data': list(course.announcement_set.all().values('id', 'created_at', 'updated_at', 'content', 'course'))
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_CHANGE])
@require_http_methods(['DELETE'])
@require_course_permission
def remove_course_announcement(request: HttpRequest, course: Course, *args, **kwargs):
    """remove announcement of course['id']

    [method]: DELETE

    [route]: /api/announcement
    """
    body: dict = parse_data(request)
    if 'all' in body and body['all']:
        course.announcement_set.all().delete()
        return success_api_response({'result': 'OK, announcement cleared'})
    announcement_id = body['announcement_id']
    if not isinstance(announcement_id, list):
        return failed_api_response(400, "announcement_id 参数必须为数组。")
    announcement_id = course.announcement_set.all().filter(id__in=announcement_id)
    for ann_id in announcement_id:
        ann_id.delete()
    return success_api_response({'result': 'OK, announcement removed'})


COURSE_ANNOUNCEMENT_API = wrapped_api({
    "put": add_course_announcement,
    "post": update_course_announcement,
    "get": get_course_announcement,
    "delete": remove_course_announcement
})
