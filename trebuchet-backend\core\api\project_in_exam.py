"""
define project_in_exam api
"""
import json
import pandas
from django.core.exceptions import FieldError, ValidationError
from django.db.models import F, QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest, HttpResponse
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_PIE_CHANGE, CORE_PIE_CREATE,
                                  CORE_PIE_DELETE, CORE_PIE_VIEW)
from core.api.query_utils import query_filter
from core.api.student_progress import RequirementParser
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from core.forms.exam import ProjectInExamInfo
from core.interface.student_progress import query_student_passed
from core.models import ProjectInExam
from core.models.exam import Exam
from core.models.exam_record import ExamRecord
from core.models.project import Project
from judge.interface.problem_judge_record import \
    get_problem_judge_record_for_core
from judge.models.problem import Problem
from judge.models.problem_judge_record import ProblemJudgeRecord
from judge.models.test_case_judge_record import TestCaseJudgeRecord

DEFAULT_COHORT_NAME = "UnderClass"


def _validate_create_project_in_exam(request: HttpRequest) -> bool:
    data: dict = parse_data(request)
    exam_id = data.get('exam_id', None)
    if exam_id is None or not isinstance(exam_id, int):
        return False
    if not Exam.objects.filter(id=exam_id).exists():
        return False
    del data['exam_id']
    project_in_exam_info = ProjectInExamInfo(data)
    if not project_in_exam_info.is_valid():
        return False
    project_id = data.get('project_id')
    return Project.objects.filter(id=project_id).exists()


def _validate_update_project_in_exam(request: HttpRequest) -> bool:
    data: dict = parse_data(request)
    if not data.keys() <= {'begin_time', 'duration', 'description', 'is_open_for_students', 'fail_analysis'}:
        return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_PIE_CREATE])
@require_POST
@validate_args(func=_validate_create_project_in_exam)
def create_project_in_exam(request: HttpRequest):
    """create project in exam

    [method]: POST

    [route]: /api/project-in-exam
    """
    data: dict = parse_data(request)
    project = Project.objects.get(id=data['project_id'])
    exam: Exam = Exam.objects.get(id=data['exam_id'])
    inherit_desc: bool = data['inherit_description']
    del data['project_id'], data['exam_id'], data['inherit_description']
    data['project'] = project
    data['exam'] = exam
    if inherit_desc:
        last = ProjectInExam.objects.filter(project=project).order_by('-id').first()
        if last is not None:
            data['description'] = last.description

    project_in_exam = ProjectInExam.objects.create(**data)

    return success_api_response({'id': project_in_exam.id})


@response_wrapper
@jwt_auth(perms=[CORE_PIE_VIEW])
@require_GET
@require_item_exist(model=ProjectInExam, field='id', item='id')
def get_project_in_exam(request: HttpRequest, query_id: int):
    """get project in exam

    [method]: GET

    [route]: /api/project-in-exam/<int:id>
    """
    model = ProjectInExam.objects.get(id=query_id)
    project_in_exam = model_to_dict(model)
    project_in_exam['project'] = model.project.id
    project_in_exam['exam'] = model.exam.id
    project_in_exam['problems'] = list(model.problems.order_by('core_projectinexam_problems.id').values('id', 'name'))
    project_in_exam['download_problems'] = list(model.download_problems.values('id', 'name'))
    question = model.question
    if question is not None:
        question = json.loads(question)
    project_in_exam['question'] = question
    return success_api_response(project_in_exam)


def _get_onclass_data(project_in_exam: ProjectInExam):
    exam_records = list(ExamRecord.objects
                        .filter(project_in_exam=project_in_exam)
                        .annotate(studentid=F("student__student_id"))
                        .annotate(name=F("student__name"))
                        .values("studentid", "name", "check_result", "check_comment"))

    check_result_convert = {-1: "F", 0: "D",
                            1: "C", 2: "B", 3: "A", 4: "A+"}

    query_id = project_in_exam.id
    problems = project_in_exam.problems.all()

    for record in exam_records:
        record.update(passed=query_student_passed(
            record["studentid"], query_id))
        # beautify
        record["student_id"] = record.pop("studentid")
        record["check_result"] = check_result_convert[record["check_result"]]
        # problems
        for problem in problems:
            problem_id = problem.id
            problem_name = problem.name
            judge_result = get_problem_judge_record_for_core(
                record["student_id"], problem_id, 0).exists()
            record.update(**{problem_name: judge_result})
    return exam_records


def  _get_afterclass_data(project_in_exam: ProjectInExam):
    exam_records = []
    for student in project_in_exam.project.student_whitelist.all():
        record = {
            "student_id": student.student_id,
            "name": student.name
        }
        for problem in project_in_exam.problems.all():
            problem_id = problem.id
            problem_name = problem.name
            judge_result = get_problem_judge_record_for_core(
                student.student_id, problem_id, 0).exists()
            record.update(**{problem_name: judge_result})
        exam_records.append(record)
    return exam_records


@response_wrapper
@jwt_auth(perms=[CORE_PIE_VIEW])
@require_GET
@require_item_exist(model=ProjectInExam, field="id", item="id")
def get_project_in_exam_csv(request: HttpRequest, query_id: int):
    """get a csv with examrecords of students in the projectinexam

    [method]: GET

    [route]: /api/project-in-exam/<int:id>/csv
    """
    project_in_exam = ProjectInExam.objects.get(id=query_id)

    # 对于课下考试进行特判
    if project_in_exam.exam.course.under_class_exam == project_in_exam.exam:
        exam_records = _get_afterclass_data(project_in_exam)
        meta_info_columns = ["student_id", "name"]
    else:
        exam_records = _get_onclass_data(project_in_exam)
        meta_info_columns = ["student_id", "name", "check_result", "passed", "check_comment"]

    data_frames = pandas.DataFrame(exam_records)
    problem_columns = filter(lambda col: col not in meta_info_columns, data_frames.columns.tolist())
    meta_info_columns.extend(problem_columns)
    data_frames = data_frames[meta_info_columns]

    return HttpResponse(data_frames.to_csv())


@response_wrapper
@jwt_auth(perms=[CORE_PIE_VIEW])
@require_GET
def get_exam_projects(request: HttpRequest, exam_id: int):
    """get exam projects

    [method]: GET

    [route]: /api/project-in-exam/<int:exam_id>/exam
    """
    projects = ProjectInExam.objects.filter(exam_id=exam_id)
    projects_info = [{
      "id": project.id,
      "name": project.project.name,
      "begin_time": project.begin_time,
      "duration": project.duration
    } for project in projects]
    return success_api_response({"projects": projects_info})

@response_wrapper
@jwt_auth(perms=[CORE_PIE_CHANGE])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_project_in_exam)
@require_item_exist(model=ProjectInExam, field='id', item='id')
def update_project_in_exam(request: HttpRequest, query_id: int):
    """update project in exam

    [method]: PUT

    [route]: /api/project-in-exam/<int:id>
    """
    project_in_exam = ProjectInExam.objects.get(pk=query_id)
    data: dict = parse_data(request)
    try:
        for key in data.keys():
            if data[key] is None:
                continue
            setattr(project_in_exam, key, data[key])
        project_in_exam.save()
    except TypeError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
    except ValidationError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[CORE_PIE_DELETE])
@require_http_methods(['DELETE'])
@require_item_exist(model=ProjectInExam, field='id', item='id')
def delete_project_in_exam(request: HttpRequest, query_id: int) -> dict:
    """delete project in exam

    [method]: DELETE

    [route]: /api/project-in-exam/<int:id>
    """
    model = ProjectInExam.objects.get(id=query_id)
    info = model_to_dict(model)
    info.pop("problems", None)
    model.delete()
    return success_api_response(info)


@response_wrapper
@jwt_auth(perms=[CORE_PIE_VIEW])
@require_GET
@query_filter(fields=[("exam__id", int)])
def list_project_in_exams(request: HttpRequest, *args, **kwargs):
    """list project in exams

    [method]: GET

    [route]: /api/project-in-exam
    """
    models: QuerySet = ProjectInExam.objects.all()
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    models_info = list(
        models.values(
            'id', 'begin_time', 'duration', 'project__name', 'project__id'
        )
    )
    return success_api_response({"models": models_info})


def validate_requirement(request: HttpRequest) -> bool:
    """validate requirement with parse_requirement

    Args:
        request (HttpRequest): post request
    """
    data: dict = parse_data(request)
    requirement = data.get("requirement")
    if not isinstance(requirement, str) or len(requirement) > 200:
        return False
    problems = data.get("problems")
    if not isinstance(problems, list):
        return False
    # validate
    lexer = RequirementParser(requirement, lambda problem_id: True)
    try:
        lexer.parse_expr()
        if lexer.cur != len(lexer.data) - 1:
            raise ValueError
    except ValueError:
        return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_PIE_CHANGE])
@require_http_methods(["PUT"])
@validate_args(func=validate_requirement)
@require_item_exist(model=ProjectInExam, field="id", item="id")
def update_requirement_in_project_in_exam(request: HttpRequest, query_id: int):
    """create or update requirement

    [method]: PUT

    [route]: /api/project-in-exam/<int:id>/requirement

    Args:
        request (HttpRequest): post request
        query_id (int): project_in_exam id
    """
    model = ProjectInExam.objects.get(pk=query_id)
    data: dict = parse_data(request)
    requirement = data.get("requirement")
    m_requirement = data.get("achieveRequirement")
    problems_ids = data.get("problems")
    download_problem_ids = data.get("downloadProblems")
    id_list = list(dict.fromkeys(problems_ids + download_problem_ids))  # 去重
    problems = Problem.objects.filter(pk__in=id_list)

    if len(id_list) != len(problems):
        found_list = [item.id for item in problems]
        invalid_list = [item for item in id_list if item not in found_list]
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "编号为 {} 的问题不存在".format(invalid_list))

    model.problems.clear()
    model.download_problems.clear()
    model.pass_requirement = requirement
    model.mark_requirement = m_requirement
    for pid in problems_ids:
        model.problems.add(pid)
        model.save()
    for pid in download_problem_ids:
        model.download_problems.add(pid)

    model.save()

    return success_api_response({"result": "Ok, requirement has been updated."})


@response_wrapper
@jwt_auth(perms=[CORE_PIE_VIEW])
@require_GET
@require_item_exist(model=ProjectInExam, field="id", item="id")
def retrieve_requirement_in_project_in_exam(request: HttpRequest, query_id: int):
    """retrieve requirement information

    [method]: GET

    [route]: /api/project-in-exam/<int:id>/requirement

    Args:
        request (HttpRequest): get request
        query_id (int): project_in_exam id
    """
    model = ProjectInExam.objects.get(pk=query_id)
    problems = list(model.problems.all().values("id", "name"))
    requirement = model.pass_requirement
    data = {
        "requirement": requirement,
        "problems": problems,
        "fail_analysis": model.fail_analysis
    }
    return success_api_response(data)


def validate_question(request: HttpRequest) -> bool:
    """check the validity of question

    Args:
        request (HttpRequest): post request
    """
    data: dict = parse_data(request)
    questions = data.get("question")
    if not isinstance(questions, str) or len(questions) == 0:
        return False
    try:
        questions = json.loads(questions)
    except json.JSONDecodeError:
        return False
    if len(questions) == 0:
        return False
    for question_group in questions:
        if len(question_group) == 0:
            return False
        # for question in question_group:
        #     if not isinstance(question, str) or question.isspace() or len(questions) == 0:
        #         return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_PIE_CHANGE])
@require_http_methods(["PUT"])
@validate_args(func=validate_question)
@require_item_exist(model=ProjectInExam, field="id", item="id")
def update_question_in_project_in_exam(request: HttpRequest, query_id: int):
    """create or update question

    [method]: PUT

    [route]: /api/project-in-exam/<int:id>/question

    Args:
        request (HttpRequest): post request
        query_id (int): project_in_exam id
    """
    pie = ProjectInExam.objects.get(pk=query_id)
    data: dict = parse_data(request)
    questions = data.get("question")
    pie.question = questions
    pie.save()
    return success_api_response({"result": "Ok, question has been updated."})


@response_wrapper
@jwt_auth(perms=[CORE_PIE_VIEW])
@require_GET
@require_item_exist(model=ProjectInExam, field="id", item="id")
def get_question_in_project_in_exam(request: HttpRequest, query_id: int):
    """get questions information

    [method]: GET

    [route]: /api/project-in-exam/<int:id>/question

    Args:
        request (HttpRequest): get request
        query_id (int): project_in_exam id
    """
    pie = ProjectInExam.objects.get(pk=query_id)
    data = {
        "question": pie.question
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PIE_VIEW])
@require_GET
@require_item_exist(model=ProjectInExam, field="id", item="id")
def download_error_info(request: HttpRequest, query_id: int):
    """download error info
    """
    pie = ProjectInExam.objects.get(pk=query_id)
    data = []

    judge_records = ProblemJudgeRecord.objects.filter(project_in_exam=pie)
    for record in judge_records:
        student_id = record.edx_username  # 这是学号
        submitted_time = record.submitted_at.strftime("%Y-%m-%d %H:%M:%S")
        test_case_records = list(TestCaseJudgeRecord.objects.filter(problem_judge_record=record))
        for test_case_record in test_case_records:
            comment = test_case_record.comment \
                if 'Compilation error' not in test_case_record.comment \
                else 'Compilation error'
            data.append({
                "student_id": student_id,
                "testcase_id": test_case_record.test_case.id,
                "comment": comment,
                "submitted_time": submitted_time
            })
    data_frames = pandas.DataFrame(data)
    meta_info_columns = ["student_id", "testcase_id", "comment", "submitted_time"]
    data_frames = data_frames[meta_info_columns]

    return success_api_response({
        "data": data_frames.to_csv(),
        "filename": f"{pie.project.name}_{pie.exam.date}_error_info.csv"
    })

PROJECT_IN_EXAM_DETAIL_API = wrapped_api({
    "get": get_project_in_exam,
    "put": update_project_in_exam,
    "delete": delete_project_in_exam
})

PROJECT_IN_EXAM_SET_API = wrapped_api({
    'get': list_project_in_exams,
    'post': create_project_in_exam
})

REQUIREMENT_SET_API = wrapped_api({
    'put': update_requirement_in_project_in_exam,
    'get': retrieve_requirement_in_project_in_exam
})

QUESTION_SET_API = wrapped_api({
    'put': update_question_in_project_in_exam,
    'get': get_question_in_project_in_exam
})
