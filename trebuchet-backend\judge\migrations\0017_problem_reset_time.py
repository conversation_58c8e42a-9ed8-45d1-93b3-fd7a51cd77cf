# Generated by Django 3.1.7 on 2021-09-08 14:21

import datetime
from django.db import migrations, models
from django.utils.timezone import utc


class Migration(migrations.Migration):
    dependencies = [
        ('judge', '0016_auto_20210901_1726'),
    ]

    operations = [
        migrations.AddField(
            model_name='problem',
            name='reset_time',
            field=models.DateTimeField(default=datetime.datetime(1970, 1, 1, 0, 0, tzinfo=utc)),
        ),
    ]
