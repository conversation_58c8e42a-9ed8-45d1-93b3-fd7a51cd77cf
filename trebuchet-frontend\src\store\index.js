import Vue from 'vue'
import Vuex from 'vuex'
import VuexPersistence from 'vuex-persist'

import app from './module/app'
import view from './module/view'
import user from './module/user'
import onClass from './module/onClass'

Vue.use(Vuex)

const vuexLocal = new VuexPersistence({
  storage: window.localStorage,
  modules: ['user', 'onClass', 'view']
})

export default new Vuex.Store({
  modules: {
    app,
    view,
    user,
    onClass
  },
  plugins: [vuexLocal.plugin]
})
