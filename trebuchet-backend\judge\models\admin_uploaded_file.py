"""
declare AdminUploadedFile model
"""

from django.contrib.auth import get_user_model
from django.db import models

from judge.constants import TOKEN_MAX_LENGTH
from judge.models.permissions import (DOWNLOAD_PROBLEM_ATTACHMENT,
                                      DOWNLOAD_TESTCASE_ATTACHMENT,
                                      UPLOAD_PROBLEM_ATTACHMENT,
                                      UPLOAD_TESTCASE_ATTACHMENT)
from judge.models.user_submitted_file import FileTypes


class AdminUploadedFile(models.Model):
    """
    Storing the data about user's upload.
    """
    user = models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True)
    filename = models.CharField(max_length=100)
    type = models.IntegerField(choices=FileTypes.choices, default=FileTypes.JUDGE_DATA)
    oss_token = models.Char<PERSON>ield(
        max_length=TOKEN_MAX_LENGTH, blank=True)
    uploaded_at = models.DateTimeField(auto_now=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (DOWNLOAD_PROBLEM_ATTACHMENT, <PERSON>OWNLOAD_PROBLEM_ATTACHMENT),
            (UPLOAD_PROBLEM_ATTACHMENT, UPLOAD_PROBLEM_ATTACHMENT),
            (DOWNLOAD_TESTCASE_ATTACHMENT, DOWNLOAD_TESTCASE_ATTACHMENT),
            (UPLOAD_TESTCASE_ATTACHMENT, UPLOAD_TESTCASE_ATTACHMENT)
        ]
