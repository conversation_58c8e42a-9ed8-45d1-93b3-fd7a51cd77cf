"""
    instructor class management api
"""
from enum import Enum

from django.db.models.aggregates import Count
from django.http.request import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_CLASS_CHANGE, CORE_CLASS_CREATE,
                                  CORE_CLASS_VIEW, CORE_CLASS_DELETE)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, require_item_miss,
                            response_wrapper, success_api_response,
                            validate_args, wrapped_api)
from core.forms.instructor_class import InstructorClassInfo
from core.models.course import Course
from core.models.instructor_class import InstructorClass
from core.models.student import Student


def validate_class_request(request: HttpRequest) -> bool:
    """validate class post/put request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"type", "name", "teacher", "student", "belong_to"}
    if not data.keys() <= allowed_fields:
        return False
    # check form
    students = data.pop("student", None)
    info = InstructorClassInfo(data)
    if not info.is_valid():
        return False
    # check students
    if students is not None:
        if len(students) != len(set(students)):
            return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_CLASS_VIEW])
@require_GET
def get_class_list(request):
    """get class list

    [method]: GET

    [route]: /api/classes
    """
    user = request.user
    body = request.GET.dict()
    body['belong_to'] = user.userprofile.course
    classes = InstructorClass.objects.filter(
        **body).annotate(student_count=Count('student'))
    data = classes.values('id', 'name', 'teacher',
                          'student_count', 'belong_to')
    return_data = {'data': list(data)}
    return success_api_response(return_data)


@response_wrapper
@jwt_auth(perms=[CORE_CLASS_CREATE])
@require_POST
@validate_args(func=validate_class_request)
@require_item_miss(model=InstructorClass, field='name')
def create_class(request):
    """create a class

    [method]: POST

    [route]: /api/classes
    """
    body = parse_data(request)
    name = body.get('name')
    teacher = body.get('teacher')
    student = body.get('student')
    new_class = InstructorClass(name=name, teacher=teacher)
    if body.get('belong_to', None) is not None:
        course = Course.objects.get(pk=body.get('belong_to'))
        new_class.belong_to = course
    else:
        user = request.user
        new_class.belong_to = user.userprofile.course
    new_class.save()
    students = Student.objects.filter(student_id__in=student)
    new_class.student.add(*students)
    data = {
        'id': new_class.pk
    }
    return success_api_response(data)


class PutType(Enum):
    """the argument named type in PUT /api/class/<str:id> has 3 choices
    """
    ADD = 1
    REMOVE = 2
    CHANGE = 3
    CLEAR = 4


@response_wrapper
@jwt_auth(perms=[CORE_CLASS_CHANGE])
@require_http_methods(['PUT'])
@validate_args(func=validate_class_request)
@require_item_exist(model=InstructorClass, field='id', item='class_id')
def put_class(request, class_id):
    """update class info

    [method]: PUT

    [route]: /api/classes/<int:class_id>
    """
    body = parse_data(request)
    put_type = body.get('type')
    if put_type == PutType.ADD.value:
        student = body.get('student')
        change_class = InstructorClass.objects.get(pk=class_id)
        students = Student.objects.filter(student_id__in=student)
        repeat_students = []
        for stu in students:
            repeat_class_set = stu.instructorclass_set.filter(belong_to=change_class.belong_to)
            if repeat_class_set:
                stu.instructorclass_set.remove(*repeat_class_set)
                repeat_students.append(stu.student_id)
        found_students = [stu.student_id for stu in students]
        unsuccessful_students = [stu for stu in student if stu not in found_students]
        if found_students:
            change_class.student.add(*students)
        if unsuccessful_students or repeat_students:
            return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                       '已加入学生 {}。学生 {} 未找到。学生 {} 有重复班级，现已移除'
                                       .format(found_students, unsuccessful_students, repeat_students))
    elif put_type == PutType.REMOVE.value:
        student = body.get('student')
        change_class = InstructorClass.objects.get(pk=class_id)
        students = Student.objects.filter(student_id__in=student)
        existed_students = change_class.student.all()
        changed_students = students & existed_students
        change_class.student.remove(*changed_students)
    elif put_type == PutType.CHANGE.value:
        body.pop('type')
        belong_to = body.get('belong_to', None)
        if belong_to is None:
            InstructorClass.objects.filter(pk=class_id).update(**body)
        else:
            body.pop('belong_to')
            InstructorClass.objects.filter(pk=class_id).update(**body)
            InstructorClass.objects.get(
                pk=class_id).belong_to = Course.objects.get(pk=belong_to)
    elif put_type == PutType.CLEAR.value:
        clear_class = InstructorClass.objects.get(pk=class_id)
        clear_class.student.clear()
    else:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'Sorry, this api does not support this \'type\' ')
    return success_api_response({'result': 'OK, class updated'})


@response_wrapper
@jwt_auth(perms=[CORE_CLASS_VIEW])
@require_GET
@require_item_exist(model=InstructorClass, field='id', item='class_id')
def get_class(request, class_id):
    """get a class

    [method]: GET

    [route]: /classes/<int:class_id>
    """
    target_class = InstructorClass.objects.get(pk=class_id)
    stus = target_class.student.all().values('student_id', 'name', 'department')
    student = list(stus)
    data = {
        'id': target_class.pk,
        'name': target_class.name,
        'teacher': target_class.teacher,
        'student': student,
        'belong_to': target_class.belong_to.pk
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_CLASS_DELETE])
@require_http_methods(['DELETE'])
@require_item_exist(model=InstructorClass, field='id', item='class_id')
def delete_class(request, class_id):
    """delete a class

    [method]: DELETE

    [route]: /classes/<int:class_id>
    """
    target_class = InstructorClass.objects.get(pk=class_id)
    target_class.delete()
    return success_api_response({'result': 'OK, class deleted'})


CLASS_DETAIL_API = wrapped_api({
    "get": get_class,
    "put": put_class,
    "delete": delete_class
})

CLASS_SET_API = wrapped_api({
    "get": get_class_list,
    "post": create_class
})
