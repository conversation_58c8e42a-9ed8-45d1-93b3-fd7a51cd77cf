# 进度检测功能设计报告

## 1. 功能概述

基于现有教学管理系统，新增4个进度检测功能：

1. **进度慢检测**：检测提交次数过少的学生，支持一键导出
2. **考试通过人数检测及无人通过警报**：监测考试的实时通过情况
3. **多次挂在同一个P检测**：统计在同一P上失败次数超过阈值的学生
4. **多次没有课上资格检测**：由于现有数据库没有保存学生历史上的课程进度推进记录，这里尝试用当前课程进度状态和课上考试记录来表示这一数值。

## 2. 后端接口设计

### 2.1 新增API接口

#### 功能1：进度慢检测
```
GET /api/progress/slow-detection/<int:course_pk>
```
**参数：** submission_threshold (提交次数阈值), days_range (统计天数范围)
**功能：** 统计指定课程中学生在最近N天内的代码提交次数，返回提交次数低于阈值的学生名单。

#### 功能2：考试通过人数检测及无人通过警报
```
GET /api/progress/exam-pass-detection/<int:exam_pk>
```
**功能：** 实时检测指定考试的通过情况，包括当前通过人数及具体通过者的名单、考试持续时间。警报在前端实现。

#### 功能3：多次挂在同一个P检测
```
GET /api/progress/repeated-failures/<int:course_pk>
```
**参数：** failure_threshold (失败次数阈值)
**功能：** 统计课上成绩记录，在当前P上失败次数超过阈值的学生。只统计学生当前所在P的失败情况，已经通过的P不做考虑。

#### 功能4：多次没有课上资格检测
```
GET /api/progress/qualification-failures/<int:course_pk>
```
**参数：** failure_threshold (失败次数阈值)
**功能：** 统计在当前P上多次失去课下资格的学生。具体计算方式是：假如一个人当前的课程进度是某个P的课下且未通过，查询其课上考试的记录，取他参加过的最近一次课上考试，对比所有课上考试的信息，确认出他有几次课上考试没有参加。

## 3. 数据库更改设计

无

