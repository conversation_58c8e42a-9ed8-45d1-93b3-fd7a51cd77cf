# 如何查看进度检测页面

## 快速启动

### 1. 启动前端项目
```bash
cd trebuchet-frontend
npm install  # 如果还没有安装依赖
npm run serve-dev-local  # 启动开发服务器
```

### 2. 访问页面

启动成功后，在浏览器中访问以下地址：

#### 主要功能页面：

1. **考试管理 - 进度检测**
   - URL: `http://localhost:8080/exam/progress-detection`
   - 包含功能：进度慢检测、同一P多次失败检测、课上资格失败检测

2. **课上信息 - 进度检测**  
   - URL: `http://localhost:8080/on-exam/exam-progress-detection`
   - 包含功能：考试通过人数检测及无人通过警报

#### 概览页面：
3. **功能概览页面**
   - URL: `http://localhost:8080/index/progress-overview`
   - 展示所有功能的概览和技术说明

## 页面功能演示

### 考试管理 - 进度检测页面

#### 功能1：进度慢检测
1. 选择课程
2. 设置参数：
   - 提交次数阈值（默认5次）
   - 统计天数范围（默认7天）
3. 点击"开始检测"按钮
4. 查看检测结果表格
5. 点击"一键导出"下载CSV文件

#### 功能3：同一P多次失败检测
1. 设置失败次数阈值（默认3次）
2. 点击"开始检测"
3. 查看在同一P上多次失败的学生列表

#### 功能4：课上资格失败检测
1. 设置失败次数阈值（默认2次）
2. 点击"开始检测"
3. 查看多次失去课上资格的学生列表

### 课上信息 - 进度检测页面

#### 功能2：考试通过人数检测
1. 选择考试
2. 查看实时统计数据：
   - 当前通过人数
   - 总参考人数  
   - 通过率
3. 监控考试持续时间
4. 设置无人通过警报阈值
5. 查看通过/未通过学生列表
6. 查看通过趋势图表
7. 开启/关闭自动刷新

## 模拟数据说明

所有页面当前使用模拟数据，包括：

- **学生信息**: 张三、李四、王五等示例学生
- **考试数据**: 模拟的通过情况和时间统计
- **进度数据**: 模拟的提交次数、失败次数等

这些数据仅用于演示页面效果和交互流程。

## 页面特色功能

### 1. 数据可视化
- 使用ECharts展示通过趋势图
- 彩色标签显示不同状态
- 统计卡片展示关键指标

### 2. 交互体验
- 加载状态提示
- 成功/错误消息反馈
- 参数配置界面
- 自动/手动刷新切换

### 3. 数据导出
- CSV格式导出功能
- 一键下载学生名单

### 4. 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的布局

## 注意事项

1. **模拟环境**: 当前为演示版本，所有数据均为模拟数据
2. **API接口**: 已定义但未连接真实后端
3. **权限验证**: 未实现用户权限控制
4. **数据持久化**: 页面刷新后数据会重置

## 技术架构

- **前端框架**: Vue 2
- **UI组件**: View Design (iView)
- **图表库**: ECharts
- **构建工具**: Vue CLI
- **开发服务器**: Webpack Dev Server

## 下一步工作

1. 连接真实的后端API接口
2. 处理实际数据格式
3. 添加用户权限验证
4. 完善错误处理机制
5. 进行性能优化

---

如果您在查看页面时遇到任何问题，请检查：
1. Node.js 和 npm 是否正确安装
2. 依赖包是否完整安装
3. 开发服务器是否成功启动
4. 浏览器控制台是否有错误信息
