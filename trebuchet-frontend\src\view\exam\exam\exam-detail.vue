<template>
  <div>
    <Card>
      <p slot="title">考试信息</p>
      <Row>
        <Form ref="examUpdate" :model="exam" :label-width="110">
          <form-item prop="date" label="考试日期">
            <p>{{ exam.date }}</p>
          </form-item>
          <form-item prop="active" label="Active">
            <radio-group v-model="active">
              <radio label="true" />
              <radio label="false" />
            </radio-group>
          </form-item>
          <form-item>
            <Button type="primary" @click="handleSubmit('examUpdate')">确认修改</Button>
          </form-item>
        </Form>
      </Row>
      <Tabs v-model="tabName" @on-click="onClick">
        <Tab-pane label="Project In Exam Arrange" name="project">
          <Table :data="projectData" :columns="projectColumns" />
          <br />
          <Row>
            <Button type="primary" style="margin-right: 5px" @click="onAddProject">创建新 Project In Exam</Button>
            <Table v-show="false" ref="tables" />
            <Button type="primary" style="margin-right: 5px" @click="onDownload">下载考试学生</Button>
            <Modal v-model="addProject" title="添加新的 PIE" @on-ok="handleSubmit('projectNew')">
              <Form ref="projectNew" :model="newProject" :rules="projectRule" :label-width="110">
                <form-item prop="project_id" label="Project ID">
                  <Select v-model="newProject.project_id" @on-change="onChange">
                    <Option v-for="item in projectAll" :key="item.id" :value="item.id">
                      {{ item.name }} : {{ item.id }}
                    </Option>
                  </Select>
                </form-item>
                <form-item prop="begin_time" label="Begin Time">
                  <time-picker v-model="newProject.begin_time" type="time" placeholder="请选择时间" />
                </form-item>
                <form-item prop="duration" label="Duration(min)">
                  <Input v-model="newProject.duration" type="text" />
                </form-item>
                <form-item prop="inherit_description" label="继承">
                  <Checkbox v-model="newProject.inherit_description">从上一次考试继承说明内容</Checkbox>
                </form-item>
              </Form>
            </Modal>
          </Row>
        </Tab-pane>
        <Tab-pane label="Seats Arrange" name="seat">
          <template v-if="!isCreate">
            <filter-table :data="seatFilterData" :columns="columns" :height="400" @on-search="onSearch" />
            <br />
            <Row>
              <Button type="primary" style="margin-right: 5px" @click="onSeatDelete">删除座位表</Button>
            </Row>
          </template>
          <template v-else>
            <Form ref="seatNew" :model="newSeat" :label-width="100">
              <form-item label="座位表">
                <div style="display: flex; align-items: center">
                  <Upload :before-upload="beforeUpload" action="">
                    <Button icon="ios-cloud-upload-outline">上传 CSV 文件</Button>
                  </Upload>
                  <span style="margin-left: 10px; font-size: small">文件列名：["student", "seat"]</span>
                </div>
                <strong>
                  <span style="font-size: small">请确保 CSV 文件的编码格式为 UTF-8</span>
                </strong>
              </form-item>
              <form-item>
                <Button :disabled="!uploadFileReady" type="primary" @click="handleSubmit('seatNew')">
                  {{ uploadBtnMsg }}
                </Button>
              </form-item>
            </Form>
          </template>
        </Tab-pane>
        <Tab-pane label="考试通过情况" name="check">
          <Select v-model="selectProject" style="width: 200px">
            <Option v-for="item in examProject" :key="item.name" :value="item.name">
              {{ item.name }}
            </Option>
          </Select>
          <br />
          <br />
          <Table :data="checkResult.filter((item) => item.project === selectProject)" :columns="checkColumns" />
        </Tab-pane>
      </Tabs>
    </Card>
    <Card style="margin-top: 20px; margin-bottom: 20px">
      <p slot="title">通知管理</p>
      <Row>
        <Form ref="newsCreate" :model="news" :rules="newsRule" :label-width="130">
          <form-item prop="content" label="通知内容">
            <Input v-model="news.content" type="textarea" />
          </form-item>
          <form-item prop="star">
            <Checkbox v-model="news.star">重要通知</Checkbox>
          </form-item>
          <form-item>
            <Button type="primary" @click="handleNewsPost">发布通知</Button>
          </form-item>
        </Form>
      </Row>
      <Row>
        <Table :data="newsList" :columns="newsColumns" />
      </Row>
    </Card>
  </div>
</template>

<script>
import {
  checkExam,
  createNewsReq,
  deleteNewsReq,
  examIdReq,
  examProjectIdReq,
  examProjectReq,
  examSeatReq,
  getExamStudent,
  listNewsReq,
  pieCSV
} from '@/api/exam'
import { getArrayFromFile, getErrModalOptions, getLocalTime, getTableDataFromArray, processDownload } from '@/libs/util'
import { projectReq } from '@/api/project'
import FilterTable from '@/view/filter-table/filter-table'
import _ from 'lodash'
import { LinkButton, ActionButton, Spacer, PercentTooltip } from '@/libs/render-item'

export default {
  name: 'ExamDetail',
  components: { FilterTable },
  data() {
    return {
      newsRule: {
        content: [{ required: true, message: '请填写通知内容', trigger: 'blur' }]
      },
      news: { content: '', star: false },
      newsId: null,
      newsList: [],
      newsColumns: [
        {
          title: 'ID',
          minWidth: 75,
          key: 'id'
        },
        {
          title: '发送时间',
          minWidth: 150,
          sortable: true,
          key: 'created_at',
          render: (h, params) => h('div', getLocalTime(params.row['created_at']))
        },
        {
          title: 'PIE',
          key: 'pie_id',
          render: (h, params) =>
            h(
              'div',
              params.row.pie_id !== null
                ? params.row.pie_id.toString() + ' (' + params.row['pie__project__name'] + ')'
                : '所有 PIE'
            )
        },
        {
          title: '重要',
          key: 'star'
        },
        {
          title: '内容',
          key: 'content',
          width: 300,
          ellipsis: true,
          tooltip: true
        },
        {
          title: '操作',
          key: null,
          render: (h, params) => ActionButton(h, () => this.handleNewsDelete(params.row.id), '删除', false)
        }
      ],
      exam: {},
      seatData: [],
      seatFilterData: [],
      columns: [
        {
          title: 'Student ID',
          key: 'student',
          filter: {
            type: 'input'
          }
        },
        {
          title: 'Room',
          key: 'seat',
          render: (h, params) => h('div', params.row.seat.room.name),
          filter: {
            type: 'input'
          }
        },
        {
          title: 'Seat',
          render: (h, params) => h('div', params.row.seat.name)
        }
      ],
      active: 'false',
      isCreate: false,
      newSeat: {
        data: []
      },
      csvColumns: [],
      uploadBtnMsg: '确认上传',
      expectedColumnNames: ['student', 'seat'],
      tabName: 'project',
      projectData: [],
      projectColumns: [
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: 'Project Name',
          key: 'project__name'
        },
        {
          title: 'Begin Time',
          key: 'begin_time'
        },
        {
          title: 'Duration',
          key: 'duration'
        },
        {
          title: 'Action',
          render: (h, params) =>
            h('div', [
              LinkButton(h, params.row.id, 'project_in_exam', '查看修改', false),
              Spacer(h),
              ActionButton(h, () => this.onProjectDelete(params.row.id), '删除', false)
            ])
        },
        {
          title: 'Download',
          render: (h, params) => ActionButton(h, () => this.onDownloadPIE(params.row.id), '下载考试数据', false)
        }
      ],
      addProject: false,
      newProject: {
        project_id: null,
        begin_time: null,
        duration: null,
        inherit_description: true
      },
      projectRule: {
        project_id: [
          {
            validator(rule, value, callback) {
              try {
                if (value === null) callback(new Error('请选择project'))
                if (value >= 0) {
                  callback()
                }
                callback(new Error('请输入合法 project id'))
              } catch (error) {
                callback(error)
              }
            },
            required: true
          }
        ],
        begin_time: [{ required: true, message: '请填写开始时间', trigger: 'change' }],
        duration: [{ required: true, message: '请填写考试时长', trigger: 'blur' }]
      },
      searchName: null,
      projectAll: [],
      checkResult: [],
      checkColumns: [
        {
          title: '系号',
          key: 'department'
        },
        {
          title: '通过人数',
          key: 'pass',
          render: (h, params) => {
            return params.row.pass_error === 0
              ? h('p', `${params.row.pass}`)
              : h(
                  'a',
                  {
                    on: {
                      click: () => {
                        this.$Modal.info({
                          title: '异常学生学号',
                          content: params.row.pass_error_student.toString()
                        })
                      }
                    }
                  },
                  `${params.row.pass}(点击查看异常学生)`
                )
          }
        },
        {
          title: '未通过人数',
          key: 'fail',
          render: (h, params) => {
            return params.row.fail_error === 0
              ? h('p', `${params.row.fail}`)
              : h(
                  'a',
                  {
                    on: {
                      click: () => {
                        this.$Modal.info({
                          title: '异常学生学号',
                          content: params.row.fail_error_student.toString()
                        })
                      }
                    }
                  },
                  `${params.row.fail}(点击查看异常学生)`
                )
          }
        },
        {
          title: '通过比例',
          render: (h, params) => PercentTooltip(h, params.row.pass, params.row.pass + params.row.fail)
        }
      ],
      selectProject: null,
      examProject: []
    }
  },
  computed: {
    updateExam() {
      return {
        date: this.exam.date,
        active: this.active === 'true'
      }
    },
    columnNames() {
      return this.csvColumns.map((item) => item.title)
    },
    uploadFileReady() {
      if (!this.columnNames || this.columnNames.length !== this.expectedColumnNames.length) {
        return false
      }
      return this.columnNames.every((item, index) => item === this.expectedColumnNames[index])
    }
  },
  mounted() {
    this.loadData()
    this.loadNews()
    this.loadProjectData()
  },
  methods: {
    async loadNews() {
      await listNewsReq(this.$route.params.id)
        .then((res) => {
          this.newsList = res.data.news
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    loadData() {
      examIdReq('get', this.$route.params.id, {})
        .then((res) => {
          this.exam = res.data
          this.active = this.exam.active ? 'true' : 'false'
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    loadProjectData() {
      examProjectReq('get', {
        exam__id__exact: this.$route.params.id
      })
        .then((res) => {
          this.projectData = res.data.models
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    upload(name) {
      if (name === 'examUpdate') {
        return examIdReq('put', this.$route.params.id, this.updateExam)
      } else {
        return examProjectReq('post', {
          exam_id: parseInt(this.$route.params.id),
          project_id: parseInt(this.newProject.project_id),
          begin_time: this.newProject.begin_time,
          duration: parseInt(this.newProject.duration),
          inherit_description: this.newProject.inherit_description
        })
      }
    },
    onClick(name) {
      switch (name) {
        case 'project': {
          this.loadProjectData()
          break
        }
        case 'seat': {
          examSeatReq('get', this.$route.params.id, {})
            .then((res) => {
              this.seatData = res.data.data
              this.isCreate = this.seatData.length === 0
              this.onSearch({})
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
          break
        }
        case 'check': {
          this.checkResult.length = 0
          this.examProject.length = 0
          checkExam(this.$route.params.id)
            .then((res) => {
              Object.keys(res.data).forEach((project) => {
                this.examProject.push({ name: project })
                Object.keys(res.data[project]).forEach((department) => {
                  if (department !== 'passed' && department !== 'total' && department !== 'retaker') {
                    this.checkResult.push({
                      project: project,
                      department: department === 'retaker' ? '重修生' : department,
                      pass: res.data[project][department].normal_student.passed.count,
                      pass_error: res.data[project][department].normal_student.passed['error_students'].length,
                      fail: res.data[project][department].normal_student['failed'].count,
                      fail_error: res.data[project][department].normal_student['failed']['error_students'].length,
                      pass_error_student: res.data[project][department].normal_student['passed']['error_students'],
                      fail_error_student: res.data[project][department].normal_student['failed']['error_students']
                    })
                  }
                })
              })
              this.selectProject = this.examProject[0] === undefined ? null : this.examProject[0].name
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
          break
        }
        default:
          break
      }
    },
    onChange(id) {
      this.newProject.project_id = id
    },
    onDownload() {
      getExamStudent(this.exam.id)
        .then((res) => {
          this.$refs.tables.exportCsv({
            filename: `exam_${this.exam.id}.csv`,
            columns: [
              { key: 'student__student_id' },
              { key: 'student__name' },
              { key: 'project_in_exam__project__name' }
            ],
            data: res.data.students
          })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    beforeUpload(file) {
      getArrayFromFile(file)
        .then((data) => {
          const { columns, tableData } = getTableDataFromArray(data)
          this.newSeat.data = tableData.map((item) => {
            return {
              student: item.student,
              seat: parseInt(item.seat)
            }
          })
          this.csvColumns = columns
          if (!this.uploadFileReady) {
            this.uploadBtnMsg = '格式不符'
            this.$Notice.warning({ title: '格式不符' })
          } else {
            this.uploadBtnMsg = '确认上传'
          }
        })
        .catch((err) => {
          getErrModalOptions(getErrModalOptions(err))
          this.$Notice.warning({ title: '只能上传 CSV 文件' })
        })
      return false
    },
    createSet(data) {
      return examSeatReq('post', this.$route.params.id, data)
    },
    handleSubmit(name) {
      if (name === 'seatNew') {
        this.createSet(this.newSeat)
          .then(() => {
            this.$Notice.success({ title: '创建成功' })
            this.loadData()
            this.tabName = 'project'
          })
          .catch((error) => {
            this.$Modal.error(getErrModalOptions(error))
          })
      } else {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.upload(name)
              .then(() => {
                this.$Notice.success({ title: '修改成功' })
                this.newProject = {
                  project_id: null,
                  begin_time: null,
                  duration: null,
                  inherit_description: true
                }
                this.loadData()
                this.loadProjectData()
              })
              .catch((error) => {
                this.$Modal.error(getErrModalOptions(error))
              })
          } else {
            this.$Notice.warning({ title: '表单验证失败' })
          }
        })
      }
    },
    onSeatDelete() {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          examSeatReq('delete', this.$route.params.id, {})
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.loadData()
              this.isCreate = true
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    },
    onProjectDelete(id) {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          examProjectIdReq('delete', id, {})
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.loadProjectData()
              this.isCreate = true
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    },
    onAddProject() {
      this.addProject = true
      projectReq('get', {
        order_by: 'name',
        page: 1,
        page_size: 20
      })
        .then((res) => {
          return projectReq('get', {
            order_by: 'name',
            page: 1,
            page_size: res.data['total_count']
          })
        })
        .then((res) => {
          this.projectAll = res.data.models
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
    },
    onSearch(search) {
      search = this.refactorSearchObject(search)
      this.seatFilterData = this.seatData.filter((item) => {
        return (
          (search['student'] === undefined ? true : item.student === search.student) &&
          (search['seat'] === undefined ? true : item.seat.room.name === search.seat)
        )
      })
    },
    refactorSearchObject(search) {
      return _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
    },
    async onDownloadPIE(pie) {
      try {
        const res = await pieCSV(pie)
        processDownload('\uFEFF' + res.data, `PIE ${pie} 考试情况.csv`)
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    handleNewsPost() {
      this.$refs['newsCreate'].validate(async (valid) => {
        if (valid) {
          try {
            await createNewsReq({
              pie_id: null,
              exam_id: this.$route.params.id,
              active: true,
              content: this.news.content,
              star: this.news.star
            })
            this.$Notice.success({ title: '发布成功' })
            this.news.content = ''
            await this.loadNews()
          } catch (e) {
            this.$Modal.error(getErrModalOptions(e))
          }
        }
      })
    },
    async handleNewsDelete(id) {
      try {
        await deleteNewsReq(id)
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
      await this.loadNews()
    }
  }
}
</script>

<style lang="less" scoped>
#exam-card-container * {
  margin-bottom: 5px;
}
</style>
