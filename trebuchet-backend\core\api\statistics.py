"""Some statistics data
"""
import datetime

import pandas
from django.conf import settings
from django.http import HttpRequest, HttpResponse
from django.utils import timezone

from core.api.auth import jwt_auth
from core.api.permissions import CORE_COURSE_VIEW
from core.api.utils import response_wrapper
from core.models.course import Course
from core.models.exam_record import (GRADE_F, ExamRecord)
from core.models.student_progress import StudentProgress
from judge.interface.problem_judge_record import (
    statistics_at_the_end_of_the_semester,
    statistics_distribution_at_the_end_of_the_semester)


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_VIEW])
def final_statistics(request: HttpRequest, course_id: int):
    """Final statistics at the end of the semester
    """
    course = Course.objects.get(pk=course_id)
    begin_at = datetime.datetime.combine(
        course.under_class_exam.date, datetime.datetime.min.time(), tzinfo=timezone.pytz.timezone(settings.TIME_ZONE))

    students = list(StudentProgress.objects.filter(course=course).values_list(
        "student__name", "student__student_id", "current_project__project__name"))
    pie_begin = {}
    for exam in course.exam_set.all():
        exam_date = exam.date
        for pie in exam.projectinexam_set.all():
            pie_begin.update({pie.id: datetime.datetime.combine(
                exam_date, pie.begin_time, tzinfo=timezone.pytz.timezone(settings.TIME_ZONE))})

    projects = [project["name"] for project in course.project_set.all().values("name")]

    data = statistics_at_the_end_of_the_semester(begin_at, students, pie_begin, projects)
    exam_record_all = ExamRecord.objects.filter(project_in_exam__project__course=course)
    for student in data:
        student_id = student["student_id"]
        exam_records = exam_record_all.filter(student__student_id=student_id).order_by("id")
        failed_cnt = exam_records.filter(check_result=GRADE_F).count()
        student["failed_cnt"] = failed_cnt
        longest_pass_cnt = 0
        for (i, _) in enumerate(exam_records):
            if exam_records[i].check_result != GRADE_F:
                cnt = 0
                for j in range(i, len(exam_records)):
                    if exam_records[j].check_result == GRADE_F:
                        i = j + 1
                        break
                    cnt += 1
                if cnt > longest_pass_cnt:
                    longest_pass_cnt = cnt
        student["longest_pass_cnt"] = longest_pass_cnt

    data_frame = pandas.DataFrame(data)
    return HttpResponse(data_frame.to_csv())


@response_wrapper
@jwt_auth(perms=[CORE_COURSE_VIEW])
def final_statistics_distribution(request: HttpRequest, course_id: int):
    """Final statistics at the end of the semester
    """
    course = Course.objects.get(pk=course_id)
    begin_at = datetime.datetime.combine(
        course.under_class_exam.date, datetime.datetime.min.time(), tzinfo=timezone.pytz.timezone(settings.TIME_ZONE))

    students = list(StudentProgress.objects.filter(course=course).values_list(
        "student__name", "student__student_id", "current_project__project__name"))
    data = statistics_distribution_at_the_end_of_the_semester(begin_at, students)
    data_frame = pandas.DataFrame(data)
    return HttpResponse(data_frame.to_csv())
