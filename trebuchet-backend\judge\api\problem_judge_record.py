"""
define problem judge record's view-layer functions
"""
from django.core.exceptions import FieldError, ValidationError
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from core.models.project_in_exam import ProjectInExam
from judge.api.permissions import (JUDGE_CHANGE_PJR, JUDGE_CREATE_PJR,
                                   JUDGE_DELETE_PJR, JUDGE_VIEW_PJR)
from judge.constants import FAILED, JUDGING
from judge.forms.judge_record import ProblemJudgeR<PERSON>ordInfo
from judge.models import (Problem, ProblemJudgeRecord, TestCaseJudgeRecord,
                          UserSubmittedFile)


def _validate_create_problem_judge_record(request: HttpRequest) -> bool:
    """
    validate create problem judge record
    Args:
        request:

    Returns:

    """
    fields = [field.name for field in ProblemJudgeRecord._meta.get_fields()]
    data: dict = parse_data(request)
    for key in data.keys():
        if key not in fields or key in ['started_at', 'submitted_at', 'finished_at']:
            return False
    info: ProblemJudgeRecordInfo = ProblemJudgeRecordInfo(data)
    if not info.is_valid():
        return False
    judge_result = data.get('judge_result', JUDGING)

    # better method?
    if judge_result < JUDGING or judge_result > FAILED:
        return False

    entity_missing = False
    query_id: int = data.get('problem', None)
    if query_id is not None:
        model = Problem.objects.filter(id=query_id)
        if not model.exists():
            entity_missing = True
    query_id: int = data.get('attachment', None)
    if query_id is not None:
        model = UserSubmittedFile.objects.filter(id=query_id)
        if not model.exists():
            entity_missing = True
    query_id: int = data.get('project_in_exam', None)
    if query_id is not None:
        model = ProjectInExam.objects.filter(id=query_id)
        if not model.exists():
            entity_missing = True

    if entity_missing:
        return False

    return True


def _validate_update_problem_judge_record(request: HttpRequest) -> bool:
    """
    validate update problem judge record
    Args:
        request:

    Returns:

    """
    fields = [field.name for field in ProblemJudgeRecord._meta.get_fields()]
    data: dict = parse_data(request)
    if data is None:
        return False
    for key in data.keys():
        if key not in fields:
            return False

    query_id = data.get('problem', None)
    if query_id is not None:
        if not Problem.objects.filter(id=query_id).exists():
            return False
    query_id = data.get('attachment', None)
    if query_id is not None:
        if not UserSubmittedFile.objects.filter(id=query_id).exists():
            return False
    query_id = data.get('project_in_exam', None)
    if query_id is not None:
        if not ProjectInExam.objects.filter(id=query_id).exists():
            return False
    return True


@response_wrapper
@jwt_auth(perms=[JUDGE_CREATE_PJR])
@validate_args(func=_validate_create_problem_judge_record)
@require_POST
def create_problem_judge_record(request: HttpRequest):
    """
    create problem judge record
    :param request:
    :return:
    """
    data: dict = parse_data(request)

    query_id = data.get('attachment', None)
    if query_id is not None:
        data['attachment'] = UserSubmittedFile.objects.get(id=query_id)

    query_id = data.get('problem', None)
    if query_id is not None:
        data['problem'] = Problem.objects.get(id=query_id)

    ProblemJudgeRecord.objects.create(**data)
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PJR])
@require_GET
@require_item_exist(model=ProblemJudgeRecord, field='id', item='id')
def get_problem_judge_record(request: HttpRequest, query_id: int) -> dict:
    """
    get problem judge record
    :param request:
    :param query_id:
    :return:
    """
    problem_judge_record = ProblemJudgeRecord.objects.get(**{'id': query_id})
    data = model_to_dict(problem_judge_record)
    data["problem_name"] = problem_judge_record.problem.name
    data["attachment_name"] = None if problem_judge_record.attachment is None \
        else problem_judge_record.attachment.filename
    data['test_case_judge_record'] = list(
        TestCaseJudgeRecord.objects.filter(
            problem_judge_record=problem_judge_record).values()
    )
    data["project_in_exam"] = None
    if problem_judge_record.project_in_exam is not None:
        data["project_in_exam"] = problem_judge_record.project_in_exam.id
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[JUDGE_CHANGE_PJR])
@require_http_methods(['PUT'])
@validate_args(func=_validate_update_problem_judge_record)
@require_item_exist(model=ProblemJudgeRecord, field='id', item='id')
def update_problem_judge_record(request: HttpRequest, query_id: int) -> dict:
    """
    update problem judge record
    :param request:
    :param query_id:
    :return:
    """
    data: dict = parse_data(request)
    query = ProblemJudgeRecord.objects.get(**{'id': query_id})

    query_id = data.get('problem', None)
    if query_id is not None:
        query.problem = Problem.objects.get(id=query_id)
        del data['problem']
    query_id = data.get('attachment', None)
    if query_id is not None:
        query.attachment = UserSubmittedFile.objects.get(id=query_id)
        del data['attachment']
    query_id = data.get('project_in_exam', None)
    if query_id is not None:
        query.project_in_exam = ProjectInExam.objects.get(id=query_id)
        del data['project_in_exam']
    try:
        for key in data.keys():
            if data[key] is None:
                continue
            setattr(query, key, data[key])
        query.save()
    except TypeError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
    except ValidationError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS)
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[JUDGE_DELETE_PJR])
@require_http_methods(['DELETE'])
@require_item_exist(model=ProblemJudgeRecord, field='id', item='id')
def delete_problem_judge_record(request: HttpRequest, query_id: int) -> dict:
    """
    delete problem judge record
    :param request:
    :param query_id:
    :return:
    """
    model = ProblemJudgeRecord.objects.get(**{'id': query_id})
    info = model_to_dict(model)
    model.delete()
    return success_api_response(info)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PJR])
@require_GET
@query_filter(fields=[("id", int), ("origin_id", int), ("course_code", str), ("edx_username", str),
                      ("chapter_code", str), ("judge_result",
                                              int), ("tool_chain", str),("problem__id", int),
                      ("problem__name", str), ("attachment__filename", str),
                      ("judger_identifier", str)])
@query_distinct(fields=["origin_id", "course_code", "edx_username",
                        "chapter_code", "judge_result"], model=ProblemJudgeRecord)
@query_order_by(fields=["origin_id", "course_code", "chapter_code",
                        "submitted_at", "started_at", "finished_at", "edx_username",
                        "problem__id", "problem__name", "attachment__filename"])
@query_page(default=10)
def list_problem_judge_records(request: HttpRequest, *args, **kwargs):
    """
    list problem judge records
    :param request:
    :param args:
    :param kwargs:
    :return:
    """
    models_all = ProblemJudgeRecord.objects.count()
    models: QuerySet = ProblemJudgeRecord.objects.all()
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")

    # order by
    order_by = kwargs.get('order_by')
    if order_by is not None:
        models = models.order_by(*order_by)
    else:
        models = models.order_by('-id')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'origin_id', 'edx_username', 'problem__id', 'problem__name', 'attachment__filename',
                'attachment__id', 'student_comment', 'judger_identifier', 'tool_chain',
                'judge_result', 'submitted_at', 'started_at', 'finished_at'
            )
        )
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


PROBLEM_JUDGE_RECORD_SET_API = wrapped_api({
    "post": create_problem_judge_record,
    "get": list_problem_judge_records,
})

PROBLEM_JUDGE_RECORD_DETAIL_API = wrapped_api({
    "get": get_problem_judge_record,
    "put": update_problem_judge_record,
    "delete": delete_problem_judge_record,
})
