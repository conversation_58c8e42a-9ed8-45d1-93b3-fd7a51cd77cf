<template>
  <Row>
    <i-col span="8" offset="8">
      <Card>
        <i-form ref="passwordUpdate" :model="user" :rules="userRule" :label-width="100">
          <form-item prop="oldPassword" label="旧密码">
            <Input v-model="user.oldPassword" type="password" />
          </form-item>
          <form-item prop="newPasswordAlpha" label="新密码">
            <Input v-model="user.newPasswordAlpha" type="password" />
          </form-item>
          <form-item prop="newPasswordBeta" label="再次输入新密码">
            <Input v-model="user.newPasswordBeta" type="password" />
          </form-item>
          <form-item>
            <Button type="primary" @click="handleSubmit()" style="margin-right: 20px">确认修改</Button>
            <Button type="primary" @click="$router.go(-1)">取消修改</Button>
          </form-item>
        </i-form>
      </Card>
    </i-col>
  </Row>
</template>

<script>
import { updatePassword } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'PasswordUpdate',
  data() {
    return {
      user: {
        oldPassword: null,
        newPasswordAlpha: null,
        newPasswordBeta: null
      },
      userRule: {
        oldPassword: [{ required: true, message: '请填写旧密码', trigger: 'blur' }],
        newPasswordAlpha: [
          { required: true, message: '请填写密码', trigger: 'blur' },
          { min: 6, message: '请至少输入6位密码' }
        ],
        newPasswordBeta: [
          { validator: this.validatePassWord, trigger: 'blur' },
          { min: 6, message: '请至少输入6位密码' }
        ]
      }
    }
  },
  methods: {
    validatePassWord(rule, value, callback) {
      if (value === '') {
        return callback(new Error('请再次输入密码'))
      } else if (value !== this.user.newPasswordAlpha) {
        return callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    },
    handleSubmit() {
      this.$refs.passwordUpdate.validate((valid) => {
        if (valid) {
          updatePassword({
            'old-password': this.user.oldPassword,
            'new-password': this.user.newPasswordAlpha
          })
            .then(() => {
              this.$Notice.success({ title: '修改成功' })
              this.$router.push({ name: 'home' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    }
  }
}
</script>
