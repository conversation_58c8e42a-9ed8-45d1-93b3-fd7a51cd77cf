import { getRequest } from '@/api/util'

// 进度慢检测
export const slowDetectionReq = (courseId, params) => {
  return getRequest(`/api/progress/slow-detection/${courseId}`, 'get', params)
}

// 考试通过人数检测及无人通过警报
export const examPassDetectionReq = (examId, params) => {
  return getRequest(`/api/progress/exam-pass-detection/${examId}`, 'get', params)
}

// 多次挂在同一个P检测
export const repeatedFailuresReq = (courseId, params) => {
  return getRequest(`/api/progress/repeated-failures/${courseId}`, 'get', params)
}

// 多次没有课上资格检测
export const qualificationFailuresReq = (courseId, params) => {
  return getRequest(`/api/progress/qualification-failures/${courseId}`, 'get', params)
}

// 获取当前课程ID（从用户配置中获取）
export const getCurrentCourseId = () => {
  return getRequest('/api/user-profile', 'get')
}
