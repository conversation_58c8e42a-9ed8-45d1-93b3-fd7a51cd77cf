"""Seat APIs
"""

from django.db.models import Q
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_SEAT_CHANGE, CORE_SEAT_CREATE,
                                  CORE_SEAT_DELETE, CORE_SEAT_VIEW)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)
from core.forms.seat import SeatInfo
from core.models.room import Room
from core.models.seat import Seat


def validate_seat_data(data: dict, room_id=1) -> bool:
    """validate query seat data

    Args:
        data (dict): parsed seat data
        room_id (int, optional): placeholder. Defaults to 1.

    Returns:
        bool: indicate if the seat data is valid
    """
    data.update({"room_id": room_id})
    info = SeatInfo(data)
    if not info.is_valid():
        return False
    pos_x = data.get("pos_x")
    pos_y = data.get("pos_y")
    if pos_x <= 0 or pos_y <= 0:
        return False
    return True


def validate_seat_post_request(request: HttpRequest) -> bool:
    """validate seat post request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"room_id", "name",
                      "pos_x", "pos_y", "available", "comment"}
    if not data.keys() <= allowed_fields:
        return False

    return validate_seat_data(data, data.get("room_id"))


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_CREATE])
@require_POST
@validate_args(func=validate_seat_post_request)
def create_seat(request):
    """Deal with seat post request

    [route]: /api/seats

    [method]: POST
    """
    data: dict = parse_data(request)
    room_id, name, pos_x, pos_y = data.get("room_id"), data.get(
        "name"), data.get("pos_x"), data.get("pos_y")
    room = Room.objects.get(**{"id": room_id})
    # name | (pos_x & pos_y)
    query_same_name = Q(name=name)
    query_same_pos = Q(pos_x=pos_x) & Q(pos_y=pos_y)
    query = query_same_name | query_same_pos
    if room.seat_set.filter(query).exists():
        return failed_api_response(ErrorCode.ITEM_ALREADY_EXISTS, "Sorry, the seat is duplicated.")

    seat = Seat(**data)
    seat.room = room
    seat.save()
    return success_api_response({"result": "Ok, Seat Created."})


def validate_seat_put_request(request: HttpRequest) -> bool:
    """validate seat put request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    allowed_fields = {"name", "pos_x", "pos_y", "available", "comment"}
    if not data.keys() <= allowed_fields:
        return False
    # placeholders, have nothing to do with real data
    data.setdefault("pos_x", 1)
    data.setdefault("pos_y", 1)
    return validate_seat_data(data)


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_CHANGE])
@require_http_methods(["PUT"])
@validate_args(func=validate_seat_put_request)
@require_item_exist(model=Seat, field="id", item="id")
def update_seat(request, seat_id: int):
    """Deal with put request

    same as PATCH

    [route]: /api/seats/:id

    [method]: PUT
    """
    data = parse_data(request)
    seat = Seat.objects.get(**{"id": seat_id})
    name, pos_x, pos_y = data.get("name"), data.get("pos_x"), data.get("pos_y")

    query_others = ~Q(id=seat_id)
    query_same_name = Q(name=name)
    query_same_pos = Q(pos_x=pos_x) & Q(pos_y=pos_y)
    query = query_others & (query_same_name | query_same_pos)
    if seat.room.seat_set.filter(query).exists():
        return failed_api_response(ErrorCode.ITEM_ALREADY_EXISTS, "Sorry, seat put request causes conflict.")

    for key in data.keys():
        setattr(seat, key, data.get(key))
    seat.save()

    return success_api_response({"result": "Ok, Seat Information Updated."})


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_VIEW])
@require_GET
@require_item_exist(model=Seat, field="id", item="id")
def retrieve_seat_detail(request, seat_id):
    """Deal with seat get request

    [route]: /api/seats/:id

    [method]: GET
    """
    seat = Seat.objects.get(**{"id": seat_id})
    seat_detail = model_to_dict(seat, exclude="room")
    return success_api_response(seat_detail)


@response_wrapper
@jwt_auth(perms=[CORE_SEAT_DELETE])
@require_http_methods(["DELETE"])
@require_item_exist(model=Seat, field="id", item="id")
def remove_seat(request, seat_id):
    """Deal with seat request

    [route]: /api/seats/:id

    [method]: DELETE
    """
    Seat.objects.filter(**{"id": seat_id}).delete()
    return success_api_response({"result": "Ok, Seat Deleted."})


SEAT_DETAIL_API = wrapped_api({
    "get": retrieve_seat_detail,
    "put": update_seat,
    "delete": remove_seat
})

SEAT_SET_API = wrapped_api({
    "post": create_seat
})
