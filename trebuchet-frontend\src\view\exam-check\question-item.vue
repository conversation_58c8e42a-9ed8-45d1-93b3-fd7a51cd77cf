<template>
  <div>
    <Form>
      <form-item v-for="([key, value], index) in Object.entries(questionDict)" :key="key">
        <Row>
          <Col span="10">
            {{ key }}
          </Col>
          <Col span="14">
            <template v-if="value == 1">
              <Input v-model="innerRecord[index]" @on-change="changeRecord(key, innerRecord[index])" :rows="4" />
            </template>
            <template v-else>
              <radio-group v-model="innerRecord[index]" @on-change="changeRecord(key, $event)">
                <radio label="A" />
                <radio label="B" />
                <radio label="C" />
                <radio label="F" />
                <radio label="无" />
              </radio-group>
            </template>
          </Col>
        </Row>
      </form-item>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'QuestionItem',
  props: {
    questionDict: {
      type: Object,
      default: () => ({})
    },
    record: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      innerRecord: []
    }
  },
  beforeMount() {
    Object.keys(this.questionDict).forEach((key) => {
      let tmp = this.record.find((item) => {
        return item.q === key
      })
      this.innerRecord.push(tmp ? tmp.a : '无')
    })
  },
  methods: {
    changeRecord(key, value) {
      let question = key
      let aRecordIndex = this.record.findIndex((item) => {
        return item.q === question
      })
      const next = this.record
      if (aRecordIndex === -1) {
        let newRecord = { q: question, a: '' }
        newRecord.a = value
        next.push(newRecord)
        return undefined
      }
      let aRecord = this.record[aRecordIndex]
      if (value === '无') {
        next.splice(aRecordIndex, 1)
        return undefined
      }
      aRecord.a = value
    }
  }
}
</script>
