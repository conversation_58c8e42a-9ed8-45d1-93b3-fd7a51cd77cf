"""
declare Class model
"""

from django.db import models

from core.models.course import Course
from core.models.permissions import (CLASS_CHANGE, CLASS_CREATE, CLASS_DELETE,
                                     CLASS_VIEW)

from .student import Student


class InstructorClass(models.Model):
    """This model describes an administrative class.

    此模型用于描述教务选课班级。

    Class is an organizational entity.
    For example, "2018年秋高小鹏老师班" conveys two messages: the class belongs to
        the course named "2018年秋", and the corresponding teacher is "高小鹏老师".

    Class 是一个组织上的实体。
    譬如, "2018年秋高小鹏老师班" 主要有两层意思: 该班是隶属于 "2018年秋" 课程的，以及其
        对应的老师是 "高小鹏老师"。

    Attributes:
        name: A CharField storing the name of class.
        teacher: A CharField storing the teacher's name.
        student: A ManyToManyField which represents the students of the class.
        belong_to: A Database ForeignKey to Course, to which the class belongs.

    属性:
        name: <PERSON><PERSON><PERSON><PERSON>, 用于存储班级名称
        teacher: <PERSON><PERSON><PERSON><PERSON>, 用于存储班级的教师名称
        student: <PERSON>ToManyField, 用于指向属于该班级的学生
        belong_to: ForeignKey, 用于指向该班级所属的课程
    """
    name = models.CharField(max_length=100)
    teacher = models.CharField(max_length=100)
    student = models.ManyToManyField(to=Student)
    belong_to = models.ForeignKey(to=Course, on_delete=models.SET_NULL, null=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (CLASS_CHANGE, CLASS_CHANGE),
            (CLASS_CREATE, CLASS_CREATE),
            (CLASS_DELETE, CLASS_DELETE),
            (CLASS_VIEW, CLASS_VIEW)
        ]
