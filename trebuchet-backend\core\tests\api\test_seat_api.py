"""Seat APIs test

| method  | path                | description                | Success Code | Success Response       |
| POST    | /api/rooms          | create a room              | 200          | Success Message        |
| POST    | /api/seats          | append a seat for the room | 200          | Success Message        |
| PUT     | /api/rooms/:id      | modify room information    | 200          | Success Message        |
| PUT     | /api/seats/:id      | modify seat information    | 200          | Success Message        |
| GET     | /api/rooms          | get all room information   | 200          | room information array |
| GET     | /api/rooms/:id      | get all seat information   | 200          | room information       |
| GET     | /api/seats/:id      | get a seat information     | 200          | seat information       |
| DELETE  | /api/rooms/:id      | delete a room              | 200          | Success Message        |
| DELETE  | /api/seats/:id      | delete a seat              | 200          | Success Message        |
"""

import json

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.test import Client, TestCase

from core.api.utils import ErrorCode
from core.models.room import Room
from core.models.seat import Seat

BASE_ROOM_URL = "http://localhost:8000/api/rooms"
BASE_SEAT_URL = "http://localhost:8000/api/seats"
USER_MODEL = get_user_model()
CLIENT = Client()


def retrieve_seat_detail(seat_id):
    """send request to retrieve seat detail

    [method]: get

    example
    {
        "id": 1,
        "name": "test seat",
        "pos_x": 1,
        "pos_y": 1,
        "available": true,
        "comment": "test seat"
    }
    """
    return CLIENT.get(BASE_SEAT_URL + "/" + str(seat_id))


def retrieve_room_detail(room_id):
    """send request to retrieve room detail

    [method]: get

    example
    {
        "id" : 1,
        "name" : "G849",
        "available" : true,
        "seats_all" : 50,
        "seats_available" : 40,
        "seats" : [
            {
            "id" : 1,
            "name" : "test seat",
            "pos_x" : 1,
            "pos_y" : 1,
            "available" : true,
            "comment" : "test seat"
            },
            ...
        ]
    }
    """
    return CLIENT.get(BASE_ROOM_URL + "/" + str(room_id))


def create_seat(data):
    """send request to create seat

    [method]: post

    """
    return CLIENT.post(BASE_SEAT_URL, json.dumps(data), content_type="application/json")


def update_seat(seat_id, data):
    """send request to update seat

    [method]: put

    """
    return CLIENT.put(BASE_SEAT_URL + "/" + str(seat_id), json.dumps(data), content_type="application/json")


def remove_seat(seat_id):
    """send request to remove seat

    [method]: delete

    """
    return CLIENT.delete(BASE_SEAT_URL + "/" + str(seat_id))


def clean_up():
    """clean up db
    """
    Room.objects.all().delete()
    Seat.objects.all().delete()


def build_seat_response(seat_id, name, pos_x, pos_y, available, comment):
    """build seat response
    """
    return {
        "id": seat_id,
        "name": name,
        "pos_x": pos_x,
        "pos_y": pos_y,
        "available": available,
        "comment": comment
    }


def build_post_data(room_id, name, pos_x, pos_y, available=None, comment=None):
    """build post data
    """
    return_value = {
        "room_id": room_id,
        "name": name,
        "pos_x": pos_x,
        "pos_y": pos_y,
    }
    if not available is None:
        return_value.update({"available": available})
    if not comment is None:
        return_value.update({"comment": comment})
    return return_value


class TestSeatAPI(TestCase):
    """TestCase class for seat api
    """

    def assert_code(self, code: int, response):
        """assert status code
        """
        try:
            self.assertEqual(code, response.status_code)
        except AssertionError as err:
            print("Expected status code: {}, but actual status code: {}".format(
                code, response.status_code))
            raise err

    def build_seat_dict(self, name: str, pos_x: int, pos_y: int, available: bool = None, comment: str = None):
        """build seat dict data
        """
        idx = self.idx
        self.idx = self.idx + 1
        retval = {
            "id": idx,
            "name": name,
            "pos_x": pos_x,
            "pos_y": pos_y
        }
        if not available is None:
            retval.update({"available": available})
        if not comment is None:
            retval.update({"comment": comment})
        return retval

    def setUp(self):
        """initialize
        """
        self.idx = 1
        room = Room.objects.create(**{
            "id": 1,
            "name": "Test Room",
            "available": True,
            "comment": "Test room in Seat"
        })
        seat1 = Seat(**(self.build_seat_dict("1", 1, 1)))
        seat2 = Seat(**(self.build_seat_dict("2", 2, 2, False)))
        seat3 = Seat(**(self.build_seat_dict("3", 3, 3, True, "Oh")))
        seat1.room = room
        seat2.room = room
        seat3.room = room
        seat1.save()
        seat2.save()
        seat3.save()
        user = USER_MODEL.objects.create_user(
            username="test", password="12345")
        permission_1 = Permission.objects.get(codename="view_seat")
        permission_2 = Permission.objects.get(codename="add_seat")
        permission_3 = Permission.objects.get(codename="change_seat")
        permission_4 = Permission.objects.get(codename="delete_seat")
        permission_5 = Permission.objects.get(codename="view_room")
        permission_6 = Permission.objects.get(codename="add_room")
        user.user_permissions.add(permission_1)
        user.user_permissions.add(permission_2)
        user.user_permissions.add(permission_3)
        user.user_permissions.add(permission_4)
        user.user_permissions.add(permission_5)
        user.user_permissions.add(permission_6)
        auth_token = CLIENT.post(
            "http://localhost:8000/api/token-auth",
            {"username": "test", "password": "12345"}).json().get("access_token")
        CLIENT.defaults["HTTP_AUTHORIZATION"] = "Bearer" + " " + auth_token

    def test_all(self):
        """test all cases
        """
        self.retrieve_seat_detail_success()
        self.retrieve_seat_detail_fail()
        self.retrieve_room_detail_success()
        self.create_seat_success()
        self.create_seat_fail()
        self.update_seat_success()
        self.update_seat_fail()
        self.remove_seat_success()
        self.remove_seat_fail()
        clean_up()

    def retrieve_seat_detail_success(self):
        """retrieve seat detail test, valid request
        """
        response = retrieve_seat_detail(1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(data,
                             build_seat_response(1, "1", 1, 1, True, ""))

    def retrieve_seat_detail_fail(self):
        """retrieve seat detail test, invalid request
        """
        response = retrieve_seat_detail(114514)
        self.assert_code(ErrorCode.ITEM_NOT_FOUND.value, response)
        data = response.json()
        self.assertIsNotNone(data.get("error_msg"))

    def retrieve_room_detail_success(self):
        """retrieve room detail test, valid request
        now with details of seats
        """
        response = retrieve_room_detail(1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(data, {
            "id": 1,
            "name": "Test Room",
            "available": True,
            "comment": "Test room in Seat",
            "seats_all": 3,
            "seats_available": 2,
            "seats": [
                build_seat_response(1, "1", 1, 1, True, ""),
                build_seat_response(2, "2", 2, 2, False, ""),
                build_seat_response(3, "3", 3, 3, True, "Oh")]
        })

    def create_seat_success(self):
        """create seat test, valid request
        """
        post_data_1 = build_post_data(1, "4", 4, 4)
        post_data_2 = build_post_data(1, "5", 5, 5, False)
        post_data_3 = build_post_data(1, "6", 6, 6, True, "emm")
        response = create_seat(post_data_1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = create_seat(post_data_2)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = create_seat(post_data_3)
        self.assert_code(ErrorCode.SUCCESS.value, response)

        response = retrieve_room_detail(1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(data, {
            "id": 1,
            "name": "Test Room",
            "available": True,
            "comment": "Test room in Seat",
            "seats_all": 6,
            "seats_available": 4,
            "seats": [
                build_seat_response(1, "1", 1, 1, True, ""),
                build_seat_response(2, "2", 2, 2, False, ""),
                build_seat_response(3, "3", 3, 3, True, "Oh"),
                build_seat_response(4, "4", 4, 4, True, ""),
                build_seat_response(5, "5", 5, 5, False, ""),
                build_seat_response(6, "6", 6, 6, True, "emm")]
        })

    def create_seat_fail(self):
        """create seat test, invalid request
        """
        post_data_5 = build_post_data(1, "4", 4, 4)
        response = create_seat(post_data_5)
        self.assert_code(ErrorCode.ITEM_ALREADY_EXISTS.value, response)
        post_data_6 = build_post_data(1, "7", 7, 7)
        post_data_6.update({"wrong": "oh"})
        response = create_seat(post_data_6)
        self.assert_code(ErrorCode.INVALID_REQUEST_ARGS.value, response)

    def update_seat_success(self):
        """update seat test, valid request
        """
        put_data_1 = build_post_data(1, "114514", 2, 4, False, "시험")
        put_data_1.pop("room_id")
        response = update_seat(1, put_data_1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = retrieve_room_detail(1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(data, {
            "id": 1,
            "name": "Test Room",
            "available": True,
            "comment": "Test room in Seat",
            "seats_all": 6,
            "seats_available": 3,
            "seats": [
                build_seat_response(1, "114514", 2, 4, False, "시험"),
                build_seat_response(2, "2", 2, 2, False, ""),
                build_seat_response(3, "3", 3, 3, True, "Oh"),
                build_seat_response(4, "4", 4, 4, True, ""),
                build_seat_response(5, "5", 5, 5, False, ""),
                build_seat_response(6, "6", 6, 6, True, "emm")]
        })

    def update_seat_fail(self):
        """update seat test, invalid request
        """
        put_data_2 = build_post_data(1, "114514", 2, 2)
        put_data_2.pop("room_id")
        response = update_seat(1, put_data_2)
        self.assert_code(ErrorCode.ITEM_ALREADY_EXISTS.value, response)
        put_data_3 = build_post_data(1, "114514", 2, 4)
        put_data_3.pop("room_id")
        put_data_3.update({"wrong": "Oh"})
        response = update_seat(1, put_data_3)
        self.assert_code(ErrorCode.INVALID_REQUEST_ARGS.value, response)

    def remove_seat_success(self):
        """remove seat test, valid request
        """
        response = remove_seat(1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = retrieve_seat_detail(1)
        self.assert_code(ErrorCode.ITEM_NOT_FOUND.value, response)
        response = retrieve_room_detail(1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(data, {
            "id": 1,
            "name": "Test Room",
            "available": True,
            "comment": "Test room in Seat",
            "seats_all": 5,
            "seats_available": 3,
            "seats": [
                build_seat_response(2, "2", 2, 2, False, ""),
                build_seat_response(3, "3", 3, 3, True, "Oh"),
                build_seat_response(4, "4", 4, 4, True, ""),
                build_seat_response(5, "5", 5, 5, False, ""),
                build_seat_response(6, "6", 6, 6, True, "emm")]
        })

    def remove_seat_fail(self):
        """remove seat test, invalid request
        """
        response = remove_seat(114514)
        self.assert_code(ErrorCode.ITEM_NOT_FOUND.value, response)
