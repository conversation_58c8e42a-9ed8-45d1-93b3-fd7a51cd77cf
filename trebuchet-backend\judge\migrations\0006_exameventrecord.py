# Generated by Django 2.2.6 on 2020-11-13 11:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0017_auto_20201106_2001'),
        ('judge', '0005_rejudgedproblemjudgerecord_batch_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExamEventRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('happened_at', models.DateTimeField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                 to=settings.AUTH_USER_MODEL)),
                ('project_in_exam',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.ProjectInExam')),
            ],
            options={
                'permissions': [('查看考试通过情况', '查看考试通过情况')],
                'default_permissions': (),
            },
        ),
    ]
