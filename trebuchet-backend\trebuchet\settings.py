import os
import yaml

with open("config.yaml", 'r') as stream:
    _YAML_CONFIG = yaml.safe_load(stream)

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = _YAML_CONFIG["DjangoSecretKey"]

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = _YAML_CONFIG['Debug']

ALLOWED_HOSTS = ['*']
HOSTNAME = ALLOWED_HOSTS[0]

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'django_nose',
    'core',
    'judge',
    'discussion',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.middleware.gzip.GZipMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

CORS_ALLOW_ALL_ORIGINS = True
CORS_EXPOSE_HEADERS = ['Content-Disposition', 'Date']

TEST_RUNNER = 'django_nose.NoseTestSuiteRunner'

ROOT_URLCONF = 'trebuchet.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'trebuchet.wsgi.application'

# Database
# https://docs.djangoproject.com/en/2.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': _YAML_CONFIG['DatabaseName'],
        'USER': _YAML_CONFIG['DatabaseUser'],
        'PASSWORD': _YAML_CONFIG['DatabasePassword'],
        'HOST': _YAML_CONFIG['DatabaseHost'],
        'PORT': _YAML_CONFIG['DatabasePort'],
    }
}

# Password validation
# https://docs.djangoproject.com/en/2.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.2/howto/static-files/

STATIC_URL = '/static/'

# CSCore management

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://{}/{}".format(_YAML_CONFIG["RedisAddress"], _YAML_CONFIG["RedisDatabase"]),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": _YAML_CONFIG["RedisPassword"]
        }
    }
}

S3_SECRET_ID = _YAML_CONFIG['S3SecretId']
S3_SECRET_KEY = _YAML_CONFIG['S3SecretKey']
S3_BUCKET_NAMES = [    # corresponding to the order defined in user_submitted_file.py
    _YAML_CONFIG['S3BucketSubmission'],
    _YAML_CONFIG['S3BucketImage'],
    _YAML_CONFIG['S3BucketJudgeData'],
    _YAML_CONFIG['S3BucketProblemData']
]
S3_REGION = _YAML_CONFIG['S3Region']
S3_ADDRESS = _YAML_CONFIG['S3Address']
S3_SSL = _YAML_CONFIG['S3UseSSL']

MAIL_FORMAT = '{}@buaa.edu.cn'

MAIL_SMTP_USERNAME = _YAML_CONFIG['SmtpUsername']
MAIL_SMTP_FROM = _YAML_CONFIG['SmtpFrom']
MAIL_SMTP_PASSWORD = _YAML_CONFIG['SmtpPassword']
MAIL_SMTP_HOST = _YAML_CONFIG['SmtpHost']
MAIL_SMTP_PORT = _YAML_CONFIG['SmtpPort']

GIT_TOKEN = _YAML_CONFIG["GitToken"]
