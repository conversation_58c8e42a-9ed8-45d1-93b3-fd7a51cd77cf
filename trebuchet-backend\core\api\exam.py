"""Exam management APIs
"""
from django.core.exceptions import FieldError
from django.core.paginator import Paginator
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)
from django.db.models import Q

from core.api.auth import jwt_auth
from core.api.exam_queue import _get_course_current_exam
from core.api.permissions import (CORE_EXAM_CHANGE, CORE_EXAM_CREATE,
                                  CORE_EXAM_DELETE, CORE_EXAM_VIEW)
from core.api.query_utils import query_filter, query_order_by, query_page
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api, require_course_permission)
from core.forms.exam import ExamInfo
from core.models.exam import Exam
from core.models.exam_record import ExamRecord
from core.models.user_profile import UserProfile
from core.models.course import Course


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_VIEW])
@require_GET
@require_course_permission
@query_filter(fields=[("name", str), ("active", bool)])
@query_order_by(fields=["date", "active"])
@query_page()
def list_exams(request: HttpRequest, course: Course, *args, **kwargs):
    """List all exams which meet the need

    [route]: /api/exams

    [method]: GET
    """
    exams_all = Exam.objects.all()
    exams_all_count = len(exams_all)
    # filter
    exam_filter = kwargs.get("filter")
    exam_filter &= Q(course_id=course.pk)
    try:
        exams = exams_all.filter(exam_filter)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    # order_by
    exam_order_by = kwargs.get("order_by")
    if exam_order_by is not None:
        exams = exams.order_by(*exam_order_by)
    else:
        exams = exams.order_by("date", "-active")
    # page
    page = kwargs.get("page")
    page_size = kwargs.get("page_size")
    paginator = Paginator(exams, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        exams_details = []
    else:
        exams_details = list(paginator.get_page(page)
                             .object_list.values("id", "date", "active", "course"))
    exam_list_data = {
        "exam_all": exams_all_count,
        "total_count": paginator.count,
        "page_all": page_all,
        "page_now": page,
        "exams": exams_details
    }
    return success_api_response(exam_list_data)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_VIEW])
@require_GET
@require_item_exist(model=Exam, field="id", item="exam_id")
def retrieve_exam_detail(request: HttpRequest, exam_id: int):
    """Show details of the specified exam

    [route]: /api/exams/<int:exam_id>

    [method]: GET
    """
    exam = Exam.objects.get(pk=exam_id)
    projects = exam.project.all()
    projects_details = list(projects.values("id", "name"))
    exam_detail = {
        "id": exam.id,
        "date": exam.date,
        "active": exam.active,
        "projects": projects_details
    }
    return success_api_response(exam_detail)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_DELETE])
@require_http_methods(["DELETE"])
@require_item_exist(model=Exam, field="id", item="exam_id")
def remove_exam(request: HttpRequest, exam_id: int):
    """Remove the specified exam from db

    [route]: /api/exams/<int:exam_id>

    [method]: DELETE
    """
    Exam.objects.filter(pk=exam_id).delete()
    return success_api_response({"result": "Ok, Exam Deleted."})


def validate_exam_request(request: HttpRequest) -> bool:
    """Validate incoming exam data
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"date", "active"}
    if not data.keys() <= allowed_fields:
        return False
    # check exam
    data.pop("projects", None)
    exam_info = ExamInfo(data)
    if not exam_info.is_valid():
        return False
    return True


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_CREATE])
@require_POST
@validate_args(func=validate_exam_request)
def create_exam(request: HttpRequest):
    """Create a new exam from incoming json data

    [route]: /api/exams

    [method]: POST
    """
    data: dict = parse_data(request)
    data.pop("projects", None)
    if data.get("active") is None:
        data["active"] = False
    current_user_profile = UserProfile.objects.filter(
        user=request.user).first()
    if current_user_profile and current_user_profile.course:
        data['course'] = current_user_profile.course
    else:
        return failed_api_response({'result': 'Failed to get current course from user profile.'})
    exam: Exam = Exam.objects.create(**data)
    return success_api_response({"id": exam.id})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_CHANGE])
@require_http_methods(["PUT"])
@validate_args(func=validate_exam_request)
@require_item_exist(model=Exam, field="id", item="exam_id")
def update_exam(request: HttpRequest, exam_id: int):
    """Update the specified exam with incoming json data

    [route]: /api/exams/<int:exam_id>

    [method]: PUT
    """
    data: dict = parse_data(request)
    exam = Exam.objects.get(pk=exam_id)
    data.pop("projects", None)
    if data.get("active"):
        course_id = UserProfile.objects.filter(user=request.user).first().course_id
        active_exam = _get_course_current_exam(course_id)
        if active_exam and active_exam != exam:
            active_exam.active = False
            active_exam.save()
    for key in data.keys():
        setattr(exam, key, data.get(key))
    exam.save()
    return success_api_response({"result": "Ok, Exam Updated."})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_VIEW])
@require_GET
@require_item_exist(model=Exam, field="id", item="exam_id")
def retrieve_students_in_exam(request: HttpRequest, exam_id: int):
    """Retrieve all students in specified exam

    [route]: /api/exams/<int:exam_id>/students

    [method]: GET
    """
    exam = Exam.objects.get(pk=exam_id)
    exam_records = ExamRecord.objects.filter(project_in_exam__exam=exam).distinct()
    students = list(exam_records.values(
        "student__id", "student__student_id", "student__name", "project_in_exam__project__name"))
    data = {
        "students": students
    }
    return success_api_response(data)


EXAM_SET_API = wrapped_api({
    "get": list_exams,
    "post": create_exam
})

EXAM_DETAIL_API = wrapped_api({
    "get": retrieve_exam_detail,
    "delete": remove_exam,
    "put": update_exam
})
