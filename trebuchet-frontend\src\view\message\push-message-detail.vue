<template>
  <Row>
    <Col span="16" offset="4">
      <Card dis-hover class="push-message-form-container">
        <div>
          <Form ref="pushMsgForm" :model="pushMsgInfo" :rules="ruleValidate" :label-width="80">
            <FormItem label="标题" prop="title">
              <Input v-model="pushMsgInfo.title" placeholder="请输入推送信息的标题" size="large" :disabled="!ready" />
            </FormItem>
            <FormItem label="内容" prop="content">
              <Input
                v-model="pushMsgInfo.content"
                type="textarea"
                placeholder="请输入推送信息的内容"
                :disabled="!ready"
                :rows="5"
              />
            </FormItem>
            <FormItem label="备注" prop="comment">
              <Input v-model="pushMsgInfo.comment" placeholder="请在此添加备注" size="large" :disabled="!ready" />
            </FormItem>
            <FormItem label="网站推送" prop="with_web_notification">
              <i-switch
                v-model="pushMsgInfo.with_web_notification"
                size="large"
                :disabled="!ready"
                @on-change="pushMethodChanged"
              >
                <span slot="open">On</span>
                <span slot="close">Off</span>
              </i-switch>
            </FormItem>
            <FormItem label="邮件推送" prop="with_email">
              <i-switch v-model="pushMsgInfo.with_email" size="large" :disabled="!ready" @on-change="pushMethodChanged">
                <span slot="open">On</span>
                <span slot="close">Off</span>
              </i-switch>
            </FormItem>
            <FormItem label="推送目标" prop="recipients_str">
              <Input
                v-model="pushMsgInfo.recipients_str"
                type="textarea"
                placeholder="请输入推送信息接收者的学号 以英文逗号分割"
                :disabled="!ready || loadingStudents"
                :rows="4"
              />
              <div class="select-all-button">
                <Button
                  icon="ios-download-outline"
                  :disabled="!ready"
                  :loading="loadingStudents"
                  @click="loadStudentsData"
                >
                  选中本课程所有学生
                </Button>
              </div>
            </FormItem>
            <FormItem>
              <Button type="primary" :disabled="!ready || loadingStudents" @click="submit">
                {{ operationString }}
              </Button>
            </FormItem>
          </Form>
        </div>
      </Card>
    </Col>
  </Row>
</template>

<script>
import { studentListReq } from '@/api/student'
import { pushMsgDetailReq, pushMsgReq } from '@/api/push-message'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'PushMessageDetail',
  data() {
    const validatePushMethod = (rule, value, callback) => {
      if (!(this.pushMsgInfo.with_email || this.pushMsgInfo.with_web_notification)) {
        callback(new Error('至少选中一种推送方式'))
      } else {
        callback()
      }
    }

    return {
      validatePushMethod: validatePushMethod,
      pushMsgInfo: {
        message_type: 1,
        title: '',
        content: '',
        comment: '',
        with_email: false,
        with_web_notification: true,
        recipients_str: ''
      },
      ruleValidate: {
        title: [
          { type: 'string', whitespace: true, required: true, message: '请输入标题', trigger: 'change' },
          { type: 'string', max: 50, message: '标题不能超过50字', trigger: 'change' }
        ],
        comment: [{ type: 'string', max: 150, message: '注释不能超过150字', trigger: 'change' }],
        content: [{ required: true, whitespace: true, message: '请输入内容', trigger: 'change' }],
        with_web_notification: [
          { type: 'boolean', required: true, message: '需要选择是否使用课程网站推送', trigger: 'change' },
          { validator: validatePushMethod, trigger: 'change' }
        ],
        with_email: [
          { type: 'boolean', required: true, message: '需要选择是否使用邮件推送', trigger: 'change' },
          { validator: validatePushMethod, trigger: 'change' }
        ],
        recipients_str: [{ required: true, whitespace: true, message: '请输入要通知的学生学号', trigger: 'change' }]
      },
      ready: false,
      loadingStudents: false
    }
  },
  computed: {
    modify() {
      return this.$route.params.id !== undefined
    },
    operationString() {
      return this.modify ? '修改' : '创建'
    }
  },
  mounted() {
    if (this.modify) this.loadData()
    else this.ready = true
  },
  methods: {
    async loadData() {
      this.ready = false
      try {
        const res = await pushMsgDetailReq('get', this.$route.params.id, {})
        this.pushMsgInfo = res.data
        this.ready = this.pushMsgInfo.status === 0
        if (!this.ready) {
          this.$Notice.warning({ title: '无法修改已经推送过的信息' })
        }

        let recipient_list = []
        this.pushMsgInfo['recipients_status'].forEach((i) => {
          recipient_list = recipient_list.concat(i['student__student_id'])
        })
        this.pushMsgInfo.recipients_str = recipient_list.join(',')
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    async loadStudentsData() {
      try {
        this.loadingStudents = true
        const preReqRes = await studentListReq('get')
        const page_size = preReqRes.data['total_count']
        const res = await studentListReq('get', { page: 1, page_size: page_size })
        const student_ids = res.data.students.map((item) => item.student_id)
        this.pushMsgInfo.recipients_str = student_ids.join(',')
        this.loadingStudents = false
      } catch (error) {
        this.loadingStudents = false
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    async submit() {
      const valid = await this.$refs['pushMsgForm'].validate()
      if (!valid) {
        this.$Notice.warning({ title: '表单验证失败' })
        return
      }
      const recipient_list = this.pushMsgInfo.recipients_str.trim().split(',')
      const real_data = {
        message_type: this.pushMsgInfo.message_type,
        title: this.pushMsgInfo.title,
        content: this.pushMsgInfo.content,
        comment: this.pushMsgInfo.comment,
        with_email: this.pushMsgInfo.with_email,
        with_web_notification: this.pushMsgInfo.with_web_notification,
        recipients: recipient_list
      }
      try {
        if (this.modify) {
          await pushMsgDetailReq('put', this.$route.params.id, real_data)
          await this.loadData()
          this.$Notice.success({ title: '修改成功' })
        } else {
          const res = await pushMsgReq('post', real_data)
          await this.$router.push({ name: 'push_message_show', params: { id: res.data['push_message_id'] } })
        }
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    pushMethodChanged() {
      this.$refs['pushMsgForm'].validateField('with_web_notification')
      this.$refs['pushMsgForm'].validateField('with_email')
    }
  }
}
</script>

<style lang="less" scoped>
.text-wrapper {
  white-space: pre-wrap;
}

.select-all-button {
  margin-top: 5px;
}
</style>
