# Generated by Django 3.1.7 on 2021-10-23 11:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('discussion', '0005_response_deleted'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiscussionTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discussion', models.ForeignKey(default=False, on_delete=django.db.models.deletion.CASCADE, to='discussion.discussion')),
            ],
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(max_length=100, unique=True)),
                ('discussion', models.ManyToManyField(through='discussion.DiscussionTag', to='discussion.Discussion')),
            ],
            options={
                'permissions': [('查看标签', '查看标签'), ('创建标签', '创建标签'), ('删除标签', '删除标签'), ('更新标签', '更新标签')],
                'default_permissions': (),
            },
        ),
        migrations.AddField(
            model_name='discussiontag',
            name='tag',
            field=models.ForeignKey(default=False, on_delete=django.db.models.deletion.CASCADE, to='discussion.tag'),
        ),
        migrations.AlterUniqueTogether(
            name='discussiontag',
            unique_together={('discussion_id', 'tag_id')},
        ),
    ]
