"""
define api user get course he/she is managing
"""

from django.contrib.auth import get_user_model
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import require_GET, require_http_methods

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_USER_PROFILE_CHANGE,
                                  CORE_USER_PROFILE_VIEW)
from core.api.utils import (ErrorCode, failed_api_response,
                            parse_data, require_item_exist, response_wrapper,
                            success_api_response, wrapped_api)
from core.models.course import Course
from core.models.user_profile import UserProfile


def get_user_profile_impl(user):
    model = UserProfile.objects.filter(user=user).first()
    if model is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'Such User No Profile')
    user_profile_dict = model_to_dict(model)
    if model.course is not None:
        cur_course = model_to_dict(model.course)
        del cur_course['retake_students']
        del cur_course['allowed_users']
        user_profile_dict['course'] = cur_course
    user_profile_dict['first_name'] = model.user.first_name
    user_profile_dict['last_name'] = model.user.last_name
    return user_profile_dict


@response_wrapper
@jwt_auth(perms=[])
@require_GET
def get_user_profile(request: HttpRequest):
    """get mid belong course

    [method]: GET

    [route]: /api/user-profile
    """
    user = request.user
    if not user:
        return failed_api_response(ErrorCode.UNAUTHORIZED, 'No Such User')
    return success_api_response(get_user_profile_impl(user))


@response_wrapper
@jwt_auth(perms=[CORE_USER_PROFILE_VIEW])
@require_GET
@require_item_exist(model=get_user_model(), field="id", item="user_id")
def get_specified_user_profile(request: HttpRequest, user_id: int):
    """get user-profile of specified user

    [method]: GET

    [route]: /api/user-profile/<int:user_id>
    """
    user = get_user_model().objects.get(pk=user_id)
    if user is None:
        return failed_api_response(ErrorCode.NOT_FOUND_ERROR, 'No Such User')
    return success_api_response(get_user_profile_impl(user))


def update_user_profile_impl(user, updates: object):
    model, _ = UserProfile.objects.get_or_create(**{'user': user})
    if 'course_id' in updates:
        course_id = updates['course_id']
        if course_id is not None:
            temp: Course = Course.objects.filter(id=course_id).first()
            if temp is None:
                return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'No Such Course')
            model.course = temp
        else:
            model.course = None
    if 'first_name' in updates:
        model.user.first_name = updates["first_name"]
    if 'last_name' in updates:
        model.user.last_name = updates["last_name"]
    model.save()
    model.user.save()
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[])
@require_http_methods(["PUT"])
def update_user_profile(request: HttpRequest):
    """update the course is managing

    [method]: PUT

    [route]: /api/user-profile
    """
    user = request.user
    if not user:
        return failed_api_response(ErrorCode.UNAUTHORIZED, 'No Such User')

    course_id = parse_data(request)['course_id']
    associated_course_ids = user.course_set.all().values_list('id', flat=True)

    if course_id not in associated_course_ids:
        return failed_api_response(ErrorCode.UNAUTHORIZED, 'Course ID is not authorized to user')

    return update_user_profile_impl(user, parse_data(request))


@response_wrapper
@jwt_auth(perms=[CORE_USER_PROFILE_VIEW])
@require_GET
@require_item_exist(model=get_user_model(), field="id", item="user_id")
def get_specified_user_authorized_courses(request: HttpRequest, user_id: int):
    user = get_user_model().objects.get(pk=user_id)
    return success_api_response({"courses": list(user.course_set.all().values("id", "name"))})


@response_wrapper
@jwt_auth(perms=[CORE_USER_PROFILE_CHANGE])
@require_http_methods(["PUT"])
@require_item_exist(model=get_user_model(), field="id", item="user_id")
def set_specified_user_authorized_courses(request: HttpRequest, user_id: int):
    user = get_user_model().objects.get(pk=user_id)
    data: dict = parse_data(request)
    user.course_set.set(data["courses"])
    return success_api_response({})


@response_wrapper
@jwt_auth(perms=[])
@require_GET
def get_user_authorized_courses(request: HttpRequest):
    """Get authorized courses that current user has

    [method]: GET

    [route]: /api/user-profile/authorized-courses
    """
    user = request.user
    if not user:
        return failed_api_response(ErrorCode.UNAUTHORIZED, 'No Such User')
    return success_api_response({"courses": list(user.course_set.all().values("id", "name"))})


@response_wrapper
@jwt_auth(perms=[CORE_USER_PROFILE_CHANGE])
@require_http_methods(["PUT"])
@require_item_exist(model=get_user_model(), field="id", item="user_id")
def update_specified_user_profile(request: HttpRequest, user_id: int):
    """update the course which is under the user's supervisor

    [method]: PUT

    [route]: /api/user-profile/<int:user_id>
    """
    user = get_user_model().objects.get(pk=user_id)
    if user is None:
        return failed_api_response(ErrorCode.NOT_FOUND_ERROR, 'No Such User')
    return update_user_profile_impl(user, parse_data(request))


USER_PROFILE_DETAIL_API = wrapped_api({
    "get": get_user_profile,
    "put": update_user_profile,
})

SPECIFIED_USER_PROFILE_DETAIL_API = wrapped_api({
    "get": get_specified_user_profile,
    "put": update_specified_user_profile,
})

AUTHORIZED_COURSES_API = wrapped_api({
    "get": get_user_authorized_courses
})

SPECIFIED_AUTHORIZED_COURSES_API = wrapped_api({
    "get": get_specified_user_authorized_courses,
    "put": set_specified_user_authorized_courses
})
