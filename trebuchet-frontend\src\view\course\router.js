import Main from '@/view/index/main'

export const courseRouter = {
  path: '/course',
  name: 'course',
  component: Main,
  meta: {
    title: '课程信息',
    icon: 'ios-book',
    jumpRoute: '/course/course-table'
  },
  children: [
    {
      path: 'course-table',
      name: 'course_table',
      meta: {
        title: '课程总览'
      },
      component: () => import('@/view/course/course-table')
    },
    {
      path: 'course-create',
      name: 'course_create',
      meta: {
        title: '课程创建',
        hideInMenu: true
      },
      component: () => import('@/view/course/course-create')
    },
    {
      path: 'course-update/:id',
      name: 'course_update',
      meta: {
        title: '课程更新',
        hideInMenu: true
      },
      component: () => import('@/view/course/course-update')
    },
    {
      path: 'course-retake',
      name: 'course_retake',
      meta: {
        title: '添加重修生'
      },
      component: () => import('@/view/course/course-retake')
    }
  ]
}
