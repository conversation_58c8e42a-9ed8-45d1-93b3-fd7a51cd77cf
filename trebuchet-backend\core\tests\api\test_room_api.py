"""Room APIs test

| method  | path                | description                | Success Code | Success Response       |
| POST    | /api/rooms          | create a room              | 200          | Success Message        |
| POST    | /api/seats          | append a seat for the room | 200          | Success Message        |
| PUT     | /api/rooms/:id      | modify room information    | 200          | Success Message        |
| PUT     | /api/seats/:id      | modify seat information    | 200          | Success Message        |
| GET     | /api/rooms          | get all room information   | 200          | room information array |
| GET     | /api/rooms/:id      | get all seat information   | 200          | room information       |
| GET     | /api/seats/:id      | get a seat information     | 200          | seat information       |
| DELETE  | /api/rooms/:id      | delete a room              | 200          | Success Message        |
| DELETE  | /api/seats/:id      | delete a seat              | 200          | Success Message        |
"""

import json

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.test import Client, TestCase

from core.api.utils import ErrorCode
from core.models.room import Room

BASE_URL = "http://localhost:8000/api/rooms"
USER_MODEL = get_user_model()
CLIENT = Client()


def list_rooms():
    """send request to list rooms

    [method]: get

    example:
    {
        "rooms_all" : 8,
        "rooms_available" : 7,
        "rooms" : [
            {
                "id" : 1,
                "name" : "G849",
                "available" : true,
                "seats" : 50,
                "seats_available" : 40,
            },
            ...
        ]
    }

    """
    return CLIENT.get(BASE_URL)


def list_rooms_query_string(query_string: str):
    """[summary]

    Args:
        query_string (str): [description]

    Returns:
        [type]: [description]
    """
    return CLIENT.get(BASE_URL + "?" + query_string)


def retrieve_room_detail(room_id: int):
    """send request to retrieve room detail

    [method]: get

    example
    {
        "id" : 1,
        "name" : "G849",
        "available" : true,
        "seats_all" : 50,
        "seats_available" : 40,
        "seats" : [
            {
            "id" : 1,
            "name" : "test seat",
            "pos_x" : 1,
            "pos_y" : 1,
            "available" : true,
            "comment" : "test seat"
            },
            ...
        ]
    }

    """
    return CLIENT.get(BASE_URL + "/" + str(room_id))


def create_room(data):
    """send request to create room

    [method]: post

    """
    return CLIENT.post(BASE_URL, json.dumps(data), content_type="application/json")


def update_room(room_id, data):
    """send request to update room information

    [method]: put

    """
    return CLIENT.put(BASE_URL + "/" + str(room_id), json.dumps(data), content_type="application/json")


def remove_room(room_id):
    """send request to remove a room

    [method]: delete

    """
    return CLIENT.delete(path=BASE_URL + "/" + str(room_id))


def clean_up() -> None:
    """clean up db
    """
    Room.objects.all().delete()


def build_room_response(room: dict, seats=True):
    """build room response
    """
    retval = {
        "id": room.get("id"),
        "name": room.get("name")
    }
    if room.get("available") is None:
        retval.update({"available": True})
    else:
        retval.update({"available": room.get("available")})
    if room.get("comment") is None:
        retval.update({"comment": ""})
    else:
        retval.update({"comment": room.get("comment")})
    retval.update({
        "seats_all": 0,
        "seats_available": 0
    })
    if seats:
        retval.update({"seats": []})
    return retval


class TestRoomAPI(TestCase):
    """TestCase class for room api
    """

    def build_room_dict(self, name: str, available: bool = None, comment: str = None) -> dict:
        """build room dict data
        """
        idx = self.idx
        self.idx = self.idx + 1
        retval = {
            "id": idx,
            "name": name
        }
        if not available is None:
            retval.update({"available": available})
        if not comment is None:
            retval.update({"comment": comment})
        return retval

    def setUp(self) -> None:
        """initialize
        """
        self.idx = 1
        init_room = [
            self.build_room_dict("A1", comment="Test A1"),
            self.build_room_dict("A2", available=True, comment="Test A2"),
            self.build_room_dict("A3", available=True, comment="今回はここまで")
        ]
        for room in init_room:
            Room.objects.create(**room)
        self.init_room = init_room
        user = USER_MODEL.objects.create_user(
            username="test", password="12345")
        permission_1 = Permission.objects.get(codename="view_room")
        permission_2 = Permission.objects.get(codename="add_room")
        permission_3 = Permission.objects.get(codename="change_room")
        permission_4 = Permission.objects.get(codename="delete_room")
        user.user_permissions.add(permission_1)
        user.user_permissions.add(permission_2)
        user.user_permissions.add(permission_3)
        user.user_permissions.add(permission_4)
        user.save()
        auth_token = CLIENT.post(
            "http://localhost:8000/api/token-auth",
            {"username": "test", "password": "12345"}).json().get("access_token")
        CLIENT.defaults["HTTP_AUTHORIZATION"] = "Bearer" + " " + auth_token

    def assert_code(self, code: int, response):
        """assert status code
        """
        try:
            self.assertEqual(code, response.status_code)
        except AssertionError as err:
            print("Expected status code: {}, but actual status code: {}".format(
                code, response.status_code))
            raise err

    def test_all(self):
        """test all cases
        """
        self.list_rooms()
        self.retrieve_room_detail_success()
        self.retrieve_room_detail_fail()
        self.create_room_success()
        self.create_room_fail()
        self.update_room_success()
        self.update_room_fail()
        self.remove_room_success()
        self.remove_room_fail()
        clean_up()
        self.query_string()

    def list_rooms(self):
        """list rooms test
        """
        response = list_rooms()
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(
            data, {
                "rooms_all": 3,
                "rooms_available": 3,
                "total_count": 3,
                "page_all": 1,
                "page_now": 1,
                "rooms": [build_room_response(room, False) for room in self.init_room]
            })

    def retrieve_room_detail_success(self):
        """retrieve room detail test, valid request
        """
        response = retrieve_room_detail(1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(data, build_room_response(self.init_room[0]))

    def retrieve_room_detail_fail(self):
        """retrieve room detail test, invalid request
        """
        response = retrieve_room_detail(114514)
        self.assert_code(ErrorCode.ITEM_NOT_FOUND.value, response)
        data = response.json()
        self.assertIsNotNone(data.get("error_msg"))

    def check_post(self, idx, name, available, comment):
        """check post request
        """
        response = retrieve_room_detail(idx)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        data = response.json()
        self.assertDictEqual(data, build_room_response({
            "id": idx,
            "name": name,
            "available": available,
            "comment": comment
        }))

    def create_room_success(self):
        """create room test, valid request
        """
        post_data_1 = {
            "name": "A4"
        }
        post_data_2 = {
            "name": "A5",
            "available": False
        }
        post_data_3 = {
            "name": "A6",
            "available": False,
            "comment": "Test Post"
        }
        post_data_4 = {
            "name": "Test Room",
            "available": False,
            "comment": "The First Test Room",
            "seats": [
                {
                    "name": "1",
                    "available": True,
                    "pos_x": 1,
                    "pos_y": 1,
                    "comment": "The First Test Seat"
                },
                {
                    "name": "2",
                    "pos_x": 2,
                    "pos_y": 2,
                    "comment": "The Second Test Seat"
                }
            ]
        }
        response = create_room(post_data_1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = create_room(post_data_2)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = create_room(post_data_3)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = create_room(post_data_4)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        self.check_post(4, "A4", True, "")
        self.check_post(5, "A5", False, "")
        self.check_post(6, "A6", False, "Test Post")

    def create_room_fail(self):
        """create room test, invalid request
        """
        # duplicate
        post_data_4 = {
            "name": "A4"
        }
        post_data_5 = {
            "name": "A7",
            "available": False,
            "comment": "???",
            "wrong": "oh"
        }
        response = create_room(post_data_4)
        self.assert_code(ErrorCode.ITEM_ALREADY_EXISTS.value, response)
        response = create_room(post_data_5)
        self.assert_code(ErrorCode.INVALID_REQUEST_ARGS.value, response)

    def update_room_success(self):
        """update room information test, valid request
        """
        put_data_1 = {
            "name": "AP",
            "available": False,
            "comment": "nice"
        }
        response = update_room(4, put_data_1)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = retrieve_room_detail(4)
        data = response.json()
        self.assertDictEqual(data, build_room_response({
            "id": 4,
            "name": "AP",
            "available": False,
            "comment": "nice"
        }))

    def update_room_fail(self):
        """update room information test, invalid request
        """
        # duplicate
        put_data_2 = {
            "name": "A5",
            "available": False,
            "comment": "nice"
        }
        # args error
        put_data_3 = {
            "name": "Wrong",
            "available": False,
            "comment": "nice",
            "wrong": "oh"
        }
        response = update_room(4, put_data_2)
        self.assert_code(ErrorCode.ITEM_ALREADY_EXISTS.value, response)
        response = update_room(4, put_data_3)
        self.assert_code(ErrorCode.INVALID_REQUEST_ARGS.value, response)
        response = update_room(114514, put_data_2)
        self.assert_code(ErrorCode.ITEM_NOT_FOUND.value, response)

    def remove_room_success(self):
        """remove room test, valid request
        """
        response = remove_room(4)
        self.assert_code(ErrorCode.SUCCESS.value, response)
        response = retrieve_room_detail(4)
        self.assert_code(ErrorCode.ITEM_NOT_FOUND.value, response)

    def remove_room_fail(self):
        """remove room test, invalid request
        """
        response = remove_room(114514)
        self.assert_code(ErrorCode.ITEM_NOT_FOUND.value, response)

    def query_string(self):
        """list rooms test with query string
        """
        rooms = []
        names = []
        for idx in range(0, 100):
            names.append("A" + str(idx))
            rooms.append({"name": names[idx]})
            response = create_room(rooms[idx])
            self.assert_code(ErrorCode.SUCCESS.value, response)
        response = list_rooms_query_string("name__exact=A50")
        self.assert_code(ErrorCode.SUCCESS.value, response)
        self.assertDictEqual(
            response.json(), {
                "rooms_all": 100,
                "rooms_available": 100,
                "total_count": 1,
                "page_all": 1,
                "page_now": 1,
                "rooms": [
                    {"id": 58,
                     "name": "A50",
                     "available": True,
                     "comment": "",
                     "seats_all": 0,
                     "seats_available": 0}
                ]
            })
        response = list_rooms_query_string(
            "name__exact=A50&available__exact=False")
        self.assert_code(ErrorCode.SUCCESS.value, response)
        self.assertDictEqual(
            response.json(),
            {
                "rooms_all": 100,
                "rooms_available": 100,
                "total_count": 0,
                "page_all": 1,
                "page_now": 1,
                "rooms": []
            }
        )
        response = list_rooms_query_string("order_by=name&page=2&page_size=3")
        self.assert_code(ErrorCode.SUCCESS.value, response)
        self.assertDictEqual(
            response.json(),
            {
                'rooms_all': 100,
                'rooms_available': 100,
                'total_count': 100,
                'page_all': 34,
                'page_now': 2,
                'rooms': [
                    {
                        'id': 19,
                        'name': 'A11',
                        'available': True,
                        'comment': '',
                        'seats_all': 0,
                        'seats_available': 0
                    },
                    {
                        'id': 20,
                        'name': 'A12',
                        'available': True,
                        'comment': '',
                        'seats_all': 0,
                        'seats_available': 0
                    },
                    {
                        'id': 21,
                        'name': 'A13',
                        'available': True,
                        'comment': '',
                        'seats_all': 0,
                        'seats_available': 0
                    }
                ]})
        response = list_rooms_query_string("page=1000")
        self.assert_code(ErrorCode.SUCCESS.value, response)
        self.assertDictEqual(
            response.json(),
            {
                "rooms_all": 100,
                "rooms_available": 100,
                "total_count": 100,
                "page_all": 10,
                "page_now": 1000,
                "rooms": []
            }
        )
