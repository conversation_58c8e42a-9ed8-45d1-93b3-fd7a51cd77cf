<template>
  <Card>
    <filter-table
      :data="tableData"
      :columns="columns"
      :default-filter="
        this.$store.state.app.tableFilter.testcaseTable ? this.$store.state.app.tableFilter.testcaseTable : {}
      "
      @on-search="onSearch"
    />
    <div style="margin: 10px; overflow: hidden">
      <div style="float: right">
        <Page
          :total="totalCnt"
          :current="curPage"
          :page-size="pageSize"
          show-total
          show-elevator
          @on-change="changePage"
        />
      </div>
    </div>
  </Card>
</template>

<script>
import { testcaseReq } from '@/api/judge'
import FilterTable from '@/view/filter-table/filter-table'
import { getErrModalOptions, getLocalTime } from '@/libs/util'
import _ from 'lodash'
import { LinkButton, WhitePre } from '@/libs/render-item'
import { testcaseAbstract, testcaseName } from '@/libs/testcases'

export default {
  name: 'TestCaseTable',
  components: { FilterTable },
  data() {
    return {
      tableData: [],
      columns: [
        {
          title: 'ID',
          key: 'id',
          filter: {
            type: 'Input'
          },
          resizable: true,
          minWidth: 80,
          render: (h, params) => WhitePre(h, params.row.id)
        },
        {
          title: 'Name',
          key: 'name',
          filter: {
            type: 'Input'
          },
          resizable: true,
          minWidth: 300,
          render: (h, params) => WhitePre(h, testcaseName(params.row.name))
        },
        {
          title: 'Judge Parameter',
          resizable: true,
          minWidth: 200,
          key: 'judge_parameter'
        },
        {
          title: 'Abstract',
          resizable: true,
          minWidth: 160,
          render: (h, params) => WhitePre(h, testcaseAbstract(params.row['judge_parameter']))
        },
        {
          title: 'Created At',
          key: 'created_at',
          resizable: true,
          minWidth: 140,
          render: (h, params) => h('div', getLocalTime(params.row['created_at']))
        },
        {
          title: 'Action',
          key: 'action',
          align: 'center',
          resizable: true,
          minWidth: 100,
          render: (h, params) => LinkButton(h, params.row.id, 'testcase_detail', '查看', false)
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      order_by: '-created_at',
      filter: {}
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.testcaseTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.testcaseTable)
    }
    this.curPage = this.$store.state.app.tablePage.testcaseTable ? this.$store.state.app.tablePage.testcaseTable : 1
    this.loadData(this.curPage)
  },
  methods: {
    loadData(index) {
      testcaseReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    changePage(index) {
      testcaseReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'testcaseTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onSearch(search) {
      search = this.refactorSearchObject(search)
      testcaseReq('get', {
        page: 1,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'testcaseTable' })
          this.$store.commit('setTableFilter', { filter: search, name: 'testcaseTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {}
      Object.keys(search).forEach((key) => {
        if (key === 'id') {
          this.filter[key + '__exact'] = search[key]
        } else {
          this.filter[key + '__contains'] = search[key]
        }
      })
      return searchNew
    }
  }
}
</script>
