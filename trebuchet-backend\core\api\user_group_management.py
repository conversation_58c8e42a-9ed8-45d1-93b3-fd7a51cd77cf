"""User Group Management APIs
"""
from django.contrib.auth.models import Permission, Group
from django.core.paginator import Paginator
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.query_utils import query_page
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, wrapped_api)


@response_wrapper
@jwt_auth(perms=["auth.add_group"])
@require_POST
def create_user_group(request: HttpRequest):
    """create user group

    [method]: POST

    [route]: /api/user-group/create
    """
    data: dict = parse_data(request)
    if not data:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Invalid request args.")
    name = data.get("name")
    permission = data.get("permission")
    if name is None:
        return failed_api_response(ErrorCode.REQUIRED_ARG_IS_NULL_ERROR, "name")
    if permission is None:
        return failed_api_response(ErrorCode.REQUIRED_ARG_IS_NULL_ERROR, "permission")
    if Group.objects.filter(name=name).exists():
        return failed_api_response(ErrorCode.DUPLICATED_ERROR, name)

    permission_all = Permission.objects.all()
    permissions = []
    for perm in permission:
        app_label, codename = perm.split(".")
        permissions.append(permission_all
                           .filter(content_type__app_label=app_label,
                                   codename=codename)
                           .first())
    new_group = Group.objects.create(name=name)
    new_group.permissions.clear()
    new_group.permissions.add(*permissions)
    new_group.save()

    return success_api_response({"result": "Ok, group created."})


@response_wrapper
@jwt_auth(perms=["auth.change_group"])
@require_http_methods(["PUT"])
@require_item_exist(model=Group, item="group_id", field="id")
def update_user_group_permission(request: HttpRequest, group_id: int):
    """update group's permissions

    [method]: PUT

    [route]: /api/group/<int:group_id>
    """
    data: dict = parse_data(request)
    if not data:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Invalid request args.")
    permission = data.get("permission")
    if not permission:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Permission required.")

    permission_all = Permission.objects.all()
    permissions = []
    for perm in permission:
        app_label, codename = perm.split(".")
        permissions.append(permission_all
                           .filter(content_type__app_label=app_label,
                                   codename=codename)
                           .first())
    group = Group.objects.get(pk=group_id)
    group.permissions.clear()
    group.permissions.add(*permissions)
    group.save()
    return success_api_response({"result": "Ok, permission updated successfully."})


@response_wrapper
@jwt_auth(perms=["auth.delete_group"])
@require_http_methods(["DELETE"])
def delete_user_group(request: HttpRequest):
    """delete user

    [method]: DELETE

    [route]: /api/user-group/delete
    """
    data: dict = parse_data(request)
    if not data:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "Invalid request args.")
    name = data.get("name")
    if not name:
        return failed_api_response(ErrorCode.REQUIRED_ARG_IS_NULL_ERROR, "name")
    group = Group.objects.filter(name=name).first()
    if not group:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "No such group.")
    group.delete()
    return success_api_response({"result": "Ok, group is removed."})


@response_wrapper
@jwt_auth(perms=["auth.view_group"])
@require_GET
@query_page(default=20)
def list_groups(request: HttpRequest, *args, **kwargs):
    """list all user groups in OA

    [method]: GET

    [route]: /api/user
    """
    group_cnt = Group.objects.count()
    groups = Group.objects.all().order_by("id")
    page = kwargs.get("page")
    page_size = kwargs.get("page_size")
    paginator = Paginator(groups, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        group_info = []
    else:
        group_info = list(paginator.get_page(page)
                          .object_list
                          .values("id", "name"))
    data = {
        "item_count": group_cnt,
        "page_all": page_all,
        "page_now": page,
        "groups": group_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=["auth.view_group"])
@require_GET
@require_item_exist(model=Group, item="group_id", field="id")
def retrieve_user_group_detail(request: HttpRequest, group_id: int):
    """retrieve a specified user's information

    [method]: GET

    [route]: /api/group/<int:group_id>
    """
    group: Group = Group.objects.get(pk=group_id)
    name = group.name
    permissions = list(group.permissions.values_list('content_type__app_label', 'codename'))
    permissions = ["%s.%s" % (ct, name) for ct, name in permissions]
    permissions.sort()
    data = {
        "id": group_id,
        "name": name,
        "permissions": permissions
    }
    return success_api_response(data)


USER_GROUP_DETAIL_API = wrapped_api({
    "get": retrieve_user_group_detail,
    "put": update_user_group_permission
})
