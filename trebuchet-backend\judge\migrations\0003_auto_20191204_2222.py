# Generated by Django 2.2.6 on 2019-12-04 14:22

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('judge', '0002_problemjudgerecord_project_in_exam'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='adminuploadedfile',
            options={'default_permissions': (),
                     'permissions': [('下载题目附件', '下载题目附件'), ('上传题目附件', '上传题目附件'), ('下载测试点附件', '下载测试点附件'),
                                     ('上传测试点附件', '上传测试点附件')]},
        ),
        migrations.AlterModelOptions(
            name='judgeparametertemplate',
            options={'default_permissions': (), 'permissions': []},
        ),
        migrations.AlterModelOptions(
            name='problem',
            options={'default_permissions': (),
                     'permissions': [('修改题目', '修改题目'), ('创建题目', '创建题目'), ('删除题目', '删除题目'), ('查看题目', '查看题目')]},
        ),
        migrations.AlterModelOptions(
            name='problemjudgerecord',
            options={'default_permissions': (),
                     'permissions': [('修改题目评测记录', '修改题目评测记录'), ('创建题目评测记录', '创建题目评测记录'), ('删除题目评测记录', '删除题目评测记录'),
                                     ('查看题目评测记录', '查看题目评测记录'), ('查看考试通过情况', '查看考试通过情况'), ('查看通过排行', '查看通过排行'),
                                     ('查看题目通过情况', '查看题目通过情况'), ('查看Project通过情况', '查看Project通过情况')]},
        ),
        migrations.AlterModelOptions(
            name='testcase',
            options={'default_permissions': (),
                     'permissions': [('修改测试点', '修改测试点'), ('添加测试点', '添加测试点'), ('删除测试点', '删除测试点'), ('查看测试点', '查看测试点')]},
        ),
        migrations.AlterModelOptions(
            name='testcasejudgerecord',
            options={'default_permissions': (),
                     'permissions': [('修改测试点评测记录', '修改测试点评测记录'), ('添加测试点评测记录', '添加测试点评测记录'), ('删除测试点评测记录', '删除测试点评测记录'),
                                     ('查看测试点评测记录', '查看测试点评测记录'), ('查看测试点通过情况', '查看测试点通过情况')]},
        ),
        migrations.AlterModelOptions(
            name='usersubmittedfile',
            options={'default_permissions': (), 'permissions': [('课上下载附件', '课上下载附件')]},
        ),
    ]
