"""
define enums and constant related to judge
"""
QUEUEING = -2
JUDGING = -1

ACCEPTED = 0
WRONG_ANSWER = 1
RUNTIME_ERROR = 2
MEMORY_LIMIT_EXCEEDED = 3
TIME_LIMIT_EXCEEDED = 4
FUNCTION_LIMIT_VIOLATION = 5
COMPILATION_ERROR = 6
PRESENTATION_ERROR = 7
UNKNOWN_ERROR = 8

TEST_CASE_RESULT_TAG_CHOICES = (
    (JUDGING, 'Judging'),
    (ACCEPTED, 'Accepted'),
    (WRONG_ANSWER, 'Wrong Answer'),
    (RUNTIME_ERROR, 'Runtime Error'),
    (MEMORY_LIMIT_EXCEEDED, 'Memory Limit Exceeded'),
    (TIME_LIMIT_EXCEEDED, 'Time Limit Exceeded'),
    (FUNCTION_LIMIT_VIOLATION, 'Function Limit Violation'),
    (COMPILATION_ERROR, 'Compilation Error'),
    (PRESENTATION_ERROR, 'Presentation Error'),
    (UNKNOWN_ERROR, 'Unknown Error'),
)

PASSED = 0
FAILED = 1
ERROR = 2

PROBLEM_RESULT_TAG_CHOICES = (
    (QUEUEING, 'Queueing'),
    (JUDGING, 'Judging'),
    (PASSED, 'Passed'),
    (FAILED, 'Failed'),
    (ERROR, 'Error'),
)

USERNAME_MAX_LENGTH = 50
JUDGER_IDENTIFIER_MAX_LENGTH = 20
TOKEN_MAX_LENGTH = 300
JUDGER_COURSE_CODE_LENGTH = 100
JUDGER_CHAPTER_CODE_LENGTH = 100
JUDGER_TOOL_CHAIN_LENGTH = 50
STUDENT_COMMENT_LENGTH = 400
REJUDGE_BATCH_LENGTH = 100
