"""
declare UserSubmittedFile model
"""
from django.db import models

from judge.constants import TOKEN_MAX_LENGTH, USERNAME_MAX_LENGTH
from judge.models.permissions import DOWNLOAD_PJR_ATTACHMENT


class FileTypes(models.IntegerChoices):
    SUBMISSION_FILE = 0
    IMAGE_FILE = 1
    JUDGE_DATA = 2
    PROBLEM_DATA = 3


class UserSubmittedFile(models.Model):
    """
    Storing the data about user's upload.
    """

    edx_username = models.Char<PERSON>ield(max_length=USERNAME_MAX_LENGTH)
    filename = models.Char<PERSON>ield(max_length=100)
    type = models.IntegerField(choices=FileTypes.choices, default=0)
    oss_token = models.CharField(max_length=TOKEN_MAX_LENGTH, blank=True)
    submitted_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (DOWNLOAD_PJR_ATTACHMENT, DOWNLOAD_PJR_ATTACHMENT),
        ]
