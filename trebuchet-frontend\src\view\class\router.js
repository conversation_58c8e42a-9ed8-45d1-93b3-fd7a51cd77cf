import Main from '@/view/index/main'

export const classRouter = {
  path: '/class',
  name: 'class',
  component: Main,
  meta: {
    title: '班级信息',
    icon: 'ios-keypad',
    jumpRoute: '/class/class-table'
  },
  children: [
    {
      path: 'class-table',
      name: 'class_table',
      meta: {
        title: '班级列表'
      },
      component: () => import('@/view/class/class-table')
    },
    {
      path: 'class-create',
      name: 'class_create',
      meta: {
        title: '班级创建'
      },
      component: () => import('@/view/class/class-create')
    },
    {
      path: 'class-show/:id',
      name: 'class_show',
      meta: {
        title: '班级详情',
        hideInMenu: true
      },
      component: () => import('@/view/class/class-show')
    }
  ]
}
