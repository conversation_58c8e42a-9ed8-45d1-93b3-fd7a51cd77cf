<template>
  <div ref="sunburstChart" :style="{ width: '100%', height: height }"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'FailChart',
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartData: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: '基于测试点的错误分类统计'
    }
  },
  data() {
    return {
      sunburstChart: null
    }
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.sunburstChart) {
      window.removeEventListener('resize', this.resizeHandler)
      this.sunburstChart.dispose()
      this.sunburstChart = null
    }
  },
  methods: {
    initChart() {
      this.$nextTick(() => {
        if (this.$refs.sunburstChart) {
          this.sunburstChart = echarts.init(this.$refs.sunburstChart)

          const option = {
            title: {
              text: this.title,
              fontSize: 16,
              left: 'center'
            },
            tooltip: {
              trigger: 'item',
              formatter: (params) => {
                const treePathInfo = params.treePathInfo
                const currentLevel = params.treePathInfo.length - 1
                const currentInfo = treePathInfo[currentLevel]

                // 只返回当前层级的信息
                return `${currentInfo.name}: ${currentInfo.value}次`
              }
            },
            series: [
              {
                name: '错误分析',
                type: 'sunburst',
                radius: ['0%', '70%'],
                itemStyle: {
                  borderRadius: 0,
                  borderWidth: 1,
                  borderColor: '#fff'
                },
                label: {
                  rotate: 'radial',
                  minAngle: 1,
                  position: 'outside',
                  distance: 5,
                  alignTo: 'none',
                  overflow: 'truncate',
                  fontSize: 10
                },
                emphasis: {
                  focus: 'ancestor',
                  itemStyle: {
                    borderWidth: 2
                  }
                },
                levels: [
                  {},
                  {
                    r0: '0%',
                    r: '35%',
                    label: {
                      rotate: 'radial',
                      fontSize: 14,
                      show: true
                    }
                  },
                  {
                    r0: '35%',
                    r: '70%',
                    label: {
                      show: false
                    }
                  }
                ],
                data: this.chartData
              }
            ]
          }

          this.sunburstChart.setOption(option)

          this.resizeHandler = () => {
            this.sunburstChart.resize()
          }
          window.addEventListener('resize', this.resizeHandler)
        }
      })
    },

    updateChart() {
      if (!this.sunburstChart) return

      this.sunburstChart.setOption({
        series: [
          {
            data: this.chartData
          }
        ]
      })
    }
  }
}
</script>
