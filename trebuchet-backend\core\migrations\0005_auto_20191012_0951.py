# Generated by Django 2.2.6 on 2019-10-12 01:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0004_auto_20191007_1434'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='studentprogress',
            name='passed_project',
        ),
        migrations.AddField(
            model_name='course',
            name='retake_students',
            field=models.ManyToManyField(related_name='_course_retake_students_+', to='core.Student'),
        ),
        migrations.AddField(
            model_name='course',
            name='under_class_exam',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+',
                                    to='core.Exam'),
        ),
        migrations.AddField(
            model_name='exam',
            name='course',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='core.Course'),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='checking_student',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.ExamQueue'),
        ),
        migrations.AlterField(
            model_name='examrecord',
            name='checked_in_at',
            field=models.DateTimeField(null=True),
        ),
        migrations.AlterField(
            model_name='examrecord',
            name='checked_out_at',
            field=models.DateTimeField(null=True),
        ),
    ]
