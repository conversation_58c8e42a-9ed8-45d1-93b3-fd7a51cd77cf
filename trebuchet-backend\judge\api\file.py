"""
upload and download file
"""
from datetime import datetime, timed<PERSON>ta
from urllib.request import pathname2url

from django.conf import settings
from django.http import HttpResponse
from django.http.request import HttpRequest
from django.views.decorators.http import require_GET, require_POST
from minio import Minio

from core.api.auth import jwt_auth
from core.api.utils import (ErrorCode, failed_api_response, require_item_exist,
                            response_wrapper, validate_args, wrapped_api)
from judge.api.permissions import (JUDGE_DOWNLOAD_PROBLEM_ATTACHMENT,
                                   JUDGE_DOWNLOAD_TESTCASE_ATTACHMENT,
                                   JUDGE_UPLOAD_PROBLEM_ATTACHMENT,
                                   JUDGE_UPLOAD_TESTCASE_ATTACHMENT, JUDGE_VIEW_PJR)
from judge.models.problem import Problem
from judge.models.problem_judge_record import ProblemJudgeRecord
from judge.models.test_case import TestCase


def _validate_upload_file_without_filename(request: HttpRequest) -> bool:
    if request.FILES.get('file', None) is None:
        return False
    return True


def _validate_upload_file(request: HttpRequest) -> bool:
    """
    validate upload file
    Args:
        request:

    Returns:

    """
    if request.GET.get('filename', None) is None or request.FILES.get('file', None) is None:
        return False
    return True


minio_client = Minio(
    settings.S3_ADDRESS,
    access_key=settings.S3_SECRET_ID,
    secret_key=settings.S3_SECRET_KEY,
    secure=settings.S3_SSL
)


def s3_download(oss_token, filename, ftype):
    """
    download file from object storage
    Args:
        oss_token:
        filename:

    Returns:

    """
    response = minio_client.get_object(settings.S3_BUCKET_NAMES[ftype], oss_token)
    response = HttpResponse(response.read())
    response['Content-Type'] = 'application/octet-stream'
    response['Content-Disposition'] = 'attachment;filename=' + \
                                      pathname2url(filename)
    return response


def s3_download_url(oss_token, ftype):
    return minio_client.presigned_get_object(settings.S3_BUCKET_NAMES[ftype], oss_token, expires=timedelta(hours=1))


def s3_upload(oss_token, request, ftype):
    """
    upload file to object storage
    Args:
        oss_token:
        request:

    Returns:

    """
    response = minio_client.put_object(
        settings.S3_BUCKET_NAMES[ftype], oss_token, request.FILES['file'], request.FILES['file'].size)
    return response


def generate_oss_token(query_id, filename) -> str:
    """
    generate oss token for a file
    Args:
        category:
        query_id:

    Returns:

    """
    blacklist = [" ", "(", ")", "/", ".."]
    for black in blacklist:
        filename = filename.replace(black, "_")
    return "{}/{}/{}".format(query_id, str(int(datetime.utcnow().timestamp() * 1000)), filename)


@response_wrapper
@jwt_auth(perms=[JUDGE_DOWNLOAD_PROBLEM_ATTACHMENT])
@require_GET
@require_item_exist(model=Problem, field='id', item='query_id')
def download_problem_file(request, query_id):
    """
    download problem file
    Args:
        request:
        query_id:

    Returns:

    """
    record = Problem.objects.get(pk=query_id).problem_data
    if record is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'This problem doesn\'t have problem_data file')
    try:
        response = s3_download(record.oss_token, record.filename, record.type)
    except Exception as exception:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, str(exception))
    return response


# pylint: disable=W0613
@response_wrapper
@jwt_auth(perms=[JUDGE_UPLOAD_PROBLEM_ATTACHMENT])
@validate_args(func=_validate_upload_file)
@require_POST
def upload_problem_file(request, query_id):
    """
    upload problem file
    Args:
        request:
        query_id:

    Returns:

    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # data: dict = {}
    # problem = Problem.objects.get(pk=query_id)
    # data['user'] = request.user
    # data['filename'] = request.GET['filename']
    # data['oss_token'] = generate_oss_token(problem.id, data['filename'])
    # data['type'] = FileTypes.PROBLEM_DATA
    # if problem.problem_data is None:
    #     new_file = AdminUploadedFile.objects.create(**data)
    #     problem.problem_data = new_file
    #     problem.save()
    # else:
    #     file_id = problem.problem_data.id
    #     AdminUploadedFile.objects.filter(pk=file_id).update(**data)
    #     AdminUploadedFile.objects.get(pk=file_id).save()
    #     problem = Problem.objects.get(pk=query_id)
    # try:
    #     s3_upload(problem.problem_data.oss_token, request, FileTypes.PROBLEM_DATA)
    # except Exception as exception:
    #     return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, str(exception))
    # return success_api_response({'Status': 'OK! File has been uploaded!'})


PROBLEM_FILE_API = wrapped_api({
    'POST': upload_problem_file,
    'GET': download_problem_file
})


# pylint: disable=W0613
@response_wrapper
@jwt_auth(perms=[JUDGE_UPLOAD_TESTCASE_ATTACHMENT])
@validate_args(func=_validate_upload_file)
@require_POST
def upload_test_case_file(request: HttpRequest, query_id: int):
    """
    upload test case file
    Args:
        request:
        query_id:

    Returns:

    """
    return failed_api_response(ErrorCode.BAD_REQUEST_ERROR, "该功能已弃用")
    # data: dict = {}
    # test_case = TestCase.objects.get(pk=query_id)
    # data['user'] = request.user
    # data['filename'] = request.GET['filename']
    # data['type'] = FileTypes.JUDGE_DATA
    # data['oss_token'] = generate_oss_token(test_case.id, data['filename'])
    # if test_case.judge_data is None:
    #     new_file = AdminUploadedFile.objects.create(**data)
    #     test_case.judge_data = new_file
    #     test_case.save()
    # else:
    #     file_id = test_case.judge_data.id
    #     AdminUploadedFile.objects.filter(pk=file_id).update(**data)
    #     AdminUploadedFile.objects.get(pk=file_id).save()
    #     test_case = TestCase.objects.get(pk=query_id)
    # try:
    #     s3_upload(test_case.judge_data.oss_token, request, data['type'])
    # except Exception as exception:
    #     return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, str(exception))
    # return success_api_response({'Status': 'OK! File has been uploaded!'})


@response_wrapper
@jwt_auth(perms=[JUDGE_DOWNLOAD_TESTCASE_ATTACHMENT])
@require_GET
@require_item_exist(model=TestCase, field='id', item='query_id')
def download_test_case_file(request, query_id):
    """
    diwnkiad test case file
    Args:
        request:
        query_id:

    Returns:

    """
    record = TestCase.objects.get(pk=query_id).judge_data
    if record is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'This test case doesn\'t have judge_data file')
    try:
        response = s3_download(record.oss_token, record.filename, record.type)
    except Exception as exception:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, str(exception))
    return response


TEST_CASE_FILE_API = wrapped_api({
    'POST': upload_test_case_file,
    'GET': download_test_case_file
})


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_PJR])
@require_GET
@require_item_exist(model=ProblemJudgeRecord, field='id', item='query_id')
def download_problem_judge_record_file(request: HttpRequest, query_id: int):
    """
    download student submit file
    Args:
        request:
        query_id:

    Returns:

    """
    record = ProblemJudgeRecord.objects.get(**{'id': query_id}).attachment
    if record is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'This problem judge record doesn\'t have '
                                                                   'attachment file')
    try:
        response = s3_download(record.oss_token, record.filename, record.type)
    except Exception as exception:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, str(exception))
    return response


PROBLEM_JUDGE_RECORD_FILE_API = wrapped_api({
    'GET': download_problem_judge_record_file
})
