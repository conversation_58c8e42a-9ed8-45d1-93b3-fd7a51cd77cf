"""
declare DiscussionTag model
"""

from django.db import models

from discussion.models.discussion import Discussion
from discussion.models.tag import Tag


class DiscussionTag(models.Model):
    discussion = models.ForeignKey(
        Discussion, on_delete=models.CASCADE, default=False, db_index=True
    )
    tag = models.ForeignKey(
        Tag, on_delete=models.CASCADE, default=False, db_index=True
    )

    class Meta:
        unique_together = ("discussion_id", "tag_id")
