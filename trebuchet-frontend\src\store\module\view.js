export default {
  namespaced: true,
  state: {
    currentWidth: 0,
    currentHeight: 0,
    showUnimportant: false
  },
  mutations: {
    updateSize(state, width, height) {
      state.currentWidth = width
      state.currentHeight = height
    },
    updateShowUnimportant(state, val) {
      state.showUnimportant = val
    }
  },
  getters: {
    mini: (state) => state.currentWidth < 640,
    fullWidth: (state) => state.currentWidth,
    showUnimportant: (state) => state.showUnimportant
  }
}
