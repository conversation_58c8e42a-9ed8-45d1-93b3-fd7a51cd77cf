"""
declare omni config
"""
from django.contrib.auth import get_user_model
from django.db import models

from core.models.permissions import OMNICONFIG_VIEW, OMNICONFIG_EDIT


class OmniConfig(models.Model):
    """
    declare omni component config record schema
    """
    name = models.CharField(max_length=100, unique=True)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True)
    enabled = models.BooleanField(default=False)

    def __str__(self):
        return self.name

    class Meta:
        default_permissions = ()
        permissions = [
            (OMNICONFIG_VIEW, OMNICONFIG_VIEW),
            (OMNICONFIG_EDIT, OMNICONFIG_EDIT)
        ]
