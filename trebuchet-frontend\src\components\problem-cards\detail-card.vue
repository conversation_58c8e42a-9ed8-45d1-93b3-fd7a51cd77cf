<template>
  <Card class="problem-cards">
    <p slot="title">Problem 详情</p>
    <Form ref="problem" :model="problem" :label-width="140">
      <form-item prop="name" label="题目名称">
        <Input v-model="problem.name" type="text" />
      </form-item>
      <form-item prop="type" label="题目类型">
        <RadioGroup :value="problem.type" type="button" button-style="solid">
          <Radio label="0">文件上传</Radio>
          <Radio label="1">单选</Radio>
          <Radio label="2">多选</Radio>
          <Radio label="3">填空</Radio>
        </RadioGroup>
      </form-item>
      <form-item prop="description" label="题目描述">
        <mavon-editor
          :ishljs="true"
          v-model="problem.description"
          placeholder="题目描述在这里修改..."
          style="z-index: 1"
        />
      </form-item>
      <form-item prop="description" label="题目解析" v-if="problem.explain">
        <mavon-editor :ishljs="true" v-model="problem.explain" placeholder="题目解析在这里修改..." style="z-index: 1" />
      </form-item>
      <form-item label="提交间隔限制">
        <i-switch v-model="useColdTime">
          <span slot="open">开</span>
          <span slot="close">关</span>
        </i-switch>
        <input-number v-model="problem.interval" :min="0" :disabled="!useColdTime" class="input" />
        <label style="margin-left: 10px">秒</label>
      </form-item>
      <form-item label="提交次数限制">
        <i-switch v-model="useMaxTime">
          <span slot="open">开</span>
          <span slot="close">关</span>
        </i-switch>
        <input-number v-model="problem.maxSubmission" :min="1" :disabled="!useMaxTime" class="input" />
        <label style="margin-left: 10px">次</label>
      </form-item>
      <form-item label="提交次数限制生效时间">
        <date-picker
          v-model="problem.resetTime"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          placeholder="请选择开始生效的日期和时间"
          style="width: 200px"
          clearable
          :disabled="!useMaxTime"
        />
      </form-item>
    </Form>
  </Card>
</template>

<script>
import './cards.less'

export default {
  name: 'DetailCard',
  props: {
    interval: Number,
    maxSubmission: Number,
    name: String,
    description: String,
    type: String,
    resetTime: String,
    explain: String
  },
  data() {
    return {
      problem: {},
      useMaxTime: false,
      useColdTime: false
    }
  },
  mounted() {
    this.problem = {
      interval: this.interval,
      maxSubmission: this.maxSubmission,
      name: this.name,
      description: this.description,
      type: this.type,
      resetTime: this.resetTime,
      explain: this.explain
    }
    this.useColdTime = this.problem.interval !== null
    this.useMaxTime = this.problem.maxSubmission !== null
  }
}
</script>
