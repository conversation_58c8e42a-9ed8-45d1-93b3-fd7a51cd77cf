"""
declare Push Message Record model
"""
from django.db import models
from django.db.models.deletion import CASCADE, SET_NULL

from core.models.student import Student
from core.models.push_message import PushMessage
from core.models.notification_inbox import NotificationInbox

MSG_PUSH_STATUS_NOT_PUSHED = 0
MSG_PUSH_STATUS_CONFIRMED = 1
MSG_PUSH_STATUS_PUSHED = 2
MSG_PUSH_STATUS_ERROR = 3


class PushMessageRecord(models.Model):
    """This model describes PushMessageRecord.

    此模型用于描述推送通知的推送目标和结果。

    属性:
        push_message: 对应的PushMessage
        student: 对应的Student

        notification_status: Choice 课程网站push状态
        notification_confirmed_at: DateTimeField 课程网站确认收到通知时间
        email_status: Choice 课程网站push状态
        email_confirmed_at: DateTimeField 课程网站确认收到通知时间
    """
    MSG_PUSH_STATUS_TYPE = [
        (MSG_PUSH_STATUS_NOT_PUSHED, "等待推送"),
        (MSG_PUSH_STATUS_CONFIRMED, "收到确认"),
        (MSG_PUSH_STATUS_PUSHED, "推送成功"),
        (MSG_PUSH_STATUS_ERROR, "推送出错")
    ]

    push_message = models.ForeignKey(PushMessage, on_delete=CASCADE)
    student = models.ForeignKey(Student, on_delete=CASCADE)

    related_notification = models.ForeignKey(NotificationInbox, null=True, on_delete=SET_NULL)
    notification_status = models.IntegerField(choices=MSG_PUSH_STATUS_TYPE, default=MSG_PUSH_STATUS_NOT_PUSHED)
    notification_confirmed_at = models.DateTimeField(null=True)
    email_status = models.IntegerField(choices=MSG_PUSH_STATUS_TYPE, default=MSG_PUSH_STATUS_NOT_PUSHED)
    email_confirmed_at = models.DateTimeField(null=True)
