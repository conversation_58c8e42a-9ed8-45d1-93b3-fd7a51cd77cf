<template>
  <div>
    <Modal v-model="showConfirmTable.show" @on-ok="onConfirmOk">
      <div slot="header" class="ivu-modal-confirm-head">
        <div class="ivu-modal-confirm-head-icon ivu-modal-confirm-head-icon-confirm">
          <Icon class="ivu-icon ivu-icon-ios-help-circle" />
          <span class="ivu-modal-confirm-head-title">
            {{ showConfirmTable.form.title }}
          </span>
        </div>
      </div>
      <div class="">
        {{ showConfirmTable.form.content }}
      </div>
    </Modal>
    <Modal v-model="initModal" title="输入初始PIE ID" @on-ok="onInitProgress">
      <Input v-model="initPieInput" type="number" />
    </Modal>
    <Modal v-model="retakerResetModal" title="输入要重置进度的重修生" @on-ok="onSubmitRetakerReset()">
      <Form
        :model="retakerToReset"
        :rules="{
          student_id: [{ pattern: /^[0-9]{8}$/, message: '学号格式错误', trigger: 'blur' }]
        }"
        inline
      >
        <FormItem prop="student_id">
          <Input
            v-model="retakerToReset.student_id"
            search
            enter-button="添加"
            placeholder="请输入学号"
            style="width: 200px"
            @on-search="
              retakerToReset.students.push({
                userName: retakerToReset.student_id
              })
            "
          />
        </FormItem>
        <FormItem>
          <div style="display: flex; align-items: center">
            <Upload :before-upload="beforeUpload" action="">
              <Button icon="ios-cloud-upload-outline">上传 CSV 文件</Button>
            </Upload>
            <label style="margin-left: 10px">CSV文件列名: ["学号"]</label>
          </div>
          <strong>
            <span style="font-size: small">请确保 CSV 文件的编码格式为 UTF-8</span>
          </strong>
        </FormItem>
        <FormItem prop="project_name">
          重置到
          <Input v-model="retakerToReset.project_name" placeholder="重置到" style="width: 200px" />
        </FormItem>
      </Form>
      <Row>
        <Col span="22" offset="1">
          <Table :stripe="true" height="500" :data="retakerToReset.students" :columns="retakerColumns">
            <template slot="userName" slot-scope="{ row }">
              <strong>{{ row.userName }}</strong>
            </template>
            <template slot="action" slot-scope="{ index }">
              <Button type="error" @click="retakerToReset.students.splice(index, 1)">删除</Button>
            </template>
          </Table>
          <Col />
        </Col>
      </Row>
    </Modal>
    <Card>
      <Row>
        <Col>
          <FilterTable
            :data="tableData"
            :columns="columns"
            :default-filter="
              this.$store.state.app.tableFilter.progressTable ? this.$store.state.app.tableFilter.progressTable : {}
            "
            @on-search="onFilterChanged"
          />
          <Table v-show="false" ref="table" />
        </Col>
      </Row>
      <br />
      <Row>
        <Col span="2">
          <Button style="width: 80%" type="primary" @click="refreshTable">刷新</Button>
        </Col>
        <Col span="2">
          <Button style="width: 80%" type="primary" @click="download">导出 CSV</Button>
        </Col>
        <Col span="2">
          <Dropdown style="margin-left: 20px">
            <Button type="text" icon="ios-more">
              更多操作
              <Icon type="ios-arrow-down" />
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem @click.native="onClickInitProgressBtn">初始化</DropdownItem>
              <DropdownItem @click.native="onPullLatestProgress">拉取数据</DropdownItem>
              <DropdownItem @click.native="handleConfirm(confirmTable.inClass)">推到课上</DropdownItem>
              <DropdownItem @click.native="handleConfirm(confirmTable.underClass)">推到课下</DropdownItem>
              <DropdownItem @click.native="handleConfirm(confirmTable.refresh)">强制刷新</DropdownItem>
              <DropdownItem @click.native="onRetakeInit">重修生继承进度</DropdownItem>
              <DropdownItem @click.native="downloadNonSubmitStudent">下载未提交学生名单</DropdownItem>
              <DropdownItem @click.native="retakerResetModal = true">重修生重置进度</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </Col>
        <Col offset="13">
          <Page
            :total="totalCnt"
            :current="curPage"
            :page-size="pageSize"
            show-elevator
            show-total
            @on-change="changePage"
          />
        </Col>
      </Row>
    </Card>
    <Modal v-model="showUnderClassCol" title="进入课下的学生一览">
      <Table :data="toUnderClassInfo" :columns="toUnderClassCol" />
    </Modal>
    <Modal v-model="showModifyProgress" title="选择将学生推到哪一个进度" @on-ok="modifyProgress">
      <Form ref="checkModifyProgressRecord" :model="modifyProgressRecord" :rules="modifyProgressRule">
        <form-item label="输入 project id" prop="project_id">
          <Input v-model="modifyProgressRecord.project_id" />
        </form-item>
        <form-item label="课上课下选择" prop="under_class">
          <radio-group v-model="modifyProgressRecord.under_class">
            <radio :label="1">课下</radio>
            <radio :label="0">课上</radio>
          </radio-group>
        </form-item>
      </Form>
    </Modal>
  </div>
</template>

<script>
import FilterTable from '@/view/filter-table/filter-table'
import {
  examProgress,
  examProgressInit,
  examProgressRetakeInit,
  examProgressRetakeReset,
  examProgressPushToInClass,
  examProgressPushToUnderClass,
  examProgressUnderclass,
  examProgressCSV,
  examProgressUnderclassForce,
  nonSubmitStudent
} from '@/api/exam'
import { userProfileReq } from '@/api/user'
import { getErrModalOptions, getLocalTime, getArrayFromFile, getTableDataFromArray } from '@/libs/util'
import { modifyProgressReq } from '@/api/progress'
import { ActionButton, WhitePre } from '@/libs/render-item'
import _ from 'lodash'

export default {
  name: 'ExamProgressPush',
  components: { FilterTable },
  data() {
    return {
      curCourse: null,
      tableData: [],
      initPieInput: '',
      initModal: false,
      retakerResetModal: false,
      retakerToReset: {
        student_id: '',
        students: [],
        project_name: 'P0'
      },
      expectedColumnNames: ['学号'],
      csvColumns: [
        {
          title: '学号',
          key: '学号'
        }
      ],
      retakerColumns: [
        {
          title: '重修生学号',
          slot: 'userName',
          width: 250,
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          width: 190,
          align: 'center'
        }
      ],
      filter: {},
      columns: [
        { title: 'id', key: 'id' },
        {
          title: '姓名',
          key: 'student_name',
          filter: {},
          render: (h, params) => WhitePre(h, params.row['student_name'])
        },
        {
          title: '学号',
          key: 'studentid',
          filter: {},
          render: (h, params) => WhitePre(h, params.row['studentid'])
        },
        { title: '系号', key: 'department', filter: {} },
        {
          title: '预习情况',
          key: 'preview_state',
          filter: {
            type: 'Select',
            option: {
              0: { value: '已提交', name: '已提交' },
              1: { value: '未提交', name: '未提交' },
              2: { value: '未登录', name: '未登录' }
            }
          }
        },
        { title: '当前项目', key: 'current_project_name', filter: {} },
        {
          title: '通过情况',
          key: 'qualified',
          filter: {
            type: 'Select',
            option: {
              0: {
                value: true,
                name: 'true',
                color: 'green'
              },
              1: {
                value: false,
                name: 'false',
                color: 'red'
              }
            }
          }
        },
        {
          title: '课上/课下',
          key: 'under_class',
          filter: {
            type: 'Select',
            option: {
              0: {
                value: true,
                name: '课下',
                color: 'green'
              },
              1: {
                value: false,
                name: '课上',
                color: 'red'
              }
            }
          },
          render: (h, params) => h('p', params.row.under_class ? '课下' : '课上')
        },
        {
          title: '最后一次提交时间',
          key: 'last_submit_at',
          render: (h, params) => h('p', getLocalTime(params.row['last_submit_at']))
        },
        {
          title: '题目名称',
          key: 'problem_name'
        },
        {
          title: 'Action',
          render: (h, params) =>
            ActionButton(
              h,
              () => {
                this.showModifyProgress = true
                this.modifyProgressRecord.student_id = params.row['studentid']
              },
              '进度修改',
              false
            )
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      toUnderClassInfo: [],
      showUnderClassCol: false,
      toUnderClassCol: [
        { title: '姓名', key: 'name' },
        { title: '学号', key: 'student_id' },
        { title: '推进之前的项目', key: 'from' },
        { title: '推进之后的项目', key: 'to' }
      ],
      modifyProgressRecord: {
        student_id: 0,
        project_id: 0,
        under_class: 1
      },
      showModifyProgress: false,
      modifyProgressRule: {
        project_id: [{ required: true, message: '请输入一个 project id', trigger: 'blur' }],
        under_class: [{ required: true, message: '请选择课上或者课下', trigger: 'blur' }]
      },
      confirmTable: {
        // confirm 注册表,如果后续需要增加confirm modal,且其有可能调用 $modal 则在这里注册,防止两次调用 $modal 导致 modal 闪退
        inClass: {
          title: '确定推到课上吗',
          content: `这个操作会推进课程内所有学生的考试进度, 且不可逆转`,
          onOk: this.onPushToInClassProgress
        },
        underClass: {
          title: '确定推到课下吗',
          content: `这个操作会推进课程内所有学生的考试进度, 且不可逆转`,
          onOk: this.onPushToUnderClassProgress
        },
        refresh: {
          title: '确定强制刷新课程',
          content: `这个操作会强制重载课程配置并刷新课程进度, 性能开销较大`,
          onOk: this.onForceRefresh
        }
      },
      showConfirmTable: {
        show: false,
        form: {}
      }
    }
  },
  computed: {
    columnNames() {
      return this.csvColumns.map((item) => item.title)
    },
    uploadFileReady() {
      if (!this.columnNames) {
        return false
      }
      const result = []
      for (let i = 0; i < this.expectedColumnNames.length; i++) {
        const temp = this.expectedColumnNames[i]
        for (let j = 0; j < this.columnNames.length; j++) {
          if (temp === this.columnNames[j]) {
            result.push(temp)
            break
          }
        }
      }
      return this.expectedColumnNames.every((item, index) => item === result[index])
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.progressTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.progressTable)
    }
    this.curPage = this.$store.state.app.tablePage.progressTable ? this.$store.state.app.tablePage.progressTable : 1
    this.loadCurCourse()
  },
  methods: {
    handleConfirm(form) {
      this.showConfirmTable.form = form
      this.showConfirmTable.show = true
    },
    loadCurCourse() {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course === null) {
            this.$Modal.info({
              title: '请在课程信息/课程总览上选择当前课程'
            })
          } else {
            this.curCourse = res.data.course.id
            this.loadTable()
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    modifyProgress() {
      modifyProgressReq('post', this.curCourse, this.modifyProgressRecord)
        .then(() => {
          this.$Notice.success({ title: '修改成功' })
          this.loadTable(true)
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onPullLatestProgress() {
      examProgressUnderclass(this.curCourse, 'post')
        .then(() => {
          this.$Notice.success({ title: '拉取成功' })
          this.loadTable(true)
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    async onPushToInClassProgress() {
      let res = await examProgress(this.curCourse, 'get', {
        under_class__exact: 'False'
      })
      if (res.data['total_count'] !== 0) {
        this.$Notice.warning({ title: '仍有学生在课上' })
      } else {
        examProgressPushToInClass(this.curCourse, 'put')
          .then(() => {
            this.$Notice.success({ title: '进度推进成功' })
            this.loadTable(true)
          })
          .catch((err) => {
            this.$Modal.error(getErrModalOptions(err))
          })
      }
    },
    onPushToUnderClassProgress() {
      examProgressPushToUnderClass(this.curCourse, 'put')
        .then((res) => {
          this.toUnderClassInfo = res.data.info
          this.showUnderClassCol = true
          this.$Notice.success({ title: '进度推进成功' })
          this.loadTable(true)
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    onForceRefresh() {
      setTimeout(() => {
        examProgressUnderclassForce(this.curCourse)
          .then(() => {
            this.$Notice.success({ title: '强制刷新成功' })
            this.loadTable(true)
          })
          .catch((err) => {
            this.$Modal.error(getErrModalOptions(err))
          })
      }, 500)
    },
    loadTable() {
      examProgress(this.curCourse, 'get', {
        page: this.curPage,
        page_size: this.pageSize,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['progress']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTablePage', {
            page: res.data['page_now'],
            name: 'progressTable'
          })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    changePage(index) {
      this.curPage = index
      this.loadTable()
    },
    refreshTable() {
      this.loadTable()
    },
    onClickInitProgressBtn() {
      this.initModal = true
    },
    onConfirmOk() {
      this.showConfirmTable.form.onOk()
    },
    onInitProgress() {
      examProgressInit(this.curCourse, 'post', {
        project_in_exam: this.initPieInput
      })
        .then(() => {
          this.$Notice.success({ title: '重置成功' })
          this.curPage = 1 // 重置 page index
          this.loadTable(true)
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    onFilterChanged(search) {
      search = this.refactorSearchObject(search)
      this.$store.commit('setTableFilter', {
        filter: search,
        name: 'progressTable'
      })
      if (this.curCourse) {
        this.curPage = 1 // 重置 page index
        this.loadTable()
      }
    },
    download() {
      examProgressCSV(this.curCourse, {
        page: this.curPage,
        page_size: this.pageSize,
        ...this.filter
      })
        .then((res) => {
          const csvData = res.data.split('\n')
          csvData.shift()
          csvData.pop()
          this.$refs['table'].exportCsv({
            filename: '学生进度.csv',
            columns: [
              { key: '姓名' },
              { key: '学号' },
              { key: '系号' },
              { key: '当前项目' },
              { key: '是否通过' },
              { key: '课上/课下' },
              { key: '最后一次提交时间' },
              { key: '题目名称' }
            ],
            data: csvData.map((item) => {
              const splitItem = item.split(',')
              const result = {}
              result['姓名'] = splitItem[2]
              result['学号'] = splitItem[1]
              result['系号'] = splitItem[3]
              result['当前项目'] = splitItem[4]
              result['是否通过'] = splitItem[6] === 'True' ? '通过' : '未通过'
              result['课上/课下'] = splitItem[5] === 'True' ? '课下' : '课上'
              result['最后一次提交时间'] = '\t' + getLocalTime(splitItem[7])
              result['题目名称'] = splitItem[8]
              return result
            })
          })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    downloadNonSubmitStudent() {
      nonSubmitStudent(this.curCourse)
        .then((res) => {
          const csvData = res.data.data
          this.$refs['table'].exportCsv({
            filename: '无提交记录学生.csv',
            columns: [
              { key: '学号' },
              { key: '姓名' },
              { key: '系号' },
              { key: '班号' },
              { key: '邮箱' },
              { key: '帐号' },
              { key: '最后登录' }
            ],
            data: csvData.map((item) => {
              const result = {}
              result['学号'] = item['student_id']
              result['姓名'] = item['student_name']
              result['系号'] = item['student_department']
              result['班号'] = item['student_official_class']
              result['邮箱'] = item['student_email'] || '无'
              result['帐号'] = item['student_username'] || '无'
              result['最后登录'] = getLocalTime(item['student_last_login'])
              return result
            })
          })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onRetakeInit() {
      examProgressRetakeInit(this.curCourse)
        .then((res) => {
          this.$Modal.info({
            title: 'Result',
            content:
              res.data['failed_list'].length === 0
                ? '均继承成功'
                : '因缺失过往记录而未能继承的学号列表: ' + res.data['failed_list']
          })
          this.curPage = 1
          this.loadTable(true)
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onSubmitRetakerReset() {
      let params = {
        selected_course_id: [this.curCourse],
        selected_student_id: this.retakerToReset.students,
        project_name: this.retakerToReset.project_name
      }
      examProgressRetakeReset(this.curCourse, params)
        .then(() => {
          this.$Notice.success({ title: `进度重置成功` })
        })
        .catch((error) => {
          this.$Modal.warning({ title: getErrModalOptions(error).title, content: getErrModalOptions(error).content })
        })
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {} // reset filter
      Object.keys(search).forEach((key) => {
        if (key === 'qualified' || key === 'under_class') {
          this.filter[key + '__exact'] = search[key] === 'true' ? 'True' : 'False'
        } else if (key === 'department') {
          this.filter[key + '__exact'] = search[key]
        } else if (key === 'preview_state') {
          this.filter['preview_state'] = search[key]
        } else {
          if (key === 'studentid') {
            this.filter['student_id__contains'] = search[key]
          } else {
            this.filter[key + '__contains'] = search[key]
          }
        }
      })
      return searchNew
    },
    async beforeUpload(file) {
      try {
        const data = await getArrayFromFile(file)
        const { columns, tableData } = getTableDataFromArray(data)
        this.retakerToReset.students = this.retakerToReset.students.concat(
          tableData.map((data) => ({ userName: data['学号'] }))
        )
        this.csvColumns = columns
      } catch (err) {
        this.$Notice.warning({ title: '只能上传 CSV 文件' })
      }

      if (!this.uploadFileReady) {
        this.$Notice.warning({ title: '只能上传 CSV 文件' })
      }
    }
  }
}
</script>
