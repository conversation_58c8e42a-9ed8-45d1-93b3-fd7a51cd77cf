import { getRequest } from '@/api/util'

export const pushMsgReq = (method, params) => {
  return getRequest(`/api/push-message`, method, params)
}

export const pushMsgDetailReq = (method, pushMsgId, params) => {
  return getRequest(`/api/push-message/${pushMsgId}`, method, params)
}

export const rePushMsgDetailReq = (method, pushMsgId, params) => {
  return getRequest(`/api/redo-push-email-message/${pushMsgId}`, method, params)
}
