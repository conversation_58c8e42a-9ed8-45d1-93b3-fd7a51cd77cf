"""
declare Exam model
"""
from django.contrib.auth import get_user_model
from django.db import models

from core.models.exam import Exam
from core.models.project_in_exam import ProjectInExam
from core.models.permissions import (NEWS_VIEW, NEWS_DELETE, NEWS_CREATE, NEWS_CHANGE)


class News(models.Model):
    """This model describes news.

    此模型用于描述推送的通知。

    属性:
        created_at: DateTimeField, 创建时间
        created_by: 创建通知的用户
        active: BooleanField, 表示该通知是否通知到同学

        content: TextField 通知内容
        pie: 对应哪个pie的通知
        star: 是否置顶
    """

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        get_user_model(), on_delete=models.CASCADE)

    active = models.BooleanField(default=True, db_index=True)
    content = models.TextField(null=True)
    pie = models.ForeignKey(ProjectInExam, on_delete=models.CASCADE, null=True, db_index=True)
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, db_index=True)
    star = models.BooleanField(default=False, db_index=True)

    class Meta:
        default_permissions = ()
        permissions = [
            (NEWS_CHANGE, NEWS_CHANGE),
            (NEWS_CREATE, NEWS_CREATE),
            (NEWS_DELETE, NEWS_DELETE),
            (NEWS_VIEW, NEWS_VIEW)
        ]
