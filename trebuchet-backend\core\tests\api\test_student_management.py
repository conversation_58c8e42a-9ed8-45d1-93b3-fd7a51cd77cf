"""
test student api
"""
import json

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.test import Client, TestCase

from core.models.student import Student

_BASE_URL = 'http://localhost:8000/api/students'
USER_MODEL = get_user_model()
CLIENT = Client()


def _get(student_id):
    """get api"""
    return CLIENT.get(_BASE_URL + '/' + str(student_id))


def _put(student_id, data):
    """put api"""
    return CLIENT.put(_BASE_URL + '/' + str(student_id), json.dumps(data))


class TestStudentAPI(TestCase):
    """
    test for student api
    """

    def _get_student_dict(self, student_id: str, name: str, department: int) -> dict:
        """get a dict from given student args"""
        last_id = self.last_id
        self.last_id = last_id + 1
        return {
            'id': last_id,
            'student_id': student_id,
            'name': name,
            'department': department
        }

    def setUp(self) -> None:
        self.last_id = 0
        init_items = [
            self._get_student_dict('12345', '李狗蛋', 6),
            self._get_student_dict('56789', '孟国庆', 13),
            self._get_student_dict('1122334', 'Johnny B Goode', 244),
        ]
        for item in init_items:
            Student.objects.create(**item)

        self.init_items = init_items
        user = USER_MODEL.objects.create_user(
            username="test", password="12345")
        permission_1 = Permission.objects.get(codename="view_student")
        permission_2 = Permission.objects.get(codename="add_student")
        permission_3 = Permission.objects.get(codename="change_student")
        permission_4 = Permission.objects.get(codename="delete_student")
        user.user_permissions.add(permission_1)
        user.user_permissions.add(permission_2)
        user.user_permissions.add(permission_3)
        user.user_permissions.add(permission_4)
        auth_token = CLIENT.post(
            "http://localhost:8000/api/token-auth",
            {"username": "test", "password": "12345"}).json().get("access_token")
        CLIENT.defaults["HTTP_AUTHORIZATION"] = "Bearer" + " " + auth_token

    def assert_http_status_success(self, response):
        """
        check whether request success
        """
        try:
            self.assertTrue(200 <= response.status_code < 300)
        except AssertionError as err:
            print('status_code:', response.status_code)
            raise err

    def test_get(self):
        """
        test get api
        """
        for item in self.init_items:
            res = _get(item['id'])
            self.assert_http_status_success(res)
            data = res.json()
            print(data)
            data = {k: data[k] for k in item}
            self.assertDictEqual(data, item)

    def test_put(self):
        """
        test put api
        """
        put_data_list = [
            {'name': '奥特曼'},
            {'name': 'Jack', 'department': 200},
        ]
        for item in self.init_items:
            for put_data in put_data_list:
                res = _put(item['id'], put_data)
                self.assert_http_status_success(res)

                res = _get(item['id'])
                self.assert_http_status_success(res)
                data = res.json()
                data = {k: data[k] for k in put_data}
                self.assertDictEqual(data, put_data)
                # TODO: complete test

    def test_put_duplicated(self):
        """test the duplicated put"""
        put_data_1 = self.init_items[0]
        res = _put(put_data_1['id'], put_data_1)
        self.assert_http_status_success(res)
