import Main from '@/view/index/main'

export const userRouter = {
  path: '/user',
  name: 'user',
  component: Main,
  meta: {
    title: '用户管理',
    icon: 'ios-settings',
    jumpRoute: '/user/user-table'
  },
  children: [
    {
      path: 'create-user',
      name: 'create_user',
      meta: {
        title: '创建用户'
      },
      component: () => import('@/view/user/user-create')
    },
    {
      path: 'user-table',
      name: 'user_table',
      meta: {
        title: '用户列表'
      },
      component: () => import('@/view/user/user-table')
    },
    {
      path: 'user-detail/:id',
      name: 'user_detail',
      meta: {
        title: '用户详情',
        hideInMenu: true
      },
      component: () => import('@/view/user/user-detail')
    },
    {
      path: 'user-detail-self/',
      name: 'user_detail_self',
      meta: {
        title: '用户详情',
        hideInMenu: true
      },
      component: () => import('@/view/user/user-detail-self')
    },
    {
      path: 'update-password',
      name: 'update_password',
      meta: {
        title: '修改密码',
        hideInMenu: true
      },
      component: () => import('@/view/user/password-update')
    },
    {
      path: 'create-group',
      name: 'create_group',
      meta: {
        title: '创建权限组'
      },
      component: () => import('@/view/user/group-create')
    },
    {
      path: 'group-table',
      name: 'group_table',
      meta: {
        title: '权限组列表'
      },
      component: () => import('@/view/user/group-table')
    },
    {
      path: 'group-detail/:id',
      name: 'group_detail',
      meta: {
        title: '权限组详情',
        hideInMenu: true
      },
      component: () => import('@/view/user/group-detail')
    }
  ]
}
