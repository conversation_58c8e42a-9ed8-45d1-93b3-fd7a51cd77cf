"""
declare Room model
"""

from django.db import models

from core.models.permissions import (ROOM_CHANGE, ROOM_CREATE, ROOM_DELETE,
                                     ROOM_VIEW)


class Room(models.Model):
    """Describe a classroom in physical world.

    Attributes:
        name -- <PERSON> <PERSON><PERSON><PERSON><PERSON> saving the room's name
        available -- A <PERSON><PERSON><PERSON><PERSON><PERSON>. Whether the room is available
        comment -- A TextField saving extra comments/details of current room
    """
    name = models.CharField(max_length=50, unique=True)
    available = models.BooleanField(default=True)
    comment = models.TextField(default='')

    class Meta:
        # no default permissions needed
        default_permissions = ()
        permissions = [
            (ROOM_CREATE, ROOM_CREATE),
            (ROOM_VIEW, ROOM_VIEW),
            (ROOM_CHANGE, ROOM_CHANGE),
            (ROOM_DELETE, ROOM_DELETE)
        ]
