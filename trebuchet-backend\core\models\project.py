"""
declare Project model
"""

from django.db import models

from core.models.course import Course
from core.models.permissions import (PROJECT_CHANGE, PROJECT_CREATE,
                                     PROJECT_DELETE, PROJECT_VIEW)
from core.models.student import Student


class Project(models.Model):
    """This model describes project.

    Project is different from Exam, and it is about teaching content.
    For example, P7 represents "Exception Handling".

    Attributes:
        name: A <PERSON>rField storing the name of project.
        course: A ForeignKey to Course model storing the belongship of project.
        parent_project: A ForeignKey to Project model storing the pre-order project.
        depth: A IntegerField storing the depth of current item in the course. (0-based index)
        student_whitelist: A ManyToManyField storing the students allowed to enter this project.
    """
    name = models.CharField(max_length=50)
    course = models.ForeignKey(to=Course, on_delete=models.PROTECT)
    parent_project = models.ForeignKey(
        to='self', on_delete=models.SET_NULL, null=True)
    depth = models.IntegerField()
    student_whitelist = models.ManyToManyField(to=Student, blank=True)
    problems = models.ManyToManyField(to='judge.problem')

    class Meta:
        default_permissions = ()
        permissions = [
            (PROJECT_CHANGE, PROJECT_CHANGE),
            (PROJECT_CREATE, PROJECT_CREATE),
            (PROJECT_DELETE, PROJECT_DELETE),
            (PROJECT_VIEW, PROJECT_VIEW)
        ]
