"""Students' progress
"""
import datetime
from collections import defaultdict
from functools import partial

import pandas
from django.core.exceptions import FieldError, ObjectDoesNotExist
from django.core.paginator import Paginator
from django.db.models import <PERSON>oleanField, Case, F, Q, QuerySet, Value, When, Count, OuterRef, Exists, Subquery
from django.forms import model_to_dict
from django.http import HttpRequest, HttpResponse
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.permissions import (CORE_EXAM_RECORD_VIEW, CORE_PROGRESS_CHANGE,
                                  CORE_PROGRESS_VIEW, CORE_EXAM_RECORD_STATISTICS)
from core.api.query_utils import query_filter, query_order_by, query_page
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, wrapped_api, require_course_permission)
from core.interface.check_in_and_out import student_check_in_exam_interface
from core.models.course import Course
from core.models.exam import Exam
from core.models.exam_record import GRADE_F, ExamRecord
from core.models.instructor_class import InstructorClass
from core.models.project import Project
from core.models.project_in_exam import ProjectInExam
from core.models.student import Student
from core.models.student_progress import StudentProgress
from core.models.user_profile import UserProfile
from judge.interface.problem_judge_record import (
    get_last_submit_of_students, get_problem_final_judge_result_for_core, non_submit_checker)


class RequirementParser:
    """helper class for parsing requirement
    """

    def __init__(self, data: str, func):
        """initialize with requirement data and judge function
        """
        data += "\0"
        self.data = data
        self.cur = 0
        self.func = func

    def get_token(self):
        """lexer
        """
        while self.data[self.cur].isspace():
            self.cur += 1
        if self.data[self.cur] in ['&', '|', 'V', '(', ')']:
            self.cur += 1
            return self.data[self.cur - 1]
        if self.data[self.cur].isdigit():
            stringstream = ""
            while self.data[self.cur].isdigit():
                stringstream += self.data[self.cur]
                self.cur += 1
            return int(stringstream)
        # updated by Dailan He
        #
        # return None
        #
        # updated by Dailan He
        #
        # return self.data[self.cur]
        #
        raise ValueError('unknown token ' + self.data[self.cur])

    def match(self, character):
        """'eat' token
        """
        next_token = self.get_token()
        if character != next_token:
            raise ValueError("not matched. expected:'{}', actual:'{}'"
                             .format(character, next_token))

    def parse_expr(self) -> bool:
        """parser: expr
        """
        self.match('(')
        value = self.parse_term()
        self.match(')')
        return value

    def parse_term(self) -> bool:
        """parser: term
        """
        token = self.get_token()
        if token == '&':
            flag = self.parse_expr() & self.parse_expr()
        elif token == '|':
            flag = self.parse_expr() | self.parse_expr()
        elif token == 'V':
            flag = self.parse_v()
        elif isinstance(token, int):
            # TODO make it sure
            flag = self.func(problem_id=token)
        elif token == ')':
            # when reach here
            # the parser get an empty exp '()'
            # so should return true
            self.cur -= 1  # because the caller will match(')') later
            return True
        else:
            raise ValueError('unknown token \'' + token + "'")
        return flag

    def parse_v(self) -> bool:
        """parser: V m n <expr>^m
        """
        problem_count = self.get_token()
        pass_count = self.get_token()
        if not isinstance(problem_count, int) or not isinstance(pass_count, int):
            raise ValueError
        cnt = 0
        for _ in range(problem_count):
            if self.parse_expr():
                cnt += 1
        if cnt >= pass_count:
            return True
        return False


def parse_requirement(data: str, username: str, pie_id=None) -> bool:
    """parse requirement and return judge result
    """
    # TODO::
    func = partial(fetch_student_problem_judge_record, username=username, pie_id=pie_id)
    lexer = RequirementParser(data, func)
    # 2018-10-09 updated by Dailan He
    # return None can cause another error later
    # for readablity we can raise the ValueError directly
    # try:
    value = lexer.parse_expr()
    # except ValueError:
    #     return None
    return value


def parse_requirement_with_dict_data(requirement: str, data: dict) -> bool:
    """parse requirement with string requirement and dict judge data
    """

    def func(problem_id):
        return data.get(problem_id, False)

    parser = RequirementParser(requirement, func)
    value = parser.parse_expr()
    return value


def fetch_student_problem_judge_record(username: str, problem_id: int, pie_id=None) -> bool:
    """fetch specified judge result from judge db

    Args:
        username (str): edx username, which is stored in Student.student_id
        problem_id (int): problem id
    """
    return get_problem_final_judge_result_for_core(username, problem_id, pie_id)


def is_student_passed_exam(student: Student, project_in_exam_id: int) -> bool:
    """fetch specified judge result from oa db

    Args:
        student_id (int): Student pk
        project_id (int): ProjectInExam pk
    """
    record_filter = Q(**{"student": student}) \
                    & Q(**{"project_in_exam__id__exact": project_in_exam_id})
    record = ExamRecord.objects.filter(record_filter).order_by("-id").first()
    # better than F
    return record.check_result > GRADE_F


def get_students_in_course(course: Course, include_retakers=True):
    """fetch students' id list in specified course

    Args:
        course (Course): target course
    """
    result = []
    class_set = course.instructorclass_set.all()
    for class_instance in class_set:
        students = class_instance.student.all()
        result = result + list(students.values_list("id", flat=True))

    if not include_retakers:
        retakers = course.retake_students.all()
        result = list(set(result) - set(retakers.values_list("id", flat=True)))

    return result


def get_students_in_course_by_class(course: Course, include_retakers=True):
    """fetch students' id list in specified course

    Args:
        course (Course): target course
    """
    result = {}
    class_set = course.instructorclass_set.all()
    retakers = course.retake_students.all()
    for class_instance in class_set:
        students = class_instance.student.all()
        # 这里的id不是学号，是db-id
        for student_id in students.values_list("id", flat=True):
            if student_id in retakers and include_retakers:
                if class_instance.teacher not in result:
                    result[class_instance.teacher] = []
                result[class_instance.teacher].append(student_id)
            elif student_id not in retakers:
                if class_instance.teacher not in result:
                    result[class_instance.teacher] = []
                result[class_instance.teacher].append(student_id)

    return result


def check_retaker_progress(course_id: int, student_id: int, selected_course_id_list: list):
    if not Course.objects.get(pk=course_id).retake_students.all().filter(student_id=student_id).exists():
        return "Sorry, the student " + str(student_id) + " isn't a retaker of current course."
    last_course_id = 0
    for last_course_id in selected_course_id_list:
        try:
            last_course = Course.objects.get(pk=last_course_id)
        except ObjectDoesNotExist:
            continue
        if last_course.retake_students.all().filter(student_id=student_id).exists():
            break
    if last_course_id == 0:
        return "Sorry, the student " + str(student_id) + " hasn't been a retaker."
    for last_exam in Exam.objects.filter(course_id=last_course_id).all():
        pie_list = ProjectInExam.objects.filter(exam_id=last_exam.pk).all()
        for pie in pie_list:
            if ExamRecord.objects.filter(
                    project_in_exam__id=pie.pk,
                    student__student_id=student_id,
                    check_result__gt=-1,
            ).exists():
                return "Sorry, the student " + str(student_id) + " progress was updated during last retake course."
    return False


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_POST
def initialize_retake_progress(request: HttpRequest, course_id: int):
    """initialize retake students' progress at the beginning of course

    [method]: POST

    [route]: /api/progress/<int:course_id>/retake-init
    """
    course = Course.objects.filter(pk=course_id).first()
    if course is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND,
                                   "Sorry, the course is non-existent.")
    retakers = course.retake_students.all()
    failed_list: list = []
    existed_list: list = []
    for retaker in retakers:
        if StudentProgress.objects.filter(student=retaker.id, course=course).first() is not None:
            existed_list.append(retaker.student_id)
            continue
        old_progress = StudentProgress.objects.filter(student=retaker.id).order_by("-course__pk").first()
        if old_progress is None:
            failed_list.append(retaker.student_id)
            continue
        if old_progress.current_project.exam == old_progress.course.under_class_exam:
            project_name = old_progress.current_project.project.name
            project = course.under_class_exam.project.filter(name=project_name).first()
            if project is None:
                return failed_api_response(ErrorCode.ITEM_NOT_FOUND,
                                           "Sorry, the project {} is non-existent.".format(project_name))
            project_in_exam = ProjectInExam.objects.filter(exam=course.under_class_exam, project=project.id).first()
        else:  # retake student is not at at under class exam, cannot handle
            return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                       "Sorry, {} this student progress is not at under class exam." \
                                       .format(old_progress.student.student_id))
        StudentProgress.objects.create(**{
            "student": retaker,
            "course": course,
            "current_project": project_in_exam
        })
    data = {
        "result": "there were {} retakers, {} of them were failed to initialize, {} of them were existed" \
            .format(len(retakers), len(failed_list), len(existed_list)),
        "failed_list": failed_list
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_POST
def initialize_progress(request: HttpRequest, course_id: int):
    """initialize students' progress at the beginning of course

    [method]: POST

    [route]: /api/progress/<int:course_id>/init
    """
    data: dict = parse_data(request)
    project_in_exam_id = data.get("project_in_exam")
    if project_in_exam_id is None:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Sorry, project_in_exam is necessary.")
    project_in_exam = ProjectInExam.objects.filter(pk=project_in_exam_id).first()
    if project_in_exam is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND,
                                   "Sorry, the project-in-exam is non-existent.")

    course = Course.objects.filter(pk=course_id).first()
    if course is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND,
                                   "Sorry, the course is non-existent.")

    if project_in_exam.exam != course.under_class_exam:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Sorry, project_in_exam must belong to under_class exam.")

    existed_progress = set(list(StudentProgress.objects.filter(course=course).values_list("student__id", flat=True)))

    students = set(get_students_in_course(course, include_retakers=False))
    for student_id in students - existed_progress:
        student = Student.objects.get(pk=student_id)
        StudentProgress.objects.create(**{
            "student": student,
            "course": course,
            "current_project": project_in_exam
        })
    return success_api_response({"result": "Ok, progress has been initialized."})


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_POST
def reset_retake_progress(request: HttpRequest, course_id: int):
    """重置给定轮次内最近一次课程无进度更新的重修生进度

    [method]: POST

    [route]: /api/progress/<int:course_id>/reset-retake-progress
    """
    if not Course.objects.filter(pk=course_id).exists():
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND,
                                   "Sorry, the course_id is illegal.")
    data = parse_data(request)
    selected_course_id_list = sorted(data.get('selected_course_id'), reverse=True)
    selected_student_id_list = data.get('selected_student_id')
    student_list = []
    for student_obj in selected_student_id_list:
        student = Student.objects.get(student_id=student_obj["userName"])
        check_result = check_retaker_progress(course_id, student.student_id, selected_course_id_list)
        if check_result:
            return failed_api_response(ErrorCode.ITEM_NOT_FOUND, check_result)
        student_list.append(student)
    reset_project = Project.objects.get(course_id=course_id, name=data.get('project_name'))
    reset_exam = Course.objects.get(pk=course_id).under_class_exam
    reset_pie = ProjectInExam.objects.filter(project_id=reset_project.pk, exam_id=reset_exam.pk).first()
    for student_obj in student_list:
        retaker_progress = StudentProgress.objects.filter(course_id=course_id, student=student_obj).first()
        if retaker_progress is not None:
            retaker_progress.current_project = reset_pie
        else:
            retaker_progress = StudentProgress(student=student_obj,
                                               course_id=course_id,
                                               current_project=reset_pie,
                                               qualified=False)
        retaker_progress.save()
    return success_api_response({"result": "Ok, all retakers' progress has been reset."})


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_course_permission
def get_qualification_list_of_pie(request: HttpRequest, course: Course, *args, **kwargs):
    pie_id = request.GET.dict()["pie_id"]
    pie = ProjectInExam.objects.get(pk=pie_id)
    qualified_list = {}
    req = pie.pass_requirement
    for cls in InstructorClass.objects.filter(belong_to=course):
        for stu in cls.student.all():
            is_retaker = False
            if course.retake_students.filter(student_id=stu.student_id).exists():
                is_retaker = True
            qualified = parse_requirement(req, stu.student_id, pie.id)
            qualified_list[stu.student_id] = {"qualified": qualified, "is_retaker": is_retaker}
    return success_api_response(qualified_list)


def refresh_under_class_progress(course: Course, force: bool):
    """更新course中所有当前PIE为课下的学生的StudentProgress中的qualified字段
    """
    for progress in StudentProgress.objects.filter(course=course):
        if force or not progress.qualified:
            req = progress.current_project.pass_requirement
            if req is None or req == "":
                raise AssertionError(
                    "project_in_exam ID {} 通过条件设置为空，请检查".format(progress.current_project.id))

            progress.qualified = parse_requirement(
                req,
                progress.student.student_id,
                progress.current_project.id
            )

            req = progress.current_project.mark_requirement
            if req is None or req == "":
                req = progress.current_project.pass_requirement
            progress.under_class_mark = parse_requirement(
                req,
                progress.student.student_id,
                progress.current_project.id
            )

            progress.save()


def _refresh_specified_student_under_class_progress(progress, force):
    if force or not progress.qualified:
        progress.qualified = parse_requirement(
            progress.current_project.pass_requirement,
            progress.student.student_id
        )
        progress.save()


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_POST
def update_students_under_class_progress(request: HttpRequest, course_id: int):
    """update students' under class progress before exam

    [method]: POST

    [route]: /api/progress/<int:course_id>/underclass
    """
    course = Course.objects.filter(pk=course_id).first()
    if not course:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND,
                                   "Sorry, the course is non-existent.")

    force = request.GET.get("force")
    refresh_under_class_progress(course, force)

    return success_api_response({"result": "Ok, under class progress has been updated."})


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_POST
def check_in_exam(request: HttpRequest, course_id: int, student_id: int):
    """update student's in exam progress

    [method]: POST

    [route]: /api/progress/<int:course_id>/exam/<int:student_id>
    """
    raise NotImplementedError


def query_filter_student_id_helper(filter_key, filter_value):
    """helper function
    """
    key = "student__student_id__" + filter_key.split("__")[-1]
    return Q(**{key: filter_value})


def query_filter_student_name_helper(filter_key, filter_value):
    """helper function
    """
    key = "student__name__" + filter_key.split("__")[-1]
    return Q(**{key: filter_value})


def query_filter_department_helper(filter_key, filter_value):
    """helper function
    """
    key = "student__department__" + filter_key.split("__")[-1]
    return Q(**{key: filter_value})


def query_filter_project_name_helper(filter_key, filter_value):
    """helper function
    """
    key = "current_project__project__name__" + filter_key.split("__")[-1]
    return Q(**{key: filter_value})


def query_filter_under_class_helper(filter_key, filter_value):
    """helper function
    """
    key = "under_class__" + filter_key.split("__")[-1]
    return Q(**{key: filter_value})


def query_filter_teacher_name_helper(filter_key, filter_value):
    """helper function
    """
    key = "teacher_name__" + filter_key.split("__")[-1]
    return Q(**{key: filter_value})


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Course, field="id", item="course_id")
@query_filter(fields=[("student_id", str),
                      ("student_name", str),
                      ("department", int),
                      ("current_project_name", str),
                      ("qualified", bool),
                      ("under_class", bool)],
              custom={"student_id": query_filter_student_id_helper,
                      "student_name": query_filter_student_name_helper,
                      "department": query_filter_department_helper,
                      "current_project_name": query_filter_project_name_helper,
                      "under_class": query_filter_under_class_helper,
                      })
@query_order_by(fields=["qualified"])
@query_page(default=10)
def list_student_progress(request: HttpRequest, course_id: int, *args, **kwargs):
    """list student progress

    [method]: GET

    [route]: /api/progress/<int:course_id>
    """
    course: Course = Course.objects.get(pk=course_id)
    under_class_exam = course.under_class_exam
    progress_all = StudentProgress.objects.count()
    progress = StudentProgress.objects \
        .annotate(student_name=F("student__name")) \
        .annotate(studentid=F("student__student_id")) \
        .annotate(department=F("student__department")) \
        .annotate(current_project_name=F("current_project__project__name")) \
        .annotate(under_class=(Case(When(current_project__exam=under_class_exam, then=Value(True)),
                                    default=Value(False),
                                    output_field=BooleanField()))) \
        .filter(course=course)
    project_info = {}
    for project in under_class_exam.project.all():
        progress_with_project = progress.filter(
            current_project__project=project)
        progress_not_passed = progress_with_project.filter(
            under_class=True, qualified=False)
        count_all = progress_with_project.count()
        count_not_passed = progress_not_passed.count()
        project_info[project.name] = {
            "all": count_all,
            "passed": count_all - count_not_passed
        }

    progress_filter = kwargs.get("filter")
    try:
        progress = progress.filter(progress_filter)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    order_by = kwargs.get("order_by")
    if order_by is not None:
        order_by.append("id")
        progress = progress.order_by(*order_by)
    else:
        progress = progress.order_by("-qualified", "id")

    progress_details = list(progress.values(
        "id", "studentid", "student_name", "current_project_name", "qualified",
        "department", "under_class"))
    edx_username_list = [detail.get("studentid")
                         for detail in progress_details]
    last_submits = get_last_submit_of_students(edx_username_list)
    for detail in progress_details:
        last_submit = last_submits.get(detail["studentid"])
        if last_submit is None:
            detail["last_submit_at"] = None
            detail["problem_name"] = None
        else:
            detail["last_submit_at"] = last_submit.get("submitted_at")
            detail["problem_name"] = last_submit.get("problem_name")
        student = Student.objects.filter(student_id=detail["studentid"]).first()
        detail["last_login_at"] = student.user.last_login
        detail["preview_state"] = "未登录" if detail["last_login_at"] is \
                                              None else "未提交" if detail["last_submit_at"] is None else "已提交"

    preview_state = request.GET.get("preview_state")
    if preview_state is not None:
        def filter_func(detail):
            return detail["preview_state"] == preview_state

        progress_details = list(filter(filter_func, progress_details))

    page = kwargs.get("page")
    page_size = kwargs.get("page_size")
    paginator = Paginator(progress_details, page_size)
    page_all = paginator.num_pages
    if page > page_all:
        new_progress_details = []
    else:
        new_progress_details = list(paginator.get_page(page))
    progress_list_data = {
        "progress_all": progress_all,
        "total_count": paginator.count,
        "page_all": page_all,
        "page_now": page,
        "progress": new_progress_details,
        "projects": project_info
    }
    return success_api_response(progress_list_data)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_POST
def set_student_progress(request: HttpRequest, course_id: int):
    """直接设置学生进度

    [method]: POST

    [route]: /api/progress/<int:course_id>
    """
    course = Course.objects.filter(pk=course_id).first()
    if not course:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "No Such Course")

    data: dict = parse_data(request)
    student_id = data["student_id"]
    project_id = data["project_id"]
    under_class = data["under_class"]
    project = Project.objects.filter(pk=project_id).first()
    progress = StudentProgress.objects.filter(course=course, student__student_id=student_id).first()
    progress.qualified = False
    progress.under_class_mark = False
    if under_class:
        progress.current_project = ProjectInExam.objects.filter(
            exam=course.under_class_exam, project=project).first()
    else:
        course_id = UserProfile.objects.filter(user=request.user).first().course_id
        exam: Exam = Exam.objects.filter(course_id=course_id, active=True).first()
        progress.current_project = ProjectInExam.objects.filter(exam=exam, project=project).first()
    progress.save()
    return success_api_response({'success': True})


def get_achievement(info: list):
    for element in info:
        if element["under_class"]:
            if element["under_class_mark"]:
                element["achieve"] = element["current_project_name"] + "课下完成"
            else:
                element["achieve"] = "无完成记录" if element["parent_project_name"] is None \
                    else (element["parent_project_name"] + "课上完成")
        else:
            if element["qualified"]:
                element["achieve"] = element["current_project_name"] + "课上完成"
            elif element["under_class_mark"]:
                element["achieve"] = element["current_project_name"] + "课下完成"
            else:
                element["achieve"] = "无完成记录" if element["parent_project_name"] is None \
                    else (element["parent_project_name"] + "课上完成")
    return info


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Course, field="id", item="course_id")
@query_filter(fields=[("student_id", str),
                      ("student_name", str),
                      ("department", int),
                      ("current_project_name", str),
                      ("qualified", bool),
                      ("under_class", bool),
                      ("teacher_name", str)],
              custom={"student_id": query_filter_student_id_helper,
                      "student_name": query_filter_student_name_helper,
                      "department": query_filter_department_helper,
                      "current_project_name": query_filter_project_name_helper,
                      "under_class": query_filter_under_class_helper,
                      "teacher_name": query_filter_teacher_name_helper,
                      })
@query_order_by(fields=["qualified"])
@query_page(default=10)
def list_student_progress_achievement(request: HttpRequest, course_id: int, *args, **kwargs):
    """list student achievement

    [method]: GET

    [route]: /api/progress/<int:course_id>/achievement
    """
    course: Course = Course.objects.get(pk=course_id)
    under_class_exam = course.under_class_exam
    progress = StudentProgress.objects \
        .annotate(student_name=F("student__name")) \
        .annotate(studentid=F("student__student_id")) \
        .annotate(department=F("student__department")) \
        .annotate(current_project_name=F("current_project__project__name")) \
        .annotate(parent_project_name=F("current_project__project__parent_project__name")) \
        .annotate(under_class=(Case(When(current_project__exam=under_class_exam, then=Value(True)),
                                    default=Value(False),
                                    output_field=BooleanField()))) \
        .annotate(teacher_name=Subquery(InstructorClass.objects
                                        .filter(belong_to=OuterRef("course"),
                                                student=OuterRef("student"))
                                        .values("teacher"))) \
        .filter(course=course)
    progress_filter = kwargs.get("filter")
    try:
        progress = progress.filter(progress_filter)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    order_by = kwargs.get("order_by")
    if order_by is not None:
        order_by.append("id")
        progress = progress.order_by(*order_by)
    else:
        progress = progress.order_by("-qualified", "id")
    page = kwargs.get("page")
    page_size = kwargs.get("page_size")
    paginator = Paginator(progress, page_size)
    page_all = paginator.num_pages
    if page > page_all:
        achievement_details = []
    else:
        achievement_details = list(paginator.get_page(page).object_list.values(
            "id", "studentid", "student_name", "department", "current_project_name", "parent_project_name",
            "qualified", "under_class_mark", "under_class", "teacher_name"))
    achievement_details = get_achievement(achievement_details)
    ans = {
        "total_count": paginator.count,
        "page_all": page_all,
        "page_now": page,
        "achievements": achievement_details
    }
    return success_api_response(ans)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Course, field="id", item="course_id")
@query_filter(fields=[("student_id", str),
                      ("student_name", str),
                      ("current_project_name", str),
                      ("qualified", bool),
                      ("under_class", bool)],
              custom={"student_id": query_filter_student_id_helper,
                      "student_name": query_filter_student_name_helper,
                      "current_project_name": query_filter_project_name_helper})
@query_order_by(fields=["qualified"])
def list_student_progress_csv_output(request: HttpRequest, course_id: int, *args, **kwargs):
    """list student progress as csv

    [method]: GET

    [route]: /api/progress/<int:course_id>/csv
    """
    course: Course = Course.objects.get(pk=course_id)
    under_class_exam = course.under_class_exam
    progress = StudentProgress.objects \
        .annotate(student_name=F("student__name")) \
        .annotate(studentid=F("student__student_id")) \
        .annotate(department=F("student__department")) \
        .annotate(current_project_name=F("current_project__project__name")) \
        .annotate(under_class=(Case(When(current_project__exam=under_class_exam, then=Value(True)),
                                    default=Value(False),
                                    output_field=BooleanField()))) \
        .filter(course=course)

    progress_filter = kwargs.get("filter")
    try:
        progress = progress.filter(progress_filter)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    order_by = kwargs.get("order_by")
    if order_by is not None:
        order_by.append("id")
        progress = progress.order_by(*order_by)
    else:
        progress = progress.order_by("-qualified", "id")
    progress_infos = progress.values(
        "studentid", "student_name", "department", "current_project_name", "under_class", "qualified")

    if len(progress_infos) > 0:
        edx_username_list = []

        for info in progress_infos:
            info["student_id"] = info.pop("studentid")
            edx_username_list.append(info["student_id"])
        last_submits = get_last_submit_of_students(edx_username_list)
        for info in progress_infos:
            last_submit = last_submits.get(info["student_id"])
            if last_submit is None:
                info["last_submit_at"] = None
                info["problem_name"] = None
            else:
                info["last_submit_at"] = last_submit.get("submitted_at")
                info["problem_name"] = last_submit.get("problem_name")
        data_frames = pandas.DataFrame(progress_infos)
        meta_info_columns = ["student_id", "student_name", "department", "current_project_name",
                             "under_class", "qualified", "last_submit_at", "problem_name"]
        data_frames = data_frames[meta_info_columns]
        ret_data = data_frames.to_csv()
    else:
        ret_data = ""

    return HttpResponse(ret_data)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Course, field="id", item="course_id")
@query_filter(fields=[("student_id", str),
                      ("student_name", str),
                      ("current_project_name", str),
                      ("qualified", bool),
                      ("under_class", bool)],
              custom={"student_id": query_filter_student_id_helper,
                      "student_name": query_filter_student_name_helper,
                      "current_project_name": query_filter_project_name_helper})
@query_order_by(fields=["qualified"])
def list_student_progress_achievement_csv_output(request: HttpRequest, course_id: int, *args, **kwargs):
    """list student achievement as csv

    [method]: GET

    [route]: /api/progress/<int:course_id>/achievement/csv
    """
    course: Course = Course.objects.get(pk=course_id)
    under_class_exam = course.under_class_exam
    progress = StudentProgress.objects \
        .annotate(student_name=F("student__name")) \
        .annotate(studentid=F("student__student_id")) \
        .annotate(department=F("student__department")) \
        .annotate(current_project_name=F("current_project__project__name")) \
        .annotate(parent_project_name=F("current_project__project__parent_project__name")) \
        .annotate(under_class=(Case(When(current_project__exam=under_class_exam, then=Value(True)),
                                    default=Value(False),
                                    output_field=BooleanField()))) \
        .annotate(teacher_name=Subquery(InstructorClass.objects
                                        .filter(belong_to=OuterRef("course"),
                                                student=OuterRef("student"))
                                        .values("teacher"))) \
        .filter(course=course)

    progress_filter = kwargs.get("filter")
    try:
        progress = progress.filter(progress_filter)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    order_by = kwargs.get("order_by")
    if order_by is not None:
        order_by.append("id")
        progress = progress.order_by(*order_by)
    else:
        progress = progress.order_by("-qualified", "id")
    achievement_infos = progress.values("studentid", "student_name", "department",
                                        "current_project_name", "parent_project_name", "under_class", "qualified",
                                        "under_class_mark", "teacher_name")
    achievement_infos = get_achievement(achievement_infos)

    if len(achievement_infos) > 0:
        edx_username_list = []

        for info in achievement_infos:
            info["student_id"] = info.pop("studentid")
            edx_username_list.append(info["student_id"])

        data_frames = pandas.DataFrame(achievement_infos)
        meta_info_columns = ["student_id", "student_name", "department", "current_project_name", "achieve",
                             "teacher_name"]
        data_frames = data_frames[meta_info_columns]
        ret_data = data_frames.to_csv()
    else:
        ret_data = ""

    return HttpResponse(ret_data)


def get_next_in_class_project_in_exam_with_course(current_project_in_exam: ProjectInExam, course_id: int):
    """获取课下PIE的对应课上PIE
    """
    exam: Exam = Exam.objects.filter(active=True, course_id=course_id).first()
    project_in_exam: ProjectInExam = ProjectInExam.objects.filter(exam=exam,
                                                                  project=current_project_in_exam.project).first()
    return project_in_exam


# def get_next_in_class_project_in_exam(current_project_in_exam: ProjectInExam):
#     """获取课下PIE的对应课上PIE
#     """
#     exam: Exam = Exam.objects.filter(active=True).first()
#     project_in_exam: ProjectInExam = ProjectInExam.objects.filter(exam=exam,
#                                                                   project=current_project_in_exam.project).first()
#     return project_in_exam


def get_next_under_class_project_in_exam(student: Student, current_project: Project):
    """get next under class pie by whitelist
    """
    next_project_set: QuerySet = current_project.project_set.all()
    for project in next_project_set:
        if student in project.student_whitelist.all():
            return ProjectInExam.objects.filter(exam=project.course.under_class_exam, project=project).first()
    return None


def _get_next_project(course_pk: int, student_pk: int):
    student: Student = Student.objects.filter(id=student_pk).first()
    course: Course = Course.objects.filter(id=course_pk).first()
    current_project_in_exam: ProjectInExam = student.studentprogress_set. \
        filter(course=course).first().current_project
    current_project: Project = current_project_in_exam.project
    next_project_set: QuerySet = current_project.project_set.all()
    next_project = None
    for project in next_project_set:
        temp = list(model_to_dict(project)['student_whitelist'])
        if student in temp:
            next_project = project
            break
    return next_project


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_http_methods(["PUT"])
@require_item_exist(model=Course, field="id", item="course_pk")
def push_all_student_before_exam(request: HttpRequest, course_pk: int):
    """考前推进全部学生Project进度

    [method]: PUT

    [route]: /api/push-all-student-before-exam/<int:course_pk>
    """
    course = Course.objects.get(id=course_pk)
    under_class_exam = course.under_class_exam
    refresh_under_class_progress(course, True)

    for progress in StudentProgress.objects.filter(course=course):
        if progress.current_project.exam != under_class_exam:
            continue
        if not progress.qualified:
            continue
        course_id = UserProfile.objects.filter(user=request.user).first().course_id
        next_project_in_exam: ProjectInExam = get_next_in_class_project_in_exam_with_course(
            progress.current_project, course_id)
        if next_project_in_exam is None:
            continue
        progress.current_project = next_project_in_exam
        progress.qualified = False
        progress.save()
        ExamRecord.objects.create(
            **{"student": progress.student, "project_in_exam": next_project_in_exam})

    return success_api_response({"success": True})


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_http_methods(["PUT"])
def push_all_student_after_exam(request: HttpRequest, course_pk: int):
    """考后推进学生Project进度

    [method]: PUT

    [route]: /api/push-all-student-after-exam/<int:course_pk>
    """
    course = Course.objects.filter(pk=course_pk).first()
    if not course:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "No Such Course")

    under_class_exam = course.under_class_exam
    info = []
    for progress in StudentProgress.objects.filter(course=course).exclude(current_project__exam=under_class_exam):
        if is_student_passed_exam(progress.student, progress.current_project.id):
            progress.under_class_mark = False
            next_project_in_exam: ProjectInExam = get_next_under_class_project_in_exam(
                progress.student,
                progress.current_project.project
            )
            if next_project_in_exam is None:
                # TODO error msg
                continue
        else:
            next_project_in_exam = ProjectInExam.objects.filter(exam=under_class_exam,
                                                                project=progress.current_project.project).first()
        info.append({
            "name": progress.student.name,
            "student_id": progress.student.student_id,
            "from": progress.current_project.project.name + "课上",
            "to": next_project_in_exam.project.name + "课下"
        })
        progress.current_project = next_project_in_exam
        progress.qualified = False
        progress.save()
    return success_api_response({
        "success": True,
        "total_count": len(info),
        "info": info
    })


def _push_specified_student_after_exam(course: Course, student: Student, progress: StudentProgress):
    under_class_exam = course.under_class_exam
    if progress.current_project.exam == under_class_exam:
        return (False, ErrorCode.REFUSE_ACCESS_ERROR, "The student is at underclass")
    if not is_student_passed_exam(student, progress.current_project.id):
        return (False, ErrorCode.REFUSE_ACCESS_ERROR, "The student has not passed the exam")
    next_project_in_exam = get_next_under_class_project_in_exam(
        student, progress.current_project.project)
    if next_project_in_exam is None:
        return (False, ErrorCode.REFUSE_ACCESS_ERROR, "No Next UnderClass Project")
    if not parse_requirement(next_project_in_exam.pass_requirement, student.student_id):
        return (False, ErrorCode.REFUSE_ACCESS_ERROR, "The student has not passed underclass exam")
    next_in_class_pie = get_next_in_class_project_in_exam_with_course(next_project_in_exam, course.id)
    if next_in_class_pie is None:
        return (False, ErrorCode.REFUSE_ACCESS_ERROR, "No Next In-Exam Project")

    progress.current_project = next_in_class_pie
    progress.qualified = False
    progress.save()

    return (True, None, None)


def _push_specified_student_before_exam(student: Student, progress: StudentProgress):
    now_time = datetime.datetime.now(None)
    begin_time = datetime.datetime.combine(progress.current_project.exam.date, progress.current_project.begin_time)
    extend_time = int((now_time - begin_time).total_seconds() / 60)
    ExamRecord.objects.create(
        **{"student": student,
           "project_in_exam": progress.current_project,
           "extend_time": extend_time})
    return student_check_in_exam_interface(student)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_CHANGE])
@require_http_methods(["PUT"])
def skip_to_next_project_in_exam(request: HttpRequest, course_id: int, student_id: str):
    """连测功能

    [method]: PUT

    [route]: /api/progress/<int:course_id>/skip/<str:student_id>
    """
    # get student
    student = Student.objects.filter(student_id=student_id).first()
    if not student:
        return failed_api_response(ErrorCode.NOT_FOUND_ERROR, "No Such Student")

    # get course
    course = Course.objects.filter(pk=course_id).first()
    if not course:
        return failed_api_response(ErrorCode.NOT_FOUND_ERROR, "No Such Course")

    # only one active exam
    course_id = UserProfile.objects.filter(user=request.user).first().course_id
    if not course.exam_set.filter(active=True, course_id=course_id).exists():
        return failed_api_response(ErrorCode.REFUSE_ACCESS_ERROR, "No Active Exam")

    # get progress
    progress = StudentProgress.objects.filter(course=course, student=student).first()
    if not progress:
        return failed_api_response(ErrorCode.REFUSE_ACCESS_ERROR, "Please contact OA maintainer")

    # push to underclass and refresh underclass qualified
    (result, error_code, msg) = _push_specified_student_after_exam(course, student, progress)
    if result:
        # push to in-exam
        progress = StudentProgress.objects.filter(course=course, student=student).first()
        (result, error_code, msg) = _push_specified_student_before_exam(student, progress)
        if result:
            return success_api_response({"result": "Ok"})
    return failed_api_response(error_code, msg)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_VIEW])
@require_http_methods(["GET"])
@require_item_exist(model=Exam, field="id", item="exam_pk")
def get_student_progress_statistics(request: HttpRequest, exam_pk: int):
    """获取通过信息，将ExamRecord与实际通过作对比得到异常学生

    [method]: GET

    [route]: /api/progress/statistics/<int:exam_pk>
    """
    exam = Exam.objects.get(id=exam_pk)
    user_profile = UserProfile.objects.filter(user=request.user).first()
    retake_students = user_profile.course.retake_students.all()

    def get_statistics(ers):
        passed = ers.filter(check_result__gt=-1)
        failed = ers.filter(check_result=-1)
        ret = {}
        ret['passed'] = {
            'count': passed.count(),
            'error_students': list(map(
                lambda record: record.student.student_id,
                filter(
                    lambda record: not parse_requirement(
                        record.project_in_exam.pass_requirement,
                        record.student.student_id,
                        record.project_in_exam.id
                    ),
                    passed
                )
            ))
        }
        ret['failed'] = {
            'count': failed.count(),
            'error_students': list(map(
                lambda record: record.student.student_id,
                filter(
                    lambda record: parse_requirement(
                        record.project_in_exam.pass_requirement,
                        record.student.student_id,
                        record.project_in_exam.id
                    ),
                    failed
                )
            ))
        }
        return ret

    def get_statistics_pie(pie):
        ret = {}
        ers = ExamRecord.objects.filter(project_in_exam=pie)
        ret['total'] = ers.count()
        ret['passed'] = ers.filter(check_result__gt=-1).count()

        retaker_ers = ers.filter(student__in=retake_students)
        ret['retaker'] = get_statistics(retaker_ers)

        ers = ers.exclude(student__in=retake_students)
        departments = list(map(
            lambda record: record['student__department'],
            ers.values('student__department').distinct()
        ))
        for department in departments:
            department_ers = ers.filter(student__department=department)
            department_retaker_ers = retaker_ers.filter(student__department=department)
            ret[department] = {
                'normal_student': get_statistics(department_ers),
                'retaker': get_statistics(department_retaker_ers),
            }
        return ret

    pie_list = ProjectInExam.objects.filter(exam=exam)
    ret = {}
    for pie in pie_list:
        ret[pie.project.name] = get_statistics_pie(pie)
    return success_api_response(ret)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Student, field='student_id', item='student_id')
def get_student_pass_under_class(request: HttpRequest, student_id: str):
    """微信获取学生是否通过课下

    [method]: GET

    [route]: /api/wechat-get-student-pass-under-class/<str:student_id>
    """
    course_id = request.GET.get('course_id')
    course = Course.objects.get(pk=course_id)
    student: Student = Student.objects.filter(
        student_id=student_id).first()
    progress = StudentProgress.objects.filter(
        student=student, course=course).order_by("-id").first()
    under_class_exam = progress.course.under_class_exam
    project = progress.current_project.project
    pie = ProjectInExam.objects.filter(
        exam=under_class_exam, project=project).first()
    if progress is None:
        return failed_api_response(ErrorCode.REFUSE_ACCESS_ERROR, "No Progress")
    qualified = parse_requirement(pie.pass_requirement,
                                  progress.student.student_id,
                                  pie.id)
    return success_api_response({"progress": project.name, "is_passed": qualified})


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_course_permission
def get_non_submit_student_list(request: HttpRequest, course: Course, *args, **kwargs):
    """获取课程开始到现在没有课下提交的学生列表

    [method]: GET

    [route]: /api/non_submit_student
    """
    course_student_progress = StudentProgress.objects.all().filter(course_id=course.pk)
    course_students = [student_progress.student.student_id for student_progress in course_student_progress]
    non_submit_students = [Student.objects.get(student_id=student_id) for student_id in course_students
                           if non_submit_checker(course.under_class_exam.date, student_id)]
    data = []
    for student in non_submit_students:
        username = None
        email = None
        last_login = None
        if student.user is not None:
            username = student.user.username
            email = student.user.email
            last_login = student.user.last_login
        data.append({"student_id": student.student_id,
                     "student_name": student.name,
                     "student_department": student.department,
                     "student_official_class": student.official_class,
                     "student_email": email,
                     "student_username": username,
                     "student_last_login": last_login,
                     "student_last_submit": get_last_submit_of_students(student.student_id)
                     })
    return success_api_response({"data": data})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_STATISTICS])
@require_http_methods(["GET"])
@require_course_permission
def get_combined_student_progress_statistics(request: HttpRequest, course: Course):
    """获取通过信息，将ExamRecord与实际通过作对比得到异常学生

    [method]: GET

    [route]: /api/progress/statistics-combine/
    """
    exam_id_list = [int(exam_id) for exam_id in
                    list(request.GET.get('exam_id_list').replace('[', '').replace(']', '').split(','))]
    exam_list = Exam.objects.all().filter(id__in=exam_id_list)

    def get_department_info(course, retakers):
        ret = {}
        department_list = course.instructorclass_set.all().values_list('student__department', flat=True) \
            .distinct().order_by()
        all_retake_student = course.retake_students.all()
        retake_q = Q(student__in=all_retake_student)
        if not retakers:
            retake_q = ~retake_q
        for department in department_list:
            student_q = course.instructorclass_set.all() \
                .filter(Q(student__department=department), retake_q)
            ret[department] = student_q.count()
        return ret

    def get_statistics_pie(pie):

        ers = ExamRecord.objects \
            .filter(project_in_exam=pie) \
            .annotate(is_retaker=Exists(course.retake_students.filter(id=OuterRef('student_id')))) \
            .values('student__department', 'is_retaker') \
            .annotate(
            passed_count=Count('student', filter=Q(check_result__gt=-1)),
            failed_count=Count('student', filter=Q(check_result=-1))
        )
        stat_list = list(ers)

        def convert(department, is_retaker):
            result_list = filter(lambda x:
                                 x["student__department"] == department and
                                 x["is_retaker"] == is_retaker, stat_list)
            converted_list = map(lambda x: (x["passed_count"], x["failed_count"]), result_list)
            val = next(converted_list, (0, 0))
            return {"passed": {"count": val[0]}, "failed": {"count": val[1]}}

        pie_stat = {}
        for department in set(map(lambda x: x["student__department"], stat_list)):
            pie_stat[department] = {
                "normal_student": convert(department, False),
                "retaker": convert(department, True),
            }

        return pie_stat

    ret = {'department_cnt_normal': get_department_info(course, False),
           'department_cnt_retakers': get_department_info(course, True),
           'exam_statistics': {}}
    for exam in exam_list.order_by('pk'):
        ret['exam_statistics'][exam.pk] = {}
        pie_list = ProjectInExam.objects.filter(exam=exam).order_by('pk')
        for pie in pie_list:
            ret['exam_statistics'][exam.pk][pie.project.name] = get_statistics_pie(pie)
    return success_api_response(ret)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_STATISTICS])
@require_http_methods(["GET"])
@require_course_permission
def get_combined_student_progress_statistics_byclass(request: HttpRequest, course: Course):
    """获取通过信息，按班级进行分组

    [method]: GET

    [route]: /api/progress/statistics-combine-class/
    """
    id_strings = request.GET.get('exam_id_list').strip('[]').split(',')
    # 过滤空字符串并转换为整数，需要考虑为 [''] 的情况
    exam_id_list = [int(id_str) for id_str in id_strings if id_str.strip()]
    exam_list = Exam.objects.all().filter(id__in=exam_id_list)

    def get_class_info(retakers):
        ret = {}
        class_list = course.instructorclass_set.order_by()
        retake_q = Q(pk__in=course.retake_students.all())
        if not retakers:
            retake_q = ~retake_q
        for cls in class_list:
            ret[cls.teacher] = cls.student.filter(retake_q).count()
        return ret

    def get_statistics_pie(pie):
        ers = ExamRecord.objects \
            .filter(project_in_exam=pie) \
            .annotate(is_retaker=Exists(course.retake_students.filter(id=OuterRef('student_id')))) \
            .annotate(teacher=Subquery(InstructorClass.objects
                                       .filter(belong_to=course, student__id=OuterRef("student_id"))
                                       .values("teacher"))) \
            .values('teacher', 'is_retaker') \
            .annotate(
            passed_count=Count('student', filter=Q(check_result__gt=-1)),
            failed_count=Count('student', filter=Q(check_result=-1))
        )
        stat_list = list(ers)

        def convert(teacher, is_retaker):
            result_list = filter(lambda x:
                                 x["teacher"] == teacher and
                                 x["is_retaker"] == is_retaker, stat_list)
            converted_list = map(lambda x: (x["passed_count"], x["failed_count"]), result_list)
            val = next(converted_list, (0, 0))
            return {"passed": {"count": val[0]}, "failed": {"count": val[1]}}

        pie_stat = {}
        for teacher in set(map(lambda x: x["teacher"], stat_list)):
            pie_stat[teacher] = {
                "normal_student": convert(teacher, False),
                "retaker": convert(teacher, True),
            }

        return pie_stat

    ret = {'class_cnt_normal': get_class_info(False),
           'class_cnt_retakers': get_class_info(True),
           'exam_statistics': {}}
    for exam in exam_list.order_by('pk'):
        ret['exam_statistics'][exam.pk] = {}
        pie_list = ProjectInExam.objects.filter(exam=exam).order_by('pk')
        for pie in pie_list:
            ret['exam_statistics'][exam.pk][pie.project.name] = get_statistics_pie(pie)
    return success_api_response(ret)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Course, field="id", item="course_id")
def list_student_progress_by_class(request: HttpRequest, course_id: int):
    """list student progress by class

    [method]: GET

    [route]: /api/progress/<int:course_id>/class
    """
    course: Course = Course.objects.get(pk=course_id)
    result = StudentProgress.objects \
        .annotate(teacher_name=Subquery(InstructorClass.objects
                                        .filter(belong_to=OuterRef("course"),
                                                student=OuterRef("student"))
                                        .values("teacher"))) \
        .annotate(current_project_name=F("current_project__project__name")) \
        .filter(course=course) \
        .values("teacher_name", "current_project_name") \
        .annotate(count=Count('*'))
    ans = {"total_count": result.values("teacher_name").distinct().count(),
           "class": defaultdict(dict)}
    for entry in result:
        ans["class"][entry["teacher_name"]][entry["current_project_name"]] = entry["count"]

    return success_api_response(ans)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Course, field="id", item="course_id")
def list_student_progress_without_retake_by_class(request: HttpRequest, course_id: int):
    """list student progress by class(without retake student)

    [method]: GET

    [route]: /api/progress/<int:course_id>/class
    """
    course: Course = Course.objects.get(pk=course_id)
    student_progress = StudentProgress.objects.exclude(  # 删去重修学生
        student__in=Subquery(Course.objects.filter(
            id=OuterRef('course_id')
        ).values('retake_students'))
    ).all()
    result = student_progress \
        .annotate(teacher_name=Subquery(InstructorClass.objects
                                        .filter(belong_to=OuterRef("course"),
                                                student=OuterRef("student"))
                                        .values("teacher"))) \
        .annotate(current_project_name=F("current_project__project__name")) \
        .filter(course=course) \
        .values("teacher_name", "current_project_name") \
        .annotate(count=Count('*'))
    ans = {"total_count": result.values("teacher_name").distinct().count(),
           "class": defaultdict(dict)}
    for entry in result:
        ans["class"][entry["teacher_name"]][entry["current_project_name"]] = entry["count"]

    return success_api_response(ans)


@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
@require_item_exist(model=Course, field="id", item="course_id")
def get_students_exam_record(request: HttpRequest, course_id: int, exam_id: int):
    """获取学生考试记录，用于以csv格式输出
    [method]: GET
    [route]: /api/progress/<int:course_id>/exam-student/<int:student_id>
    """

    # 这里的逻辑判断：
    # 1. 每周推进度的时候会创建exam_record，会对所有**有资格课上**创建**
    #    检查通过 or 未参加即可
    # 2. 对于剩余学生，如果没有资格课上，就不会创建exam_record
    info_dict = {}
    records = ExamRecord.objects.filter(project_in_exam__exam=exam_id)
    for record in records:
        if record.check_result > -1:
            status = '通过'
        elif record.status == 0:
            status = '未参加考试'
        else:
            status = '未通过'
        info_dict[record.student.student_id] = {
            "student_id": record.student.student_id,
            "student_name": record.student.name,
            "project_name": record.project_in_exam.project.name,
            "class_name": "",
            "status": status,
            "retake": '非重修'
        }

    course = Course.objects.get(pk=course_id)
    student_id_list = get_students_in_course_by_class(course, True)
    retake_student_set = set(retaker.id for retaker in course.retake_students.all())

    for teacher, student_id_list in student_id_list.items():
        # 这里的id不是学号
        for student_id in student_id_list:
            student = Student.objects.get(id=student_id)
            if student.student_id in info_dict:
                info_dict[student.student_id]["class_name"] = teacher
                if student_id in retake_student_set:
                    info_dict[student.student_id]["retake"] = '重修'
            else:
                progress = StudentProgress.objects.filter(student=student, course=course).first()
                if progress is not None:
                    info_dict[student.student_id] = {
                    "student_id": student.student_id,
                    "student_name": student.name,
                    "project_name": progress.current_project.project.name,
                    "class_name": teacher,
                    "status": "无资格参加考试",
                    "retake": '重修' if student_id in retake_student_set else '非重修'
                }

    info_list = sorted(info_dict.values(), key=lambda x: x["student_id"])
    data_frames = pandas.DataFrame(info_list)
    meta_info_columns = ["student_id", "student_name", "project_name", "class_name", "status", "retake"]
    data_frames = data_frames[meta_info_columns]

    response = {
        "data": data_frames.to_csv(),
        "filename": f"{course_id}_{exam_id}_student_record.csv"
    }
    return success_api_response(response)


PUSH_ALL_STUDENT_AFTER_EXAM_API = wrapped_api({
    'put': push_all_student_after_exam,
})

PUSH_ALL_STUDENT_BEFORE_EXAM_API = wrapped_api({
    'put': push_all_student_before_exam,
})

STUDENT_PROGRESS_SET_API = wrapped_api({
    "get": list_student_progress,
    "post": set_student_progress
})

STUDENT_PROGRESS_STATISTICS_API = wrapped_api({
    'get': get_student_progress_statistics
})

NON_SUBMIT_STUDENT_API = wrapped_api({
    'get': get_non_submit_student_list
})

CLASS_PROGRESS_API = wrapped_api({
    'get': list_student_progress_by_class
})

NO_RETAKE_CLASS_PROGRESS_API = wrapped_api({
    'get': list_student_progress_without_retake_by_class
})
