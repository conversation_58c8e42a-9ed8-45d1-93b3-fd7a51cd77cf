<template>
  <Card>
    <filter-table
      :data="tableData"
      :columns="columns"
      :default-filter="
        this.$store.state.app.tableFilter.problemTable ? this.$store.state.app.tableFilter.problemTable : {}
      "
      @on-search="onSearch"
    />
    <div style="margin: 10px; overflow: hidden">
      <div style="float: right">
        <Page
          :total="totalCnt"
          :current="curPage"
          :page-size="pageSize"
          show-total
          show-elevator
          @on-change="changePage"
        />
      </div>
    </div>
  </Card>
</template>

<script>
import { judgeProblemReq } from '@/api/judge'
import FilterTable from '@/view/filter-table/filter-table'
import { getErrModalOptions, getLocalTime } from '@/libs/util'
import _ from 'lodash'
import { LinkButton, WhitePre } from '@/libs/render-item'

export default {
  name: 'ProblemTable',
  components: { FilterTable },
  data() {
    return {
      tableData: [],
      columns: [
        {
          title: 'ID',
          key: 'id',
          filter: {
            type: 'Input'
          },
          render: (h, params) => WhitePre(h, params.row.id)
        },
        {
          title: 'Name',
          key: 'name',
          filter: {
            type: 'Input'
          },
          render: (h, params) => WhitePre(h, params.row.name)
        },
        {
          title: 'Type',
          key: 'type',
          filter: {
            type: 'Select',
            option: {
              0: {
                value: 0,
                name: '文件上传',
                color: 'green'
              },
              1: {
                value: 1,
                name: '单选',
                color: 'red'
              },
              2: {
                value: 2,
                name: '多选',
                color: 'red'
              },
              3: {
                value: 3,
                name: '填空',
                color: 'red'
              }
            }
          },
          render: (h, params) => h('div', this.getType(params.row['type']) || '无')
        },
        // {
        //   title: 'Type',
        //   key: 'type',
        //   render: (h, params) => h('div', params.row['created_by__username'] || '匿名')
        // },
        {
          title: 'Created At',
          key: 'created_at',
          render: (h, params) => h('div', getLocalTime(params.row['created_at']))
        },
        {
          title: 'Action',
          align: 'center',
          render: (h, params) => LinkButton(h, params.row.id, 'problem_detail', '查看', false)
        },
        {
          title: 'Statistics',
          align: 'center',
          render: (h, params) => LinkButton(h, params.row.id, 'statistics_detail', '统计数据', false)
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      order_by: '-created_at',
      filter: {}
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.problemTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.problemTable)
    }
    this.curPage = this.$store.state.app.tablePage.problemTable ? this.$store.state.app.tablePage.problemTable : 1
    this.loadData(this.curPage)
  },
  methods: {
    getType(type) {
      switch (type) {
        case 0:
          return '文件上传'
        case 1:
          return '单选'
        case 2:
          return '多选'
        case 3:
          return '填空'
        default:
          break
      }
      return '无'
    },
    loadData(index) {
      judgeProblemReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    changePage(index) {
      judgeProblemReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'problemTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onSearch(search) {
      search = this.refactorSearchObject(search)
      judgeProblemReq('get', {
        page: 1,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data['models']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTableFilter', { filter: search, name: 'problemTable' })
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'problemTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {} // reset filter
      Object.keys(search).forEach((key) => {
        if (key === 'id') {
          this.filter[key + '__exact'] = search[key]
        } else {
          this.filter[key + '__contains'] = search[key]
        }
      })
      return searchNew
    }
  }
}
</script>
