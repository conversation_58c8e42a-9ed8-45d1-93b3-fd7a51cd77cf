{"name": "trebuchet-frontend", "version": "2.0.0", "private": false, "scripts": {"serve-prod": "cross-env VUE_APP_API_URL=http://*************:8100 vue-cli-service serve", "serve-dev": "cross-env VUE_APP_API_URL=http://*************:9100 VUE_APP_TAG=Dev vue-cli-service serve", "serve-dev-local": "cross-env VUE_APP_API_URL=http://localhost:8000 VUE_APP_TAG=Dev vue-cli-service serve", "build-prod": "cross-env VUE_APP_API_URL=http://*************:8100 vue-cli-service build --mode production", "build-dev": "cross-env VUE_APP_API_URL=http://*************:9100 VUE_APP_TAG=Dev vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.26.1", "core-js": "^3.8.3", "countup": "^1.8.2", "echarts": "^5.3.2", "lodash": "^4.17.20", "moment": "^2.29.3", "novam-editor": "^3.0.6", "qs": "^6.11.0", "tree-table-vue": "^1.1.0", "v-click-outside": "^3.2.0", "v-org-tree": "^1.0.6", "view-design": "^4.7.0", "vue": "^2.6.14", "vue-clipboard2": "^0.3.1", "vue-router": "^3.5.1", "vuex": "^3.6.2", "vuex-persist": "^3.1.3"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "less": "3.5.0", "less-loader": "7.0.2", "prettier": "^2.4.1", "vue-template-compiler": "^2.5.13"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "plugin:prettier/recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/multi-word-component-names": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}