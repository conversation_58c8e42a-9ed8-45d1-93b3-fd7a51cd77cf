import datetime

from django.core.exceptions import Field<PERSON>rror
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.models.project_in_exam import ProjectInExam
from core.api.auth import jwt_auth
from core.api.query_utils import (query_filter, query_order_by, query_page)
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, validate_args, wrapped_api)

from judge.models.exam_event_record import ExamEventRecord
from judge.api.permissions import JUDGE_VIEW_EXAM_PASS
from judge.forms.exam_event_record import ExamEventRecordInfo


def _validate_create_exam_event_record(request: HttpRequest) -> bool:
    """
    validate create ExamEventRecord
    Args:
        request:

    Returns:
    """
    fields = [field.name for field in ExamEventRecord._meta.get_fields()]
    data: dict = parse_data(request)
    if data is None:
        return False
    for key in data.keys():
        if key not in fields or key in ['created_by', 'created_at']:
            return False
    info: ExamEventRecordInfo = ExamEventRecordInfo(data)
    if not info.is_valid():
        return False
    project_in_exam_id: int = data.get('project_in_exam', None)
    if project_in_exam_id is not None:
        if not ProjectInExam.objects.filter(id=project_in_exam_id).exists():
            return False
    return True


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_EXAM_PASS])
@require_POST
@validate_args(func=_validate_create_exam_event_record)
def create_exam_event_record(request: HttpRequest):
    """
    create ExamEventRecord
    :param request:
    :return:
    """
    data: dict = parse_data(request)
    data['happened_at'] = datetime.datetime.fromtimestamp(data['happened_at'] / 1000)
    project_in_exam_id: int = data['project_in_exam']
    data['project_in_exam'] = ProjectInExam.objects.get(pk=project_in_exam_id)
    new_exam_event_record = ExamEventRecord.objects.create(**data)
    new_exam_event_record.created_by = request.user
    new_exam_event_record.save()
    return success_api_response({'success': True})


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_EXAM_PASS])
@require_GET
@query_filter(fields=[("project_in_exam", str), ("title", str), ("description", str), ("id", int)])
@query_order_by(fields=['happened_at', 'created_at'])
@query_page(default=10)
def list_exam_event_record(request: HttpRequest, *args, **kwargs):
    """
    list exam_event_record
    :param request:
    :param args:
    :param kwargs:
    :return:
    """
    models_all = ExamEventRecord.objects.count()
    models: QuerySet = ExamEventRecord.objects.all()
    # filter
    filter_ordered = kwargs.get('filter')
    try:
        models = models.filter(filter_ordered)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGUMENT_ERROR,
                                   "Unsupported Filter Method.")
    # order by
    order_by = kwargs.get('order_by')
    if order_by is not None:
        models = models.order_by(*order_by)
    else:
        models = models.order_by('-happened_at')
    # page
    page = kwargs.get('page')
    page_size = kwargs.get('page_size')
    paginator = Paginator(models, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        models_info = []
    else:
        models_info = list(
            paginator.get_page(page).object_list.values(
                'id', 'project_in_exam', 'title', 'description', 'happened_at',
                'created_by', 'created_by__username', 'created_at'
            )
        )
    data = {
        'models_all': models_all,
        'total_count': paginator.count,
        'page_all': page_all,
        'page_now': page,
        'models': models_info
    }
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_EXAM_PASS])
@require_GET
@require_item_exist(model=ExamEventRecord, field='id', item='id')
def get_exam_event_record(request: HttpRequest, query_id: int):
    """
    get ExamEventRecord
    :param request:
    :param query_id:
    :return:
    """
    exam_event_record = ExamEventRecord.objects.get(**{'id': query_id})
    data: dict = model_to_dict(exam_event_record)
    data['created_at'] = exam_event_record.created_at
    data['happened_at'] = None if exam_event_record.happened_at is None else exam_event_record.happened_at
    data['created_by__username'] = (None if exam_event_record.created_by is None
                                    else exam_event_record.created_by.username)
    data['title'] = None if exam_event_record.title is None else exam_event_record.title
    data['description'] = None if exam_event_record.description is None else exam_event_record.description
    return success_api_response(data)


@response_wrapper
@jwt_auth(perms=[JUDGE_VIEW_EXAM_PASS])
@require_http_methods(['DELETE'])
@require_item_exist(model=ExamEventRecord, field='id', item='id')
def delete_exam_event_record(request: HttpRequest, query_id: int) -> dict:
    """
    delete problem
    :param request:
    :param query_id:
    :return:
    """
    model = ExamEventRecord.objects.get(**{'id': query_id})
    info = model_to_dict(model)
    info['created_at'] = model.created_at
    model.delete()
    return success_api_response(info)


EXAM_EVENT_RECORD_SET_API = wrapped_api({
    "get": list_exam_event_record,
    "post": create_exam_event_record,
})

EXAM_EVENT_RECORD_DETAIL_API = wrapped_api({
    "get": get_exam_event_record,
    "delete": delete_exam_event_record,
})
