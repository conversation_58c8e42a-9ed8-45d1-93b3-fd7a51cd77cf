"""
define test case validate form
"""
from django import forms
from nose.tools import nottest


@nottest
class TestCaseInfo(forms.Form):
    """
    define test case validate form
    """
    name = forms.CharField(max_length=100)
    description = forms.CharField(max_length=240, required=False)
    judge_parameter = forms.CharField(max_length=24000)
    judge_data = forms.IntegerField(required=False)
