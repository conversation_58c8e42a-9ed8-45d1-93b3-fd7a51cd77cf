{"ast": null, "code": "import Main from '@/view/index/main';\nexport const onExamRouter = {\n  path: '/on-exam',\n  name: 'on-exam',\n  component: Main,\n  meta: {\n    title: '课上信息',\n    icon: 'ios-time',\n    jumpRoute: '/on-exam/overview'\n  },\n  children: [{\n    path: 'overview',\n    name: 'overview',\n    meta: {\n      title: '考场信息总览'\n    },\n    component: () => import('@/view/on-exam/overview')\n  }, {\n    path: 'timetable',\n    name: 'timetable',\n    meta: {\n      title: '考场信息展示'\n    },\n    component: () => import('@/view/on-exam/timetable')\n  }, {\n    path: 'exam-progress-detection',\n    name: 'exam_progress_detection',\n    meta: {\n      title: '进度检测'\n    },\n    component: () => import('@/view/on-exam/exam-progress-detection')\n  }]\n};", "map": {"version": 3, "names": ["Main", "onExamRouter", "path", "name", "component", "meta", "title", "icon", "jumpRoute", "children"], "sources": ["E:/CO/助教/dev projects/trebuchet-frontend/src/view/on-exam/router.js"], "sourcesContent": ["import Main from '@/view/index/main'\r\n\r\nexport const onExamRouter = {\r\n  path: '/on-exam',\r\n  name: 'on-exam',\r\n  component: Main,\r\n  meta: {\r\n    title: '课上信息',\r\n    icon: 'ios-time',\r\n    jumpRoute: '/on-exam/overview'\r\n  },\r\n  children: [\r\n    {\r\n      path: 'overview',\r\n      name: 'overview',\r\n      meta: {\r\n        title: '考场信息总览'\r\n      },\r\n      component: () => import('@/view/on-exam/overview')\r\n    },\r\n    {\r\n      path: 'timetable',\r\n      name: 'timetable',\r\n      meta: {\r\n        title: '考场信息展示'\r\n      },\r\n      component: () => import('@/view/on-exam/timetable')\r\n    },\r\n    {\r\n      path: 'exam-progress-detection',\r\n      name: 'exam_progress_detection',\r\n      meta: {\r\n        title: '进度检测'\r\n      },\r\n      component: () => import('@/view/on-exam/exam-progress-detection')\r\n    }\r\n  ]\r\n}\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,mBAAmB;AAEpC,OAAO,MAAMC,YAAY,GAAG;EAC1BC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEJ,IAAI;EACfK,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,yBAAyB;EACnD,CAAC,EACD;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,WAAW;IACjBE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,0BAA0B;EACpD,CAAC,EACD;IACEF,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,yBAAyB;IAC/BE,IAAI,EAAE;MACJC,KAAK,EAAE;IACT,CAAC;IACDF,SAAS,EAAE,MAAM,MAAM,CAAC,wCAAwC;EAClE,CAAC;AAEL,CAAC"}, "metadata": {}, "sourceType": "module"}