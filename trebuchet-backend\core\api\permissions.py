"""permissions for app "core"
"""

from core.models.permissions import (<PERSON><PERSON><PERSON>_CHANGE, CLA<PERSON>_CREATE, CLASS_DELETE,
                                     CLASS_VIEW, COURSE_CHANGE, COURSE_CREATE,
                                     COURSE_DELETE, COURSE_VIEW, EXAM_CHANGE,
                                     EXAM_CREATE, EXAM_DELETE,
                                     EXAM_QUEUE_CHANGE, EXAM_QUEUE_CREATE,
                                     EXAM_QUEUE_DELETE, EXAM_QUEUE_VIEW, EXAM_QUEUE_TEST,
                                     EXAM_RECORD_CHANGE, EXAM_RECORD_CREATE,
                                     EXAM_RECORD_DELETE, EXAM_RECORD_EXPORT,
                                     EXAM_RECORD_OLD_CHANGE, EXAM_RECORD_VIEW,
                                     EXAM_VIEW, PIE_CHANGE, PIE_CREATE,
                                     PIE_DELETE, <PERSON>IE_VIEW, PROGRESS_CHANGE,
                                     PROGRESS_CREATE, PROGRESS_DELETE,
                                     PROGRESS_VIEW, PROJECT_CHANGE,
                                     PROJECT_CREATE, PROJECT_DELETE,
                                     PROJECT_VIEW, R<PERSON><PERSON>_CHANGE, ROOM_CREATE,
                                     ROOM_DELETE, ROOM_VIEW, SEAT_CHANGE,
                                     SEAT_CREATE, SEAT_DELETE,
                                     SEAT_RECORD_CHANGE, SEAT_RECORD_CREATE,
                                     SEAT_RECORD_DELETE, SEAT_RECORD_VIEW,
                                     SEAT_VIEW, STUDENT_CHANGE, STUDENT_CREATE,
                                     STUDENT_DELETE, STUDENT_VIEW,
                                     USER_PROFILE_CHANGE, USER_PROFILE_CREATE,
                                     USER_PROFILE_DELETE, USER_PROFILE_VIEW,
                                     OMNICONFIG_EDIT, OMNICONFIG_VIEW, EXAM_RECORD_STATISTICS,
                                     QUESTION_VIEW, QUESTION_EDIT, COURSE_EXPORT_FINAL_RESULTS,
                                     NEWS_CHANGE, NEWS_CREATE, NEWS_DELETE, NEWS_VIEW,
                                     PUSH_MSG_EDIT, PUSH_MSG_PUSH, PUSH_MSG_VIEW, TAG_CREATE, TAG_VIEW, TAG_UPDATE,
                                     TAG_DELETE)

# Room permissions

CORE_ROOM_CREATE = "core." + ROOM_CREATE
CORE_ROOM_VIEW = "core." + ROOM_VIEW
CORE_ROOM_CHANGE = "core." + ROOM_CHANGE
CORE_ROOM_DELETE = "core." + ROOM_DELETE

# Course permissions

CORE_COURSE_CREATE = "core." + COURSE_CREATE
CORE_COURSE_VIEW = "core." + COURSE_VIEW
CORE_COURSE_CHANGE = "core." + COURSE_CHANGE
CORE_COURSE_DELETE = "core." + COURSE_DELETE
CORE_COURSE_EXPORT_FINAL_RESULTS = "core." + COURSE_EXPORT_FINAL_RESULTS

# ExamQueue permissions

CORE_EXAM_QUEUE_CREATE = "core." + EXAM_QUEUE_CREATE
CORE_EXAM_QUEUE_VIEW = "core." + EXAM_QUEUE_VIEW
CORE_EXAM_QUEUE_CHANGE = "core." + EXAM_QUEUE_CHANGE
CORE_EXAM_QUEUE_DELETE = "core." + EXAM_QUEUE_DELETE
CORE_EXAM_QUEUE_TEST = "core." + EXAM_QUEUE_TEST

# ExamRecord permissions

CORE_EXAM_RECORD_CREATE = "core." + EXAM_RECORD_CREATE
CORE_EXAM_RECORD_VIEW = "core." + EXAM_RECORD_VIEW
CORE_EXAM_RECORD_CHANGE = "core." + EXAM_RECORD_CHANGE
CORE_EXAM_RECORD_DELETE = "core." + EXAM_RECORD_DELETE
CORE_EXAM_RECORD_OLD_CHANGE = "core." + EXAM_RECORD_OLD_CHANGE
CORE_EXAM_RECORD_EXPORT = "core." + EXAM_RECORD_EXPORT
CORE_EXAM_RECORD_STATISTICS = "core." + EXAM_RECORD_STATISTICS

# Exam permissions

CORE_EXAM_CREATE = "core." + EXAM_CREATE
CORE_EXAM_VIEW = "core." + EXAM_VIEW
CORE_EXAM_CHANGE = "core." + EXAM_CHANGE
CORE_EXAM_DELETE = "core." + EXAM_DELETE

# InstructorClass permissions

CORE_CLASS_CREATE = "core." + CLASS_CREATE
CORE_CLASS_VIEW = "core." + CLASS_VIEW
CORE_CLASS_CHANGE = "core." + CLASS_CHANGE
CORE_CLASS_DELETE = "core." + CLASS_DELETE

# Project permissions

CORE_PROJECT_CREATE = "core." + PROJECT_CREATE
CORE_PROJECT_VIEW = "core." + PROJECT_VIEW
CORE_PROJECT_CHANGE = "core." + PROJECT_CHANGE
CORE_PROJECT_DELETE = "core." + PROJECT_DELETE

# ProjectInExam permissions

CORE_PIE_CREATE = "core." + PIE_CREATE
CORE_PIE_VIEW = "core." + PIE_VIEW
CORE_PIE_CHANGE = "core." + PIE_CHANGE
CORE_PIE_DELETE = "core." + PIE_DELETE

# Seat permissions

CORE_SEAT_CREATE = "core." + SEAT_CREATE
CORE_SEAT_VIEW = "core." + SEAT_VIEW
CORE_SEAT_CHANGE = "core." + SEAT_CHANGE
CORE_SEAT_DELETE = "core." + SEAT_DELETE

# StudentProgress permissions

CORE_PROGRESS_CREATE = "core." + PROGRESS_CREATE
CORE_PROGRESS_VIEW = "core." + PROGRESS_VIEW
CORE_PROGRESS_CHANGE = "core." + PROGRESS_CHANGE
CORE_PROGRESS_DELETE = "core." + PROGRESS_DELETE

# StudentSeatRecord permissions

CORE_SEAT_RECORD_CREATE = "core." + SEAT_RECORD_CREATE
CORE_SEAT_RECORD_VIEW = "core." + SEAT_RECORD_VIEW
CORE_SEAT_RECORD_CHANGE = "core." + SEAT_RECORD_CHANGE
CORE_SEAT_RECORD_DELETE = "core." + SEAT_RECORD_DELETE

# Student permissions

CORE_STUDENT_CREATE = "core." + STUDENT_CREATE
CORE_STUDENT_VIEW = "core." + STUDENT_VIEW
CORE_STUDENT_CHANGE = "core." + STUDENT_CHANGE
CORE_STUDENT_DELETE = "core." + STUDENT_DELETE

# UserProfile permissions

CORE_USER_PROFILE_CREATE = "core." + USER_PROFILE_CREATE
CORE_USER_PROFILE_VIEW = "core." + USER_PROFILE_VIEW
CORE_USER_PROFILE_CHANGE = "core." + USER_PROFILE_CHANGE
CORE_USER_PROFILE_DELETE = "core." + USER_PROFILE_DELETE

CORE_OMNICONFIG_VIEW = "core." + OMNICONFIG_VIEW
CORE_OMNICONFIG_EDIT = "core." + OMNICONFIG_EDIT

CORE_QUESTION_VIEW = "core." + QUESTION_VIEW
CORE_QUESTION_EDIT = "core." + QUESTION_EDIT

CORE_NEWS_CHANGE = "core." + NEWS_CHANGE
CORE_NEWS_CREATE = "core." + NEWS_CREATE
CORE_NEWS_DELETE = "core." + NEWS_DELETE
CORE_NEWS_VIEW = "core." + NEWS_VIEW

CORE_PUSH_MSG_EDIT = "core." + PUSH_MSG_EDIT
CORE_PUSH_MSG_PUSH = "core." + PUSH_MSG_PUSH
CORE_PUSH_MSG_VIEW = "core." + PUSH_MSG_VIEW

# Tag
DISCUSSION_TAG_CREATE = "discussion." + TAG_CREATE
DISCUSSION_TAG_VIEW = "discussion." + TAG_VIEW
DISCUSSION_TAG_UPDATE = "discussion." + TAG_UPDATE
DISCUSSION_TAG_DELETE = "discussion." + TAG_DELETE
