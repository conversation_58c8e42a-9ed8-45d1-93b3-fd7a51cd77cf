"""
declare ProblemJudgeRecord model
"""
from django.db import models

from judge.constants import (JUDGER_CHAPTER_CODE_LENGTH,
                             JUDGER_COURSE_CODE_LENGTH,
                             JUDGER_IDENTIFIER_MAX_LENGTH, J<PERSON><PERSON><PERSON><PERSON>,
                             PROBLEM_RESULT_TAG_CHOICES,
                             STUDENT_COMMENT_LENGTH, USERNAME_MAX_LENGTH, JUDGER_TOOL_CHAIN_LENGTH)
from judge.models.permissions import (CHANGE_PJR, CREATE_PJR, DELETE_PJR,
                                      VIEW_EXAM_PASS, VIEW_PASS_RANK, VIEW_PJR,
                                      VIEW_PROBLEM_PASS, VIEW_PROJECT_PASS)
from judge.models.problem import Problem
from judge.models.user_submitted_file import UserSubmittedFile

from core.models.project_in_exam import ProjectInExam


class ProblemJudgeRecord(models.Model):
    """
    Result of the single problem.

    Fields:
        - course_code: the code field of Course model
        - chapter_code: `{project.name}_{exam.date}`, e.g. 'P0_20190509'
    """
    origin_id = models.IntegerField(blank=True, null=True)
    edx_username = models.CharField(max_length=USERNAME_MAX_LENGTH)
    problem = models.ForeignKey(Problem, on_delete=models.CASCADE)
    attachment = models.ForeignKey(
        UserSubmittedFile, on_delete=models.SET_NULL, null=True)
    student_comment = models.CharField(
        max_length=STUDENT_COMMENT_LENGTH, blank=True, null=True)
    judger_identifier = models.CharField(
        max_length=JUDGER_IDENTIFIER_MAX_LENGTH, blank=True, null=True)
    judge_result = models.IntegerField(
        choices=PROBLEM_RESULT_TAG_CHOICES, default=JUDGING
    )
    course_code = models.CharField(
        max_length=JUDGER_COURSE_CODE_LENGTH, blank=True, null=True)
    chapter_code = models.CharField(
        max_length=JUDGER_CHAPTER_CODE_LENGTH, blank=True, null=True)
    project_in_exam = models.ForeignKey(
        ProjectInExam, on_delete=models.SET_NULL, null=True, db_column="project_in_exam")
    submitted_at = models.DateTimeField(blank=True, null=True)
    started_at = models.DateTimeField(blank=True, null=True)
    finished_at = models.DateTimeField(blank=True, null=True)
    tool_chain = models.CharField(max_length=JUDGER_TOOL_CHAIN_LENGTH, blank=True, null=True)

    class Meta:
        default_permissions = ()
        indexes = [
            models.Index(fields=['edx_username', 'problem_id', 'project_in_exam']),
        ]
        permissions = [
            (CHANGE_PJR, CHANGE_PJR),
            (CREATE_PJR, CREATE_PJR),
            (DELETE_PJR, DELETE_PJR),
            (VIEW_PJR, VIEW_PJR),
            (VIEW_EXAM_PASS, VIEW_EXAM_PASS),
            (VIEW_PASS_RANK, VIEW_PASS_RANK),
            (VIEW_PROBLEM_PASS, VIEW_PROBLEM_PASS),
            (VIEW_PROJECT_PASS, VIEW_PROJECT_PASS)
        ]
