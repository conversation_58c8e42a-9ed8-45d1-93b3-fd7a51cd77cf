"""
define exam queue api
"""
import json

from django.db import transaction
from django.db.models import (<PERSON><PERSON>anField, Case, F, OuterRef, QuerySet,
                              Subquery, Value, When)
from django.db.models.functions import Length
from django.http import HttpRequest
from django.utils import timezone
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.permissions import CORE_EXAM_QUEUE_CHANGE, CORE_EXAM_QUEUE_VIEW
from core.api.student_progress import parse_requirement
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, response_wrapper,
                            success_api_response, wrapped_api, assign_exam_object)
from core.interface.event import publish_event
from core.interface.student_progress import query_student_passed
from core.models import ProjectInExam
from core.models.exam import Exam
from core.models.exam_queue import ExamQueue
from core.models.exam_record import STATUS_IN_PROGRESS, <PERSON>am<PERSON><PERSON>ord, STATUS_WAITING_IN_QUEUE, STATUS_CHECKED_OUT
from core.models.instructor_class import InstructorClass
from core.models.room import Room
from core.models.seat import Seat
from core.models.student import Student
from core.models.student_seat_record import StudentSeatRecord
from core.models.user_profile import UserProfile


# def _get_current_exam() -> Exam:
#     exam = Exam.objects.filter(active=True).first()
#     return exam


def _get_course_current_exam(course_id: int) -> Exam:
    exam = Exam.objects.filter(course_id=course_id, active=True).first()
    return exam

def publish_exam_queue_message(exam_id: int, room_id: int, queue_item_id: int, etype: str):
    publish_event("queue-{}-{}".format(exam_id, room_id), json.dumps({"type": etype, "id": queue_item_id}))


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_CHANGE])
@require_POST
@require_item_exist(Student, "student_id", "student_id")
def push_student_in_queue(request: HttpRequest, student_id: str, project_in_exam_id: int):
    """push student into queue

    [method]: POST

    [route]: /api/exam-queue/<str:student_id>/<int:project_in_exam_id>
    """
    student = Student.objects.filter(student_id=student_id).first()
    project_in_exam = ProjectInExam.objects.filter(id=project_in_exam_id).first()
    if project_in_exam is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, '没有找到学生的考试信息，不能举手')

    requirement: str = project_in_exam.pass_requirement
    if not parse_requirement(requirement, student_id, project_in_exam_id):
        return failed_api_response(ErrorCode.REFUSE_ACCESS, '抱歉，你没有达到考试通过条件，不能举手')

    seat_record: StudentSeatRecord = StudentSeatRecord.objects.filter(
        student=student, exam=project_in_exam.exam).first()
    if seat_record is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, '学生没有座位信息，举手失败')
    seat: Seat = seat_record.seat
    room: Room = seat.room
    exam_queue, created = ExamQueue.objects.get_or_create(
        student=student,
        room=room,
        exam=project_in_exam.exam,
        project_in_exam=project_in_exam
    )
    if not exam_queue.valid:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'already popped')
    virtual_queue: QuerySet = ExamQueue.objects.filter(room=room,
                                                       exam=project_in_exam.exam).order_by('id')
    # TODO: 解决高并发问题
    virtual_queue: list = list(virtual_queue)
    location = virtual_queue.index(exam_queue) + 1

    exam_record_set: QuerySet = exam_queue.student.examrecord_set
    exam_record: ExamRecord = exam_record_set.filter(project_in_exam__exam__active=True).order_by("-id").first()
    exam_record.status = STATUS_WAITING_IN_QUEUE
    exam_record.save()
    return success_api_response({'location': location, 'created': created})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_CHANGE])
@require_POST
@require_item_exist(model=Room, field="id", item="room_id")
def roll_back_push_student_in_queue(request: HttpRequest, room_id: int, student_id: str, project_in_exam_id: int):
    """roll back hand up status if any special situation

    [method]: POST

    [route]: /api/exam-queue/<int:room_id>/<str:student_id>/<int:project_in_exam_id>/cancelHandsUp
    """
    room = Room.objects.get(pk=room_id)
    course_id = UserProfile.objects.filter(user=request.user).first().course_id
    exam = _get_course_current_exam(course_id)
    if exam is None:
        return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)
    student = Student.objects.filter(student_id=student_id).first()
    if student is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "no such student")
    project_in_exam = ProjectInExam.objects.filter(
        pk=project_in_exam_id).first()
    exam_queue = ExamQueue.objects.filter(
        exam=exam, room=room, student=student, project_in_exam=project_in_exam).first()
    if exam_queue is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "no such exam queue")
    publish_exam_queue_message(exam.id, room.id, exam_queue.id, "rollback")

    # 由于获取排位使用的是 index，不需要担心 examqueue id 增长
    exam_queue.delete()

    # roll back exam record
    exam_record = ExamRecord.objects.filter(
        student=student, project_in_exam=project_in_exam).first()
    exam_record.status = STATUS_IN_PROGRESS
    exam_record.checked_out_at = None
    exam_record.save()

    return success_api_response({"result": "Ok, exam-queue has been deleted"})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_VIEW])
@require_GET
@require_item_exist(model=Room, field="id", item="room_id")
def retrieve_first_index_in_queue(request: HttpRequest, room_id: int):
    """fetch head of valid queue, and it won't pop it.

    [method]: GET

    [route]: /api/exam-queue/<int:room_id>
    """
    room = Room.objects.get(pk=room_id)
    course_id = UserProfile.objects.filter(user=request.user).first().course_id
    exam = _get_course_current_exam(course_id)
    if exam is None:
        return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)
    exam_queue = ExamQueue.objects.filter(
        exam=exam, room=room).order_by("id")
    first_valid_exam_queue = exam_queue.filter(valid=True).first()
    if first_valid_exam_queue is None:
        location = 0
    else:
        location = list(exam_queue).index(first_valid_exam_queue) + 1
    return success_api_response({"location": location})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_CHANGE])
@require_http_methods(['PUT'])
@require_item_exist(model=Room, field='id', item='room_id')
def pop_student_in_queue(request: HttpRequest, room_id: int):
    """front student get out the queue

    [method]: PUT

    [route]: /api/exam-queue/<int:room_id>
    """
    room: Room = Room.objects.get(id=room_id)
    course_id = UserProfile.objects.filter(user=request.user).first().course_id
    exam = _get_course_current_exam(course_id)
    if exam is None:
        return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)
    with transaction.atomic():
        data: dict = parse_data(request)
        student_id = ''
        if data is not None:
            student_id = data.get('student_id', '')
        if student_id is None:
            student_id = ''
        if not isinstance(student_id, str):
            return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "student id must be string")
        if student_id:
            exam_queue: ExamQueue = ExamQueue.objects.select_for_update().filter(
                exam=exam, room=room, valid=True, student__student_id=student_id).first()
        else:
            exam_queue: ExamQueue = ExamQueue.objects.select_for_update().filter(
                exam=exam, room=room, valid=True).order_by('id').first()
        if exam_queue is None:
            return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'queue empty')
        # 这里是唯一的将 ExamQueue.valid 设置为 False 的地方
        exam_queue.valid = False
        exam_queue.popped_at = timezone.now()
        examinant = request.user
        exam_queue.examinant = examinant
        exam_queue.save()

    publish_exam_queue_message(exam.id, room.id, exam_queue.id, "pop")
    exam_record = ExamRecord.objects.filter(
        student_id=exam_queue.student_id, project_in_exam_id=exam_queue.project_in_exam_id).first()
    return success_api_response({'exam_record_id': exam_record.id})


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_CHANGE])
@require_http_methods(['POST'])
@require_item_exist(model=Room, field='id', item='room_id')
def roll_back_pop_student_in_queue(request: HttpRequest, room_id: int, student_id: str, project_in_exam_id: int):
    """front popped student roll back into the queue

    [method]: POST

    [route]: /api/exam-queue/<int:room_id>/<str:student_id>/<int:project_in_exam_id>/cancelPop
    """
    room: Room = Room.objects.get(id=room_id)
    course_id = UserProfile.objects.filter(user=request.user).first().course_id
    exam = _get_course_current_exam(course_id)

    if exam is None:
        return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)

    exam_record = ExamRecord.objects.filter(
        student__student_id=student_id, project_in_exam_id=project_in_exam_id).first()

    if exam_record.status == STATUS_CHECKED_OUT:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'already checked')

    with transaction.atomic():
        if student_id is None:
            student_id = ''
        if not isinstance(student_id, str):
            return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, "student id must be string")
        # 逆操作:取出的ExamQueue.valid 为False，再设置为True即为放回队列
        exam_queue = None
        if student_id:
            exam_queue: ExamQueue = ExamQueue.objects.select_for_update().filter(
                exam=exam, room=room, valid=False, student__student_id=student_id).first()
        if exam_queue is None:
            return failed_api_response(ErrorCode.ITEM_NOT_FOUND, 'student in queue not find')
        exam_record.status = STATUS_WAITING_IN_QUEUE
        exam_record.checked_out_at = None
        exam_record.save()
        exam_queue.valid = True
        exam_queue.examinant = None
        exam_queue.save()

    publish_exam_queue_message(exam.id, room.id, exam_queue.id, "rollback")
    exam_record = ExamRecord.objects.filter(
        student_id=exam_queue.student_id, project_in_exam_id=exam_queue.project_in_exam_id).first()
    return success_api_response({'exam_record_id': exam_record.id})


def is_valid_qa_record(qa_record: str):
    return qa_record is not None and len(qa_record) > 2


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_QUEUE_VIEW])
@require_GET
@require_item_exist(model=Room, field='id', item='room_id')
@assign_exam_object
def list_room_queue(request: HttpRequest, exam: Exam, room_id: int):
    """list exam queue in the room

    [method]: GET

    [route]: /api/exam-queue-list/<int:room_id>
    """
    valid = request.GET.get('valid', True)
    if valid in ('true', 'True'):
        valid = True
    elif valid in ('false', 'False'):
        valid = False
    if not isinstance(valid, bool):
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS, 'invalid valid')
    room: Room = Room.objects.get(id=room_id)
    # 判断是否已经被检查：若该学生对应于该 PIE 的 ExamRecord 中的 check_comment 字段不为空，说明已经被检查
    models = ExamQueue.objects \
        .annotate(student_name=F("student__name")) \
        .annotate(studentid=F("student__student_id")) \
        .annotate(teacher_name=Subquery(InstructorClass.objects
                                        .filter(belong_to=OuterRef("project_in_exam__project__course"),
                                                student__id__exact=OuterRef("student__id"))
                                        .values("teacher"))) \
        .annotate(seat=Subquery(StudentSeatRecord.objects
                                .filter(exam=exam, student=OuterRef("student"))
                                .values("seat__name"))) \
        .annotate(check_comment=Subquery(ExamRecord.objects
                                         .filter(student=OuterRef("student"),
                                                 project_in_exam=OuterRef("project_in_exam"))
                                         .values("check_comment")[:1])) \
        .annotate(exam_record_id=Subquery(ExamRecord.objects
                                          .filter(student=OuterRef("student"),
                                                  project_in_exam=OuterRef("project_in_exam"))
                                          .values("id")[:1])) \
        .annotate(project_name=F("project_in_exam__project__name")) \
        .annotate(check_comment_length=Length("check_comment")) \
        .annotate(checked=Case(When(check_comment_length__exact=0,
                                    then=Value(False)),
                               default=Value(True),
                               output_field=BooleanField())) \
        .annotate(question_answer_record=Subquery(ExamRecord.objects
                                                  .filter(student=OuterRef("student"),
                                                          project_in_exam=OuterRef("project_in_exam"))
                                                  .values("question_answer_record")[:1]))
    if valid:
        models: QuerySet = models.filter(room=room, exam=exam, valid=True).order_by('id')
    else:
        models: QuerySet = models.filter(room=room, exam=exam, valid=False).order_by('popped_at') \
            .annotate(examinant_name=F("examinant__username"))

    queue = models.values(
        'student', 'studentid', 'student_name', 'project_in_exam', 'project_name', 'teacher_name',
        'exam', 'room', 'pushed_at', 'popped_at', 'valid', 'seat', 'checked', 'examinant__username',
        'exam_record_id', 'question_answer_record'
    )
    queue_num = models.count()

    for queue_record in queue:
        queue_record.update({
            "passed": query_student_passed(queue_record["studentid"], queue_record["project_in_exam"]),
            "checked": queue_record["checked"] or is_valid_qa_record(queue_record["question_answer_record"])
        })
        del queue_record["question_answer_record"]

    data = {
        'queue_num': queue_num,
        'queue': list(queue)
    }
    return success_api_response(data)


QUEUE_HEAD_API = wrapped_api({
    "put": pop_student_in_queue,
    "get": retrieve_first_index_in_queue
})

QUEUE_PUSH_API = wrapped_api({
    'post': push_student_in_queue
})

QUEUE_POP_API = wrapped_api({
    'post': roll_back_pop_student_in_queue
})

QUEUE_LIST_API = wrapped_api({
    'get': list_room_queue
})
