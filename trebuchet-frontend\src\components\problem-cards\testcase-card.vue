<template>
  <div>
    <Card class="problem-cards">
      <p slot="title">Testcase 列表 | Count: {{ innerTestcases.length }}</p>
      <span slot="extra">
        <Tooltip content="全部下载" style="cursor: pointer; margin-left: 10px" @click.native="downloadAll">
          <Icon type="ios-download-outline" size="25" />
        </Tooltip>
      </span>
      <Table :border="true" :stripe="true" :data="innerTestcases" :columns="testcaseColumns" />
    </Card>
  </div>
</template>

<script>
import './cards.less'
import { getErrModalOptions, processRemoteDownload } from '@/libs/util'
import { Link, LinkDownload, WhitePre } from '@/libs/render-item'
import { testcaseName } from '@/libs/testcases'

export default {
  name: 'TestcaseCard',
  props: {
    testcases: Array,
    testcaseFile: Function,
    testcaseAbstract: Function
  },
  data() {
    return {
      width: document.body.clientWidth,
      originTestcases: [],
      innerTestcases: [],
      resizeSleeping: false
    }
  },
  computed: {
    testcaseColumns() {
      return [
        {
          title: 'ID',
          resizable: true,
          minWidth: 80,
          sortable: true,
          key: 'id',
          render: (h, params) => Link(h, params.row.id, 'testcase_detail')
        },
        {
          title: 'Name',
          resizable: true,
          minWidth: 300,
          sortable: true,
          key: 'name',
          render: (h, params) => WhitePre(h, testcaseName(params.row.name))
        },
        {
          title: 'Judge Parameter',
          resizable: true,
          minWidth: 260,
          render: (h, params) => WhitePre(h, JSON.stringify(JSON.parse(params.row['judge_parameter']), null, 4))
        },
        {
          title: 'Abstract',
          resizable: true,
          minWidth: 180,
          render: (h, params) => WhitePre(h, this.testcaseAbstract(params.row['judge_parameter']))
        },
        {
          title: 'Judge Data Name',
          resizable: true,
          minWidth: 240,
          render: (h, params) =>
            LinkDownload(
              h,
              () => this.onDownload(params.row.id),
              params.row['judge_data__filename'],
              params.row['judge_data'] === null
            )
        }
      ]
    }
  },
  mounted() {
    this.originTestcases = this.testcases || []
    this.innerTestcases = [...this.originTestcases]
    const unique = new Set()
    this.originTestcases.forEach((item) => {
      if (/P[0-9]_/.test(item.name)) {
        if (unique.has(item.name.substr(3))) {
          this.$Notice.warning({ title: '测试点 ' + item.name + ' 名称疑似重复' })
        } else {
          unique.add(item.name.substr(3))
        }
      }
    })
    const _onresize = window.onresize
    window.onresize = (argv) => {
      if (_onresize) {
        _onresize(argv)
      }
      if (this.resizeSleeping === false) {
        this.resizeSleeping = setTimeout(() => {
          this.width = document.body.clientWidth
          this.resizeSleeping = false
        }, 200)
      }
    }
  },
  methods: {
    async onDownload(id) {
      try {
        processRemoteDownload(await this.testcaseFile(id))
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    async downloadAll() {
      this.innerTestcases.forEach((i) => this.onDownload(i.id))
    }
  }
}
</script>
