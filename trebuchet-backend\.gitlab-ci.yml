stages:
  - build
  - test
  - deploy

build_production:
  stage: build
  only:
    - master
  tags:
    - machine002
  script:
    - docker build -t "trebuchet-backend:ci_job_$CI_PIPELINE_ID" .

build_develop:
  stage: build
  only:
    - develop
  tags:
    - machine002
  script:
    - docker build -t "trebuchet-backend:ci_job_$CI_PIPELINE_ID" .

build_merge_requests:
  stage: build
  only:
    refs:
      - merge_requests
  tags:
    - machine002
  script:
    - docker build -t "trebuchet-backend:ci_job_$CI_PIPELINE_ID" .

test_all:
  stage: test
  only:
    - master
    - develop
    - merge_requests
  tags:
    - machine002
  script:
    - docker run --rm=true "trebuchet-backend:ci_job_$CI_PIPELINE_ID" /bin/bash -c "cp config.example.yaml config.yaml && pylint --load-plugins pylint_django --django-settings-module=trebuchet.settings ./core ./judge"

deploy_production:
  stage: deploy
  only:
    - master
  tags:
    - machine002
  script:
    - docker stop trebuchet-backend-production && docker rm trebuchet-backend-production || true
    - docker run --restart=unless-stopped --name=trebuchet-backend-production -v /mnt/data/app-config/config-production.yaml:/app/config.yaml -d -p 8100:8000 "trebuchet-backend:ci_job_$CI_PIPELINE_ID"

deploy_develop:
  stage: deploy
  only:
    - develop
  tags:
    - machine002
  script:
    - docker stop trebuchet-backend-develop && docker rm trebuchet-backend-develop || true
    - docker run --restart=unless-stopped --name=trebuchet-backend-develop -v /mnt/data/app-config/config-develop.yaml:/app/config.yaml -d -p 9100:8000 "trebuchet-backend:ci_job_$CI_PIPELINE_ID"
