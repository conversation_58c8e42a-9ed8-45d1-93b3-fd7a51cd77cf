"""Room APIs
"""
import datetime

from django.core.exceptions import FieldError
from django.core.paginator import Paginator
from django.db.models import Count, F, Q
from django.forms import model_to_dict
from django.http import HttpRequest
from django.views.decorators.http import (require_GET, require_http_methods,
                                          require_POST)

from core.api.auth import jwt_auth
from core.api.exam_queue import _get_course_current_exam
from core.api.permissions import (CORE_EXAM_RECORD_VIEW, CORE_ROOM_CHANGE,
                                  CORE_ROOM_CREATE, CORE_ROOM_DELETE,
                                  CORE_ROOM_VIEW)
from core.api.query_utils import (query_distinct, query_filter, query_order_by,
                                  query_page)
from core.api.seat import validate_seat_data
from core.api.utils import (ErrorCode, failed_api_response, parse_data,
                            require_item_exist, require_item_miss,
                            response_wrapper, success_api_response,
                            validate_args, wrapped_api)
from core.forms.room import RoomInfo
from core.models.exam import Exam
from core.models.exam_record import ExamRecord
from core.models.room import Room
from core.models.seat import Seat
from core.models.student_seat_record import StudentSeatRecord
from core.models.user_profile import UserProfile


def validate_room_request(request: HttpRequest) -> bool:
    """validate room post/put request
    """
    data: dict = parse_data(request)
    if data is None:
        return False
    # check fields
    allowed_fields = {"name", "available", "comment", "seats"}
    if not data.keys() <= allowed_fields:
        return False
    # check form
    seats = data.pop("seats", None)
    info = RoomInfo(data)
    if not info.is_valid():
        return False
    # check seats
    if seats is not None:
        # initial value is to against pylint check
        check_duplicate: set = {(0, 0)}
        for seat in seats:
            if not validate_seat_data(seat):
                return False
            pos_x = seat.get("pos_x")
            pos_y = seat.get("pos_y")
            if (pos_x, pos_y) in check_duplicate:
                return False
            check_duplicate.add((pos_x, pos_y))
    return True


@response_wrapper
@jwt_auth(perms=[CORE_ROOM_CREATE])
@require_POST
@validate_args(func=validate_room_request)
@require_item_miss(model=Room, field="name")
def create_room(request: HttpRequest):
    """Deal with room post request

    [route]: /api/rooms

    [method]: POST
    """
    data: dict = parse_data(request)
    seats = data.pop("seats", None)
    room = Room.objects.create(**data)
    if seats is not None:
        for seat in seats:
            new_seat = Seat(**seat)
            new_seat.room = room
            new_seat.save()
    return success_api_response({"result": "Ok, Room Created."})


@response_wrapper
@jwt_auth(perms=[CORE_ROOM_CHANGE])
@require_http_methods(["PUT"])
@validate_args(func=validate_room_request)
@require_item_exist(model=Room, field="id", item="id")
def update_room(request: HttpRequest, room_id: int):
    """Deal with room put request

    TODO: 如果考场名字发生改变，座位被删除，考生考试记录如何溯源

    [route]: /api/rooms/:id

    [method]: PUT
    """
    data: dict = parse_data(request)
    room = Room.objects.get(**{"id": room_id})
    name = data.get("name")
    if room.name != name:
        if Room.objects.filter(**{"name": name}).exists():
            return failed_api_response(ErrorCode.ITEM_ALREADY_EXISTS, "Sorry, room name conflict.")

    # seats = data.pop("seats", None)
    for key in data.keys():
        setattr(room, key, data.get(key))
    room.save()
    # deprecated
    # room.seat_set.all().delete()

    # if seats is not None:
    #     for seat in seats:
    #         new_seat = Seat(**seat)
    #         new_seat.room = room
    #         new_seat.save()
    return success_api_response({"result": "Ok, Room Updated."})


@response_wrapper
@jwt_auth(perms=[CORE_ROOM_VIEW])
@require_GET
@query_filter(fields=[("id", int), ("name", str), ("available", bool)])
@query_distinct(fields=["name"], model=Room)
@query_order_by(fields=["name", "available"])
@query_page(default=10)
def list_rooms(request: HttpRequest, *args, **kwargs):
    """Deal with room get all request

    [route]: /api/rooms

    [method]: GET
    """
    rooms_all = Room.objects.count()
    rooms_available = Room.objects.filter(**{"available": True}).count()
    rooms = Room.objects.annotate(seats_all=Count("seat")).annotate(
        seats_available=Count("seat", filter=Q(seat__available=True)))
    # filter
    room_filter = kwargs.get("filter")
    try:
        rooms = rooms.filter(room_filter)
    except FieldError:
        return failed_api_response(ErrorCode.INVALID_REQUEST_ARGS,
                                   "Unsupported Filter Method.")
    # order_by
    order_by = kwargs.get("order_by")
    if order_by is not None:
        rooms = rooms.order_by(*order_by)
    else:
        rooms = rooms.order_by("-available")
    # page
    page = kwargs.get("page")
    page_size = kwargs.get("page_size")
    paginator = Paginator(rooms, page_size)
    page_all = paginator.num_pages

    if page > page_all:
        rooms_details = []
    else:
        rooms_details = list(paginator.get_page(page).object_list.values(
            "id", "name", "available", "comment", "seats_all", "seats_available"))
    room_list_data = {
        "rooms_all": rooms_all,
        "rooms_available": rooms_available,
        "total_count": paginator.count,
        "page_all": page_all,
        "page_now": page,
        "rooms": rooms_details
    }
    return success_api_response(room_list_data)


@response_wrapper
@jwt_auth(perms=[CORE_ROOM_VIEW])
@require_GET
@require_item_exist(model=Room, field="id", item="id")
def retrieve_room_detail(request: HttpRequest, room_id: int):
    """Deal with room get request

    [route]: /api/rooms/:id

    [method]: GET
    """
    room = Room.objects.get(**{"id": room_id})
    seats = room.seat_set.all()
    seats_all = seats.count()
    seats_available = seats.filter(**{"available": True}).count()
    seats_details = list(seats.values(
        "id", "name", "pos_x", "pos_y", "available", "comment"))
    room_detail = model_to_dict(room)
    room_detail.update({
        "seats_all": seats_all,
        "seats_available": seats_available,
        "seats": seats_details
    })
    return success_api_response(room_detail)


@response_wrapper
@jwt_auth(perms=[CORE_ROOM_DELETE])
@require_http_methods(["DELETE"])
@require_item_exist(model=Room, field="id", item="id")
def remove_room(request: HttpRequest, room_id: int):
    """Deal with room delete request

    [route]: /api/rooms/:id

    [method]: DELETE
    """
    Room.objects.filter(**{"id": room_id}).delete()
    return success_api_response({"result": "Ok, Room Deleted."})


def exam_filter_helper(key: str, value: str):
    """helper function for get_room_arrangement filter
    """
    key = "id__" + key.split("__")[-1]
    return Q(**{key: value})


@response_wrapper
@jwt_auth(perms=[CORE_ROOM_VIEW])
@require_GET
@query_filter(fields=[("exam", int)], custom={"exam": exam_filter_helper})
@require_item_exist(model=Room, field="id", item="id")
def get_room_arrangement(request: HttpRequest, room_id: int, *args, **kwargs):
    """Deal with room get request

    [route]: /api/room-arrangement/:id

    [method]: GET
    """
    room = Room.objects.get(**{"id": room_id})
    exam_filter = kwargs.get("filter")
    if exam_filter == Q():
        course_id = UserProfile.objects.filter(user=request.user).first().course_id
        exam = _get_course_current_exam(course_id)
        if exam is None:
            return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)
    else:
        exam = Exam.objects.filter(exam_filter).first()
        if exam is None:
            return failed_api_response(ErrorCode.NOT_FOUND_ERROR)

    exam_record = ExamRecord.objects \
        .annotate(project_name=F("project_in_exam__project__name")) \
        .annotate(project_id=F("project_in_exam__project__id")) \
        .filter(project_in_exam__exam=exam)
    arrangements = list(StudentSeatRecord.objects.filter(
        exam=exam,
        seat__room=room).values(
        "id", "student__id", "student__student_id", "student__name", "seat__pos_x", "seat__pos_y"))
    arrangement_info = []
    for arrangement in arrangements:
        student_pk = arrangement.get("student__id")
        specified_exam_record = exam_record.filter(student__id=student_pk).order_by("-id").first()
        if not specified_exam_record:
            return failed_api_response(ErrorCode.SEAT_ARRANGEMENT_ERROR,
                                       "排的座位有问题：学生 ID：{}，学号：{}，姓名：{}"
                                       "，该学生没有对应的考试记录 (exam_record 表里面找不到这个人)"
                                       .format(arrangement["student__id"],
                                               arrangement["student__student_id"],
                                               arrangement["student__name"]))
        status = specified_exam_record.status
        arrangement.pop("student__id")
        arrangement.update({"status": status,
                            "project_name": specified_exam_record.project_name,
                            "project_id": specified_exam_record.project_id,
                            "project_in_exam_id": specified_exam_record.project_in_exam_id})
        arrangement_info.append(arrangement)
    return success_api_response({"arrangements": arrangement_info})


@response_wrapper
@jwt_auth(perms=[CORE_ROOM_VIEW, CORE_EXAM_RECORD_VIEW])
@require_GET
@query_filter(fields=[("exam", int)], custom={"exam": exam_filter_helper})
@require_item_exist(model=Room, field="id", item="id")
def get_room_examrecord(request: HttpRequest, room_id: int, *args, **kwargs):
    """get examrecords in specified room

    [method]: GET

    [route]: /api/room-examrecord/<int:id>
    """
    room = Room.objects.get(pk=room_id)
    exam_filter = kwargs.get("filter")
    if exam_filter == Q():
        course_id = UserProfile.objects.filter(user=request.user).first().course_id
        exam = _get_course_current_exam(course_id)
        if exam is None:
            return failed_api_response(ErrorCode.ACTIVE_EXAM_NOT_FOUND_ERROR)
    else:
        exam = Exam.objects.filter(exam_filter).first()
        if exam is None:
            return failed_api_response(ErrorCode.NOT_FOUND_ERROR)

    exam_date = exam.date
    student_pks = StudentSeatRecord.objects \
        .filter(exam=exam, seat__room=room) \
        .values_list("student__id", flat=True)

    exam_records = ExamRecord.objects \
        .annotate(begin_time=F("project_in_exam__begin_time")) \
        .annotate(duration=F("project_in_exam__duration")) \
        .filter(student__id__in=student_pks, project_in_exam__exam=exam)
    records_info = list(exam_records.values('id', 'student__student_id', 'student__name',
                                            'project_in_exam__project__name', 'status', 'duration',
                                            'check_result', 'checked_in_at', 'checked_out_at',
                                            'examinant__username', 'check_comment', 'extend_time', 'begin_time'))
    for info in records_info:
        begin_time = info.pop('begin_time')
        duration = info.pop('duration')
        extend_time = info.pop('extend_time')
        info['end_time'] = datetime.datetime.combine(
            exam_date, begin_time) + datetime.timedelta(minutes=duration + extend_time)
    data = {
        "exam-records": records_info
    }
    return success_api_response(data)


ROOM_DETAIL_API = wrapped_api({
    "get": retrieve_room_detail,
    "put": update_room,
    "delete": remove_room
})

ROOM_SET_API = wrapped_api({
    "get": list_rooms,
    "post": create_room
})

ROOM_ARRANGEMENT_API = wrapped_api({
    "get": get_room_arrangement,
})

ROOM_EXAMRECORD_API = wrapped_api({
    "get": get_room_examrecord
})
