<template>
  <Card class="problem-cards">
    <p slot="title">Attachment</p>
    <Row>
      <!--<Upload :before-upload="handleUpload" action="" style="display: inline-block; margin-right: 5px">
        <Button type="primary">上传题目附件</Button>
      </Upload>-->
      <Button v-if="fileExists" type="primary" style="margin-left: 10px" @click="downloadHandler">
        {{ '下载 ' + this.filename }}
      </Button>
    </Row>
  </Card>
</template>

<script>
import './cards.less'
import { getErrModalOptions, processDownload, processRemoteDownload } from '@/libs/util'

export default {
  name: 'AttachmentCard',
  props: {
    modify: Boolean,
    problemData: Number,
    problemFilename: String,
    problemFile: Function
  },
  data() {
    return {
      fileExists: false,
      filename: '',
      file: null
    }
  },
  computed: {
    downloadHandler() {
      return this.file === null ? this.handleRemoteDownLoad : this.handleLocalDownload
    }
  },
  mounted() {
    this.fileExists = !!this.problemData
    this.filename = this.problemFilename
    this.file = null
  },
  methods: {
    async handleRemoteDownLoad() {
      try {
        processRemoteDownload(await this.problemFile())
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    handleLocalDownload() {
      processDownload(this.file, this.filename)
    }
    /* handleUpload(uploadFile) {
      this.$Modal.confirm({
        title: '确认上传',
        content: '若已有上传文件，则会覆盖旧的',
        onOk: () => {
          this.file = uploadFile
          this.filename = uploadFile.name
          this.fileExists = true
          this.$Notice.info({ title: '文件已选中，将在确认' + (this.modify ? '修改' : '提交') + '后上传' })
        }
      })
      return false
    } */
  }
}
</script>
