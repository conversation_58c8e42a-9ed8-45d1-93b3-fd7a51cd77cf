"""
define the api for judge to check timeout.
"""
from datetime import datetime, timedelta

from django.http import HttpRequest
from django.views.decorators.http import require_GET

from core.api.auth import jwt_auth
from core.api.utils import response_wrapper, success_api_response, failed_api_response, ErrorCode, \
    wrapped_api
from core.models import ProjectInExam
from core.models.exam_record import ExamRecord, STATUS_IN_PROGRESS
from core.models.student import Student
from core.api.permissions import CORE_EXAM_RECORD_VIEW


def _validate_check_judge_permission(data: dict) -> bool:
    if not isinstance(data.get('student_id', None), str):
        return False
    if not isinstance(data.get('pie_id', None), int):
        return False
    return isinstance(data.get('timestamp', None), int)


@response_wrapper
@jwt_auth(perms=[CORE_EXAM_RECORD_VIEW], whitelisted_tokens=[])
@require_GET
def check_judge_permission(request: HttpRequest, *args, **kwargs):
    """检查这个学生有没有超时，如果没超时就access，从而judge进行评测.

    [method]: GET

    [route]: /api/check-judge-permission
    """
    query = request.GET.dict()
    student_id: str = query["student_id"]
    pie_id: int = int(query["pie_id"])
    timestamp: int = int(query["timestamp"])

    student: Student = Student.objects.filter(student_id=student_id).first()
    if student is None:
        # Teacher or TA
        return success_api_response({'allow_judge': True})
    project_in_exam: ProjectInExam = ProjectInExam.objects.filter(id=pie_id).first()
    if project_in_exam is None:
        return failed_api_response(ErrorCode.ITEM_NOT_FOUND, "No Such ProjectInExam")
    under_class_exam = project_in_exam.exam.course.under_class_exam
    if under_class_exam == project_in_exam.exam:
        return success_api_response({'allow_judge': True})
    exam_record: ExamRecord = ExamRecord.objects.filter(
        student=student, project_in_exam=project_in_exam, status=STATUS_IN_PROGRESS).first()
    if exam_record is None:
        return success_api_response({'allow_judge': False})
    now_time = datetime.fromtimestamp(timestamp)
    begin_time = datetime.combine(project_in_exam.exam.date, project_in_exam.begin_time)
    allow_judge = False
    if (now_time - begin_time) < timedelta(minutes=project_in_exam.duration + exam_record.extend_time):
        allow_judge = True
    return success_api_response({'allow_judge': allow_judge})


CHECK_JUDGE_PERMISSION_API = wrapped_api({
    'get': check_judge_permission,
})
