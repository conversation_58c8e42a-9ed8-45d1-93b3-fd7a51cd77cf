<template>
  <Card>
    <Row>
      <Col span="5">
        <Card>
          <p slot="title">基本信息</p>
          <p><b>ID: </b>{{ group.id }}</p>
          <p><b>用户组名: </b>{{ group.name }}</p>
        </Card>
      </Col>
      <Col offset="2" span="15">
        <Card>
          <p slot="title">修改权限</p>
          <Form :label-width="40">
            <form-item>
              <Transfer
                :list-style="listStyle"
                :data="allRoles"
                :target-keys="group.permissions"
                :titles="['剩余可选权限', '现有权限']"
                filterable
                @on-change="handleChange"
              />
            </form-item>
            <form-item>
              <Button type="primary" @click="handleSubmit">提交</Button>
            </form-item>
          </Form>
        </Card>
      </Col>
    </Row>
  </Card>
</template>

<script>
import { getErrModalOptions } from '@/libs/util'
import { getUserRole, userGroupIdReq } from '@/api/user'

export default {
  name: 'UserGroupDetail',
  data() {
    return {
      group: {},
      allRoles: [],
      permission: [],
      listStyle: {
        width: '300px',
        height: '500px'
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      userGroupIdReq('get', this.$route.params.id, {})
        .then((res) => {
          this.group = res.data
          getUserRole()
            .then((res) => {
              this.allRoles = res.data.all.map((item) => {
                return {
                  key: item,
                  label: item
                }
              })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    handleChange(targetKeys) {
      this.group.permissions = targetKeys
    },
    handleSubmit() {
      userGroupIdReq('put', this.$route.params.id, {
        permission: this.group.permissions
      })
        .then(() => {
          this.$Notice.success({ title: '修改成功' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    }
  }
}
</script>
