<template>
  <Row style="display: flex" justify="center">
    <Col :xs="24" :sm="24" :md="24" :lg="24" :xl="12" :xxl="12">
      <Card>
        <Button type="primary" @click="showAdd = true">新增TAG</Button><br /><br />
        <Table border :columns="columns" :data="tag_list">
          <template #name="{ row }">
            <strong>{{ row.name }}</strong>
          </template>
          <template #action="{ row, index }">
            <Button style="margin-right: 5px" @click="checkLinkedDiscussion(index)">查看关联</Button>
            <Button type="error" @click="deleteTag(row.id)">删除</Button>
          </template>
        </Table>
      </Card>
    </Col>
    <Modal v-model="info.show" title="关联的讨论" @on-ok="info.show = false">
      <Table :columns="columns_dis" :data="discussions"></Table>
    </Modal>
    <Modal v-model="showAdd" title="新增TAG" @on-ok="addTag">
      <Input v-model="tagToAdd" placeholder="输入新TAG的名称..." style="width: 300px" />
    </Modal>
  </Row>
</template>

<script>
import { getTagList, addTag, deleteTag } from '@/api/discussion'

export default {
  name: 'DiscussionTag',
  data() {
    return {
      tag_list: [],
      columns: [
        {
          title: 'ID',
          key: 'id',
          align: 'center',
          width: 150
        },
        {
          title: 'TAG名称',
          key: 'name',
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          width: 200,
          align: 'center'
        }
      ],
      info: {
        show: false,
        index: -1
      },
      discussions: [],
      columns_dis: [
        {
          title: '帖子标题',
          key: 'title',
          align: 'center'
        },
        {
          title: '隶属课程编号',
          key: 'course',
          align: 'center'
        },
        {
          title: '发布者ID',
          key: 'author',
          align: 'center'
        }
      ],
      showAdd: false,
      tagToAdd: ''
    }
  },
  mounted() {
    this.getTagList()
  },
  methods: {
    async getTagList() {
      let res = await getTagList()
      this.tag_list = res.data.data
    },
    async addTag() {
      if (this.tagToAdd.length === 0) {
        this.$Modal.error({
          title: '警告',
          content: '名称不能为空'
        })
        return
      }
      await addTag(this.tagToAdd)
      this.tagToAdd = ''
      await this.getTagList()
    },
    deleteTag(tag_id) {
      this.$Modal.confirm({
        title: '警告',
        content: '是否确认删除',
        onOk: async () => {
          await deleteTag(tag_id)
          await this.getTagList()
        }
      })
    },
    checkLinkedDiscussion(index) {
      this.info.index = index
      this.discussions = this.tag_list[index]['discussion']
      this.info.show = true
    }
  }
}
</script>
