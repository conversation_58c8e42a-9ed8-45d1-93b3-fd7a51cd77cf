# Generated by Django 3.1.7 on 2021-11-27 12:04

from django.db import migrations, models


class Migration(migrations.Migration):

    def forward(self, schema_editor):
        tag_model = self.get_model('discussion', "Tag")
        priority = 0
        for tag in tag_model.objects.all():
            tag.priority = priority
            priority += 1
            tag.save()

    def backward(self, schema_editor):
        return

    dependencies = [
        ('discussion', '0006_auto_20211023_1943'),
    ]

    operations = [
        migrations.AddField(
            model_name='tag',
            name='priority',
            field=models.PositiveIntegerField(default=0),
            preserve_default=False,
        ),
        migrations.RunPython(forward, backward)
    ]
