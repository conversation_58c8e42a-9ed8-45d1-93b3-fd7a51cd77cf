<template>
  <div class="tables-edit-outer">
    <div v-if="!isEditing" class="tables-edit-con">
      <span class="value-con">{{ value }}</span>
      <Button v-if="editable" class="tables-edit-btn" style="padding: 2px 4px" type="text" @click="startEdit">
        <Icon type="md-create" />
      </Button>
    </div>
    <div v-else class="tables-editing-con">
      <Input v-if="!isSwitch" :value="value" class="tables-edit-input" @input="handleInput" />
      <i-switch v-if="isSwitch" :value="value" @input="handleInput" />
      <Button style="padding: 6px 4px" type="text" @click="saveEdit">
        <Icon type="md-checkmark" />
      </Button>
      <Button style="padding: 6px 4px" type="text" @click="cancelEdit">
        <Icon type="md-close" />
      </Button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TablesEdit',
  props: {
    value: [String, Number, Boolean],
    editingCellId: String,
    params: Object,
    editable: Boolean
  },
  computed: {
    isEditing() {
      return this.editingCellId === `editing-${this.params.index}-${this.params.column.key}`
    },
    isSwitch() {
      return typeof this.value === 'boolean'
    }
  },
  methods: {
    handleInput(val) {
      this.$emit('input', val)
    },
    startEdit() {
      this.$emit('on-start-edit', this.params)
    },
    saveEdit() {
      this.$emit('on-save-edit', this.params)
    },
    cancelEdit() {
      this.$emit('on-cancel-edit', this.params)
    }
  }
}
</script>

<style lang="less" scoped>
.tables-edit-outer {
  height: 100%;
  .tables-edit-con {
    position: relative;
    height: 100%;
    .value-con {
      vertical-align: middle;
    }
    .tables-edit-btn {
      position: absolute;
      right: 10px;
      top: 0;
      display: none;
    }
    &:hover {
      .tables-edit-btn {
        display: inline-block;
      }
    }
  }
  .tables-editing-con {
    .tables-edit-input {
      width: ~'calc(100% - 60px)';
    }
  }
}
</style>
