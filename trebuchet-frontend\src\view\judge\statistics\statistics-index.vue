<template>
  <Card>
    <Select v-model="selectExam" style="width: 200px; float: left" @on-change="onChange">
      <Option v-for="item in examList" :key="item.id" :value="item.id">{{ item.id }} : {{ item.date }}</Option>
    </Select>
    <Button type="primary" style="margin-left: 10px" @click="refresh" :disabled="loading">刷新</Button>
    <div style="display: inline-block; vertical-align: middle; margin-left: 10px">显示重修生：</div>
    <i-switch v-model="switch_retaker" size="large" @on-change="refresh" :disabled="loading">
      <span slot="open">开启</span>
      <span slot="close">关闭</span>
    </i-switch>
    <Button type="primary" style="margin-left: 10px" @click="addEvent" :disabled="loading">添加事件</Button>
    <Modal v-model="addEventModal" title="添加考试事件" @on-ok="onPostEvent">
      <Form :label-width="80">
        <form-item prop="project" label="Project">
          <Select v-model="eventToAdd.pie">
            <Option v-for="item in projects" :key="item.id" :value="item.id">
              {{ item.name }}
            </Option>
          </Select>
        </form-item>
        <form-item prop="content" label="事件">
          <Input v-model="eventToAdd.title" />
        </form-item>
        <form-item prop="content" label="描述">
          <Input v-model="eventToAdd.content" type="textarea" :rows="4" />
        </form-item>
        <form-item prop="time" label="时间">
          <time-picker v-model="eventToAdd.time" type="time" placeholder="请选择时间" />
        </form-item>
      </Form>
    </Modal>
    <br />
    <Modal v-model="show" title="各学院通过率">
      <Table :data="modalTableData" :columns="modalTableColumns" />
    </Modal>
    <br />
    <Card>
      <p slot="title">各项目通过率</p>
      <Table :data="projects" :columns="projectColumns" />
    </Card>
    <br />
    <Row>
      <RadioGroup
        v-model="curProject"
        v-show="!loading"
        @on-change="showProject(projects[curProject].id, projects[curProject].name)"
      >
        <Radio v-for="(item, index) in projects" :key="item.id" border size="large" :label="index">
          {{ item.name }}
        </Radio>
      </RadioGroup>
    </Row>
    <br />
    <Row v-if="loading">
      <br />
      <Col offset="11" style="margin-top: 30px; margin-bottom: 40px">
        <Spin size="large" style="margin-bottom: 10px" />
        <p>加载中</p>
      </Col>
    </Row>
    <br />
    <statistics-chart
      v-if="selectPIE !== null"
      :value="newProblemChartData"
      :value-length="problemChartDataLength"
      :start-time="projectBeginTime"
      :time-delta="timeDelta"
      style="height: 300px"
      :text="selectPIE + ' 累计通过情况'"
      v-show="!loading"
      :line-formatter="
        (item, index, params) => {
          if (item.seriesName.length === 2 && index === params.length - 1) {
            return `达到 ${item.seriesName} 通过条件: ${item.data}<br/>`
          } else {
            return `通过 ${item.seriesName}: ${item.data}<br/>`
          }
        }
      "
    />
    <statistics-chart
      v-if="selectPIE !== null"
      :value="submitChartData"
      :value-length="submitChartDataLength"
      :start-time="submitBeginTime"
      :time-delta="timeDelta"
      style="height: 300px"
      :text="selectPIE + ' 提交情况'"
      v-show="!loading"
      :line-formatter="
        (item, index, params) => {
          if (item.seriesName.length === 2 && index === params.length - 1) {
            return `当前时刻 ${item.seriesName} 提交: ${item.data}<br/>`
          } else {
            return `提交 ${item.seriesName}: ${item.data}<br/>`
          }
        }
      "
    />
    <br />
    <card v-if="selectPIE !== null">
      <p slot="title">考试事件记录</p>
      <Table :data="eventTableData" :columns="eventTableColumns" no-data-text="暂无事件" />
    </card>
    <br />
    <card v-if="selectPIE !== null && showRank">
      <p slot="title">通过学生排行榜</p>
      <Table :data="studentRank" :columns="studentRankColumns" :height="500" />
    </card>
    <br />
    <Row>
      <Card>
        <p slot="title">
          {{ selectPIE === null ? '未选择 Project' : selectPIE + '各题情况' }}
        </p>
        <Table :data="problems" :columns="problemColumns" :height="200" />
      </Card>
    </Row>
    <br />
    <br />
    <statistics-chart
      v-if="selectProblem !== null"
      :value="testcaseChartData"
      :value-length="testcaseChartDataLength"
      :start-time="testcaseBeginTime"
      :time-delta="timeDelta"
      style="height: 300px"
      :text="selectProblem + ' 累计通过情况'"
    />
  </Card>
</template>

<script>
import {
  examEventRecordIdReq,
  examEventRecordReq,
  getChartData,
  getProblemChartData,
  getRankReq,
  getSubmitChartReq,
  judgeExamStatisticsReq,
  judgeProjectStatisticsReq,
  problemDetailReq
} from '@/api/judge'
import { examReq } from '@/api/exam'
import { getErrModalOptions, getLocalTime, processDownload } from '@/libs/util'
import statisticsChart from './statistics-chart'
import { ActionButton, Link, PercentTooltip, Spacer, WhitePre } from '@/libs/render-item'

export default {
  name: 'StatisticsIndex',
  components: { statisticsChart },
  data() {
    return {
      loading: true,
      curProject: null,
      projects: [],
      problems: [],
      problemColumns: [
        {
          title: 'ID',
          sortable: true,
          key: 'id',
          render: (h, params) => Link(h, params.row.id, 'statistics_detail')
        },
        {
          title: 'Name',
          sortable: true,
          key: 'name',
          render: (h, params) => WhitePre(h, params.row.name)
        },
        {
          title: '提交通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_submission'], params.row['total_submission'])
        },
        {
          title: '学生通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_student'], params.row['total_student'])
        },
        {
          title: 'Action',
          minWidth: 150,
          render: (h, params) =>
            h('div', [
              ActionButton(h, () => this.showProblem(params.row.id, params.row.name), '查看累计通过率', false),
              Spacer(h),
              ActionButton(h, () => this.downloadProblemDetail(params.row.id, params.row.name), '下载评测详情', false)
            ])
        }
      ],
      examList: [],
      selectExam: null,
      selectPIE: null,
      selectPIEId: null,
      projectColumns: [
        {
          title: 'Project ID',
          sortable: true,
          key: 'id',
          render: (h, params) => WhitePre(h, params.row.id)
        },
        {
          title: 'Project Name',
          sortable: true,
          key: 'name',
          render: (h, params) => WhitePre(h, params.row.name)
        },
        {
          title: 'Project 学生通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_student'], params.row['total_student'])
        },
        {
          title: 'Action',
          render: (h, params) =>
            ActionButton(h, () => this.showModal(params.row['department_specific_count']), '查看各学院通过率', false)
        }
      ],
      modalTableData: [],
      show: false,
      modalTableColumns: [
        {
          title: '系号',
          render: (h, params) => WhitePre(h, params.row.department)
        },
        {
          title: '本次考试通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_student'], params.row['total_student'])
        },
        {
          title: '累计通过率',
          render: (h, params) =>
            PercentTooltip(h, params.row['all_passed_student_in_course'], params.row['all_student_in_course'])
        }
      ],
      problemChartData: [],
      newProblemChartData: [],
      problemChartDataLength: null,
      projectBeginTime: null,
      selectProblem: null,
      testcaseChartData: [],
      testcaseChartDataLength: null,
      testcaseBeginTime: null,
      studentRank: [],
      studentRankColumns: [
        {
          title: '学号',
          key: 'student_id'
        }
      ],
      showRank: false,
      submitChartData: [],
      submitChartDataLength: null,
      submitBeginTime: null,
      switch_retaker: true,
      addEventModal: false,
      eventToAdd: {},
      eventTableData: [],
      eventTableColumns: [
        {
          title: '时间',
          sortable: true,
          key: 'happened_at',
          render: (h, params) => h('div', new Date(params.row.happened_at).toLocaleString())
        },
        {
          title: '事件',
          render: (h, params) => WhitePre(h, params.row.title)
        },
        {
          title: '描述',
          render: (h, params) => WhitePre(h, params.row.description)
        },
        {
          title: '操作',
          render: (h, params) => ActionButton(h, () => this.onDeleteEvent(params.index), '删除', false)
        }
      ],
      markLineData: [],
      timeDelta: null
    }
  },
  computed: {
    markLineOptions() {
      return {
        symbol: ['none', 'none'],
        lineStyle: {
          type: 'dashed',
          color: 'red'
        },
        label: {
          color: 'red',
          fontSize: 13
        },
        data: this.markLineData
      }
    }
  },
  mounted() {
    this.loadExams()
  },
  methods: {
    loadExams() {
      examReq('get', {
        order_by: '-date'
      })
        .then((res) => {
          this.examList = res.data.exams
          const underClass = this.examList.find((item) => item.course !== null)
          if (underClass === undefined) {
            this.selectExam = this.examList[0].id
          } else {
            this.selectExam = underClass.id
          }
          this.loadData(this.selectExam)
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
    },
    onChange(id) {
      this.curProject = null
      this.selectExam = id
      this.projects = []
      this.problems = []
      this.selectPIE = null
      this.selectPIEId = null
      this.selectProblem = null
      this.loadData(this.selectExam)
    },
    loadData(id) {
      this.loading = true
      judgeExamStatisticsReq('get', id, { include_retaker: this.switch_retaker ? 'True' : 'False' })
        .then((res) => {
          if (res.data.data.length === 0) {
            this.$Modal.info({
              title: '无 project in exam 数据'
            })
            return
          }
          this.projects = res.data.data
          this.loading = false
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
    },
    showProject(id, name) {
      this.selectPIE = name
      this.selectPIEId = id
      this.selectProblem = null
      this.showRank = false
      this.studentRankColumns = [
        {
          title: '学号',
          key: 'student_id'
        }
      ]
      this.studentRank = []
      judgeProjectStatisticsReq('get', id, {})
        .then((res) => {
          this.problems = res.data.data
          for (let i = 0; i < this.problems.length; i++) {
            const no = `${i + 1}`
            this.studentRankColumns.push({
              title: `通过${i + 1}题时间`,
              key: `problem_${i + 1}_pass`,
              sortable: true,
              sortType: i === 0 ? 'asc' : null,
              sortMethod: (a, b, type) => {
                if (a === undefined) {
                  return 1
                } else if (b === undefined) {
                  return -1
                } else {
                  const dateA = new Date(a * 1000)
                  const dateB = new Date(b * 1000)
                  if (type === 'asc') {
                    return dateA > dateB ? 1 : -1
                  } else {
                    return dateA > dateB ? -1 : 1
                  }
                }
              },
              render: (h, params) =>
                h('div', [
                  h('p', getLocalTime(params.row['problem_' + no + '_pass'], true, true)),
                  h('p', params.row['problem_' + no + '_name'])
                ])
            })
          }
          this.$nextTick(() => {
            this.showRank = true
          })
          getRankReq(this.selectPIEId)
            .then((res) => {
              Object.keys(res.data).forEach((studentId) => {
                const passRecord = res.data[studentId]
                passRecord.sort((a, b) => (a['passed_time'] < b['passed_time'] ? -1 : 1))
                const rank = { student_id: studentId }
                for (let i = 0; i < passRecord.length; i++) {
                  rank[`problem_${i + 1}_pass`] = passRecord[i]['passed_time']
                  rank[`problem_${i + 1}_name`] = passRecord[i]['problem_name']
                }
                this.studentRank.push(rank)
              })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
      getChartData('get', id, {})
        .then((res) => {
          this.problemChartData = []
          this.newProblemChartData = []
          this.timeDelta = res.data['delta'] * 1000
          this.projectBeginTime = res.data['begin_time']
          Object.keys(res.data).forEach((key) => {
            if (key !== 'begin_time' && key !== 'total_student' && key !== 'delta') {
              this.problemChartDataLength = res.data[key].length
              this.problemChartData.push({
                name: key,
                type: 'line',
                data: res.data[key]
              })
            }
          })
          this.loadEvent(id)
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      getSubmitChartReq(id).then((res) => {
        this.submitChartData = []
        this.submitBeginTime = res.data['begin_time']
        Object.keys(res.data).forEach((key) => {
          if (key !== 'begin_time' && key !== 'delta') {
            this.submitChartDataLength = res.data[key].length
            const fixData = []
            for (let i = 0; i < res.data[key].length; i++) {
              if (i === 0) {
                fixData.push(res.data[key][i])
              } else {
                fixData.push(res.data[key][i] - res.data[key][i - 1])
              }
            }
            this.submitChartData.push({
              name: key,
              type: 'bar',
              data: fixData
            })
          }
        })
      })
    },
    showModal(data) {
      this.modalTableData = []
      Object.keys(data).forEach((key) => {
        this.modalTableData.push({
          department: key,
          passed_student: data[key].passed_student,
          total_student: data[key].total_student,
          all_passed_student_in_course: data[key].all_passed_student_in_course,
          all_student_in_course: data[key].all_student_in_course
        })
      })
      this.show = true
    },
    showProblem(pid, project) {
      this.selectProblem = project
      getProblemChartData(this.selectPIEId, pid)
        .then((res) => {
          this.testcaseChartData = []
          this.testcaseBeginTime = res.data['begin_time']
          Object.keys(res.data).forEach((key) => {
            if (key !== 'begin_time' && key !== 'total_student' && key !== 'delta') {
              this.testcaseChartDataLength = res.data[key].length
              this.testcaseChartData.push({
                name: key,
                type: 'line',
                data: res.data[key]
              })
            }
          })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    async downloadProblemDetail(pid, pName) {
      try {
        let pie_id = this.selectPIEId
        let res = await problemDetailReq(pie_id, pid)
        let fileName = pName + '_detail.csv'
        processDownload(res.data, fileName)
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    refresh() {
      this.loadData(this.selectExam)
      if (this.selectPIEId) {
        this.showProject(this.selectPIEId, this.selectPIE)
      }
    },
    addEvent() {
      this.addEventModal = true
      this.eventToAdd.time = new Date()
    },
    async onPostEvent() {
      try {
        let selectTime = this.eventToAdd.time
        let timeReg = /([0-9]{2}):([0-9]{2}):([0-9]{2})/
        timeReg.test(selectTime)
        let happened_at = new Date()
        happened_at.setHours(parseInt(RegExp.$1))
        happened_at.setMinutes(parseInt(RegExp.$2))
        await examEventRecordReq('post', {
          project_in_exam: this.eventToAdd.pie,
          description: this.eventToAdd.content,
          happened_at: happened_at.valueOf(),
          title: this.eventToAdd.title
        })
        this.$Notice.success({ title: '加载成功' })
        await this.loadEvent(this.eventToAdd.pie)
      } catch (err) {
        this.$Modal.error(getErrModalOptions(err))
      }
    },
    async loadEvent(id) {
      try {
        let events = await examEventRecordReq('get', {
          project_in_exam__exact: id,
          page_size: 1000
        })
        this.eventTableData = events.data.models
        this.markLineData = []
        this.eventTableData.forEach((item) => {
          let eventTime = new Date(item.happened_at)
          let m = eventTime.getMinutes() < 10 ? '0' + eventTime.getMinutes() : eventTime.getMinutes()
          let h = eventTime.getHours() < 10 ? '0' + eventTime.getHours() : eventTime.getHours()
          this.markLineData.push({ xAxis: `${h}:${m}`, label: { position: 'middle', formatter: item.title } })
        })
        this.problemChartData[0].markLine = this.markLineOptions
        this.newProblemChartData = JSON.parse(JSON.stringify(this.problemChartData))
      } catch (err) {
        this.$Modal.error(getErrModalOptions(err))
      }
    },
    onDeleteEvent(index) {
      let event = this.eventTableData[index]
      this.$Modal.confirm({
        title: '确认删除',
        content: `事件描述: ${event.description}<br>事件时间: ${event.happened_at}`,
        onOk: async () => {
          try {
            await examEventRecordIdReq('delete', event.id)
            this.$Notice.success({ title: '加载成功' })
            await this.loadEvent(event.project_in_exam)
          } catch (err) {
            this.$Modal.error(getErrModalOptions(err))
          }
        }
      })
    }
  }
}
</script>
