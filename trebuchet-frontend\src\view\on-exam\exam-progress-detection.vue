<template>
  <div>
    <Card>
      <p slot="title">考试通过人数检测及无人通过警报</p>
      
      <!-- 考试选择 -->
      <Row :gutter="16" style="margin-bottom: 20px">
        <Col span="8">
          <FormItem label="选择考试">
            <Select v-model="selectedExam" @on-change="onExamChange" placeholder="请选择考试">
              <Option v-for="exam in examList" :key="exam.id" :value="exam.id">
                {{ exam.id }} - {{ exam.date }}
              </Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="4">
          <Button type="primary" @click="startMonitoring" :disabled="!selectedExam">开始监控</Button>
        </Col>
        <Col span="4">
          <Button @click="stopMonitoring" :disabled="!isMonitoring">停止监控</Button>
        </Col>
        <Col span="4">
          <Button type="success" @click="exportPassedStudents" :disabled="!passedStudents.length">导出通过名单</Button>
        </Col>
      </Row>

      <!-- 实时状态显示 -->
      <Row :gutter="16" style="margin-bottom: 20px">
        <Col span="6">
          <Card>
            <div style="text-align: center">
              <h2 style="color: #2d8cf0; margin: 0">{{ examStats.totalStudents }}</h2>
              <p style="margin: 5px 0 0 0; color: #666">总参考人数</p>
            </div>
          </Card>
        </Col>
        <Col span="6">
          <Card>
            <div style="text-align: center">
              <h2 style="color: #19be6b; margin: 0">{{ examStats.passedCount }}</h2>
              <p style="margin: 5px 0 0 0; color: #666">已通过人数</p>
            </div>
          </Card>
        </Col>
        <Col span="6">
          <Card>
            <div style="text-align: center">
              <h2 style="color: #ff9900; margin: 0">{{ examStats.passRate }}%</h2>
              <p style="margin: 5px 0 0 0; color: #666">通过率</p>
            </div>
          </Card>
        </Col>
        <Col span="6">
          <Card>
            <div style="text-align: center">
              <h2 style="color: #ed4014; margin: 0">{{ examStats.duration }}</h2>
              <p style="margin: 5px 0 0 0; color: #666">考试持续时间</p>
            </div>
          </Card>
        </Col>
      </Row>

      <!-- 警报区域 -->
      <div v-if="showAlert" style="margin-bottom: 20px">
        <Alert type="error" show-icon banner>
          <template slot="desc">
            <Icon type="ios-warning" style="font-size: 16px; margin-right: 8px;" />
            <strong>⚠️ 紧急警报：</strong>考试已进行 <strong>{{ examStats.duration }}</strong>，当前无人通过！
            <br>
            <Icon type="ios-bulb" style="margin-right: 4px;" />
            建议立即检查考试难度或学生准备情况，考虑是否需要调整考试安排。
          </template>
        </Alert>
      </div>

      <!-- 监控状态指示 -->
      <div v-if="isMonitoring" style="margin-bottom: 20px">
        <Alert type="info" show-icon>
          <Icon type="ios-pulse" style="animation: pulse 1.5s infinite; margin-right: 8px;" />
          正在实时监控考试 <strong>{{ selectedExam }}</strong> 的通过情况...
          <span style="float: right">
            <Icon type="ios-time" style="margin-right: 4px;" />
            最后更新：{{ lastUpdateTime }}
          </span>
        </Alert>
      </div>

      <!-- 通过学生列表 -->
      <Tabs v-model="activeTab">
        <TabPane label="已通过学生" name="passed">
          <Table 
            :data="passedStudents" 
            :columns="passedStudentsColumns" 
            :loading="loading"
            stripe
          />
          <div v-if="passedStudents.length" style="margin-top: 16px">
            <Tag color="green">{{ passedStudents.length }} 名学生已通过考试</Tag>
          </div>
        </TabPane>
        
        <TabPane label="未通过学生" name="failed">
          <Table 
            :data="failedStudents" 
            :columns="failedStudentsColumns" 
            :loading="loading"
            stripe
          />
          <div v-if="failedStudents.length" style="margin-top: 16px">
            <Tag color="red">{{ failedStudents.length }} 名学生尚未通过考试</Tag>
          </div>
        </TabPane>

        <TabPane label="考试进度统计" name="progress">
          <div style="height: 300px; display: flex; align-items: center; justify-content: center">
            <div style="text-align: center">
              <Icon type="ios-stats" size="60" style="color: #ccc" />
              <p style="margin-top: 20px; color: #999">考试进度图表区域</p>
              <p style="color: #999">（此处可集成图表组件显示实时进度）</p>
            </div>
          </div>
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<script>
import { getErrModalOptions } from '@/libs/util'
import moment from 'moment'

export default {
  name: 'ExamProgressDetection',
  data() {
    return {
      selectedExam: null,
      examList: [],
      isMonitoring: false,
      monitoringTimer: null,
      lastUpdateTime: '',
      activeTab: 'passed',
      loading: false,
      showAlert: false,
      
      // 考试统计数据
      examStats: {
        totalStudents: 0,
        passedCount: 0,
        passRate: 0,
        duration: '00:00:00'
      },
      
      // 已通过学生
      passedStudents: [],
      passedStudentsColumns: [
        {
          title: '学号',
          key: 'student_id',
          width: 120
        },
        {
          title: '姓名',
          key: 'student_name',
          width: 120
        },
        {
          title: '通过时间',
          key: 'pass_time',
          width: 160
        },
        {
          title: '用时',
          key: 'duration',
          width: 100,
          render: (h, params) => {
            const duration = params.row.duration
            const color = duration < 30 ? 'green' : duration < 60 ? 'orange' : 'red'
            return h('Tag', { props: { color } }, duration + '分钟')
          }
        },
        {
          title: '项目',
          key: 'project_name',
          width: 150
        },
        {
          title: '得分',
          key: 'score',
          width: 80,
          render: (h, params) => {
            const score = params.row.score
            const color = score >= 90 ? 'green' : score >= 70 ? 'orange' : 'red'
            return h('Tag', { props: { color } }, score)
          }
        }
      ],
      
      // 未通过学生
      failedStudents: [],
      failedStudentsColumns: [
        {
          title: '学号',
          key: 'student_id',
          width: 120
        },
        {
          title: '姓名',
          key: 'student_name',
          width: 120
        },
        {
          title: '当前状态',
          key: 'status',
          width: 120,
          render: (h, params) => {
            const status = params.row.status
            let color = 'blue'
            if (status === '答题中') color = 'orange'
            else if (status === '未开始') color = 'default'
            else if (status === '已提交') color = 'purple'
            return h('Tag', { props: { color } }, status)
          }
        },
        {
          title: '开始时间',
          key: 'start_time',
          width: 160
        },
        {
          title: '已用时',
          key: 'elapsed_time',
          width: 100
        },
        {
          title: '项目',
          key: 'project_name',
          width: 150
        },
        {
          title: '当前得分',
          key: 'current_score',
          width: 100,
          render: (h, params) => {
            const score = params.row.current_score
            if (score === null || score === undefined) {
              return h('span', { style: { color: '#ccc' } }, '未评分')
            }
            const color = score >= 60 ? 'green' : 'red'
            return h('Tag', { props: { color } }, score)
          }
        }
      ]
    }
  },
  
  mounted() {
    this.loadExamList()
  },
  
  beforeDestroy() {
    this.stopMonitoring()
  },
  
  methods: {
    // 加载考试列表
    loadExamList() {
      // 模拟API调用
      setTimeout(() => {
        this.examList = [
          { id: 1, date: '2024-01-16' },
          { id: 2, date: '2024-01-15' },
          { id: 3, date: '2024-01-14' }
        ]
      }, 500)
    },
    
    // 考试选择变化
    onExamChange(examId) {
      this.selectedExam = examId
      this.loadExamData()
    },
    
    // 加载考试数据
    loadExamData() {
      if (!this.selectedExam) return
      
      this.loading = true
      
      // 模拟API调用
      setTimeout(() => {
        // 模拟考试统计数据
        this.examStats = {
          totalStudents: 45,
          passedCount: 12,
          passRate: Math.round((12 / 45) * 100),
          duration: '01:23:45'
        }
        
        // 模拟已通过学生数据
        this.passedStudents = [
          {
            student_id: '20231001',
            student_name: '张三',
            pass_time: '2024-01-16 10:30:00',
            duration: 25,
            project_name: 'P1-基础算法',
            score: 95
          },
          {
            student_id: '20231002',
            student_name: '李四',
            pass_time: '2024-01-16 10:45:00',
            duration: 40,
            project_name: 'P1-基础算法',
            score: 88
          }
        ]
        
        // 模拟未通过学生数据
        this.failedStudents = [
          {
            student_id: '20231003',
            student_name: '王五',
            status: '答题中',
            start_time: '2024-01-16 09:00:00',
            elapsed_time: '83分钟',
            project_name: 'P1-基础算法',
            current_score: 45
          },
          {
            student_id: '20231004',
            student_name: '赵六',
            status: '未开始',
            start_time: '-',
            elapsed_time: '0分钟',
            project_name: 'P1-基础算法',
            current_score: null
          }
        ]
        
        this.loading = false
        
        // 检查是否需要显示警报
        this.checkAlert()
      }, 1000)
    },
    
    // 开始监控
    startMonitoring() {
      if (!this.selectedExam) {
        this.$Message.warning('请先选择考试')
        return
      }
      
      this.isMonitoring = true
      this.updateLastUpdateTime()
      
      // 设置定时器，每30秒更新一次数据
      this.monitoringTimer = setInterval(() => {
        this.loadExamData()
        this.updateLastUpdateTime()
      }, 30000)
      
      this.$Message.success('开始监控考试进度')
    },
    
    // 停止监控
    stopMonitoring() {
      if (this.monitoringTimer) {
        clearInterval(this.monitoringTimer)
        this.monitoringTimer = null
      }
      this.isMonitoring = false
      this.$Message.info('已停止监控')
    },
    
    // 更新最后更新时间
    updateLastUpdateTime() {
      this.lastUpdateTime = moment().format('HH:mm:ss')
    },
    
    // 检查是否需要显示警报
    checkAlert() {
      // 如果考试进行超过30分钟且无人通过，显示警报
      const durationParts = this.examStats.duration.split(':')
      const totalMinutes = parseInt(durationParts[0]) * 60 + parseInt(durationParts[1])
      
      this.showAlert = totalMinutes > 30 && this.examStats.passedCount === 0
    },
    
    // 导出通过学生名单
    exportPassedStudents() {
      if (!this.passedStudents.length) {
        this.$Message.warning('没有通过的学生可导出')
        return
      }
      
      const headers = ['学号', '姓名', '通过时间', '用时(分钟)', '项目', '得分']
      const csvContent = [
        headers.join(','),
        ...this.passedStudents.map(student => [
          student.student_id,
          student.student_name,
          student.pass_time,
          student.duration,
          student.project_name,
          student.score
        ].join(','))
      ].join('\n')
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `考试${this.selectedExam}_通过名单.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      this.$Message.success('导出成功')
    }
  }
}
</script>

<style scoped>
.ivu-card {
  margin: 20px;
}

.ivu-table {
  margin-top: 16px;
}

.ivu-form-item {
  margin-bottom: 0;
}

.ivu-alert {
  margin-bottom: 16px;
}

.ivu-card .ivu-card-body {
  padding: 16px;
}

/* 脉冲动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 统计卡片悬停效果 */
.ivu-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}
</style>
