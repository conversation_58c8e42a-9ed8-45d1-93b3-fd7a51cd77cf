<template>
  <div>
    <Card>
      <h2 slot="title">考试通过人数检测</h2>
      <div slot="extra">
        <Select v-model="selectedExam" style="width: 250px" @on-change="onExamChange">
          <Option v-for="exam in examList" :key="exam.id" :value="exam.id">
            {{ exam.id }} : {{ exam.date }} - {{ exam.name }}
          </Option>
        </Select>
      </div>

      <Row :gutter="16" style="margin-bottom: 16px">
        <Col span="8">
          <Card>
            <Statistic title="当前通过人数" :value="passedCount" :value-style="{ color: '#3f8600' }">
              <template slot="suffix">
                <Icon type="ios-people" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col span="8">
          <Card>
            <Statistic title="总参考人数" :value="totalCount" :value-style="{ color: '#1890ff' }">
              <template slot="suffix">
                <Icon type="ios-person" />
              </template>
            </Statistic>
          </Card>
        </Col>
        <Col span="8">
          <Card>
            <Statistic title="通过率" :value="passRate" suffix="%" :value-style="{ color: passRate > 50 ? '#3f8600' : '#cf1322' }">
              <template slot="suffix">
                <Icon type="ios-trending-up" />
              </template>
            </Statistic>
          </Card>
        </Col>
      </Row>

      <Row :gutter="16" style="margin-bottom: 16px">
        <Col span="12">
          <Card>
            <h3 slot="title">考试持续时间</h3>
            <div style="font-size: 24px; color: #1890ff;">
              {{ examDuration }}
            </div>
            <div style="color: #666; margin-top: 8px;">
              开始时间: {{ examStartTime }}
            </div>
          </Card>
        </Col>
        <Col span="12">
          <Card>
            <h3 slot="title">无人通过警报</h3>
            <div v-if="noPassAlert" style="color: #cf1322; font-size: 18px;">
              <Icon type="ios-warning" style="margin-right: 8px;" />
              已超过 {{ alertThreshold }} 分钟无人通过！
            </div>
            <div v-else style="color: #3f8600; font-size: 18px;">
              <Icon type="ios-checkmark-circle" style="margin-right: 8px;" />
              考试进行正常
            </div>
            <div style="margin-top: 12px;">
              <span>警报阈值: </span>
              <InputNumber 
                v-model="alertThreshold" 
                :min="5" 
                :max="60" 
                size="small" 
                style="width: 80px;"
                @on-change="updateAlertThreshold"
              />
              <span> 分钟</span>
            </div>
          </Card>
        </Col>
      </Row>

      <Card>
        <div slot="title">
          <span>实时通过情况</span>
          <Button 
            type="primary" 
            size="small" 
            @click="refreshData" 
            :loading="loading"
            style="margin-left: 16px;"
          >
            刷新数据
          </Button>
          <Switch 
            v-model="autoRefresh" 
            @on-change="toggleAutoRefresh"
            style="margin-left: 16px;"
          >
            <span slot="open">自动</span>
            <span slot="close">手动</span>
          </Switch>
        </div>

        <Tabs :value="activeTab" @on-click="onTabChange">
          <TabPane label="通过学生列表" name="passed">
            <Table 
              :data="passedStudents" 
              :columns="passedStudentsColumns" 
              :loading="loading"
              size="small"
            />
          </TabPane>
          
          <TabPane label="未通过学生列表" name="not-passed">
            <Table 
              :data="notPassedStudents" 
              :columns="notPassedStudentsColumns" 
              :loading="loading"
              size="small"
            />
          </TabPane>
          
          <TabPane label="通过趋势图" name="trend">
            <div ref="trendChart" style="height: 400px;"></div>
          </TabPane>
        </Tabs>
      </Card>
    </Card>
  </div>
</template>

<script>
import { examPassDetectionReq } from '@/api/progress'
import { examReq } from '@/api/exam'
import { getErrModalOptions } from '@/libs/util'
import { WhitePre, Tag } from '@/libs/render-item'
import * as echarts from 'echarts'

export default {
  name: 'ExamProgressDetection',
  data() {
    return {
      selectedExam: null,
      examList: [],
      activeTab: 'passed',
      loading: false,
      autoRefresh: false,
      refreshTimer: null,
      alertThreshold: 30,
      
      // 统计数据
      passedCount: 0,
      totalCount: 0,
      examDuration: '00:00:00',
      examStartTime: '',
      noPassAlert: false,
      lastPassTime: null,
      
      // 学生数据
      passedStudents: [],
      notPassedStudents: [],
      
      // 表格列定义
      passedStudentsColumns: [
        {
          title: '学号',
          key: 'student_id',
          render: (h, params) => WhitePre(h, params.row.student_id)
        },
        {
          title: '姓名',
          key: 'student_name',
          render: (h, params) => WhitePre(h, params.row.student_name)
        },
        {
          title: '班级',
          key: 'class_name'
        },
        {
          title: '通过时间',
          key: 'pass_time'
        },
        {
          title: '用时',
          key: 'duration',
          render: (h, params) => Tag(h, 'green', params.row.duration)
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => Tag(h, 'success', '已通过')
        }
      ],
      
      notPassedStudentsColumns: [
        {
          title: '学号',
          key: 'student_id',
          render: (h, params) => WhitePre(h, params.row.student_id)
        },
        {
          title: '姓名',
          key: 'student_name',
          render: (h, params) => WhitePre(h, params.row.student_name)
        },
        {
          title: '班级',
          key: 'class_name'
        },
        {
          title: '当前进度',
          key: 'current_progress'
        },
        {
          title: '已用时间',
          key: 'elapsed_time',
          render: (h, params) => Tag(h, 'blue', params.row.elapsed_time)
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => Tag(h, 'warning', '进行中')
        }
      ],
      
      // 图表数据
      trendChart: null,
      trendData: []
    }
  },
  computed: {
    passRate() {
      return this.totalCount > 0 ? Math.round((this.passedCount / this.totalCount) * 100) : 0
    }
  },
  mounted() {
    this.loadExams()
    this.initTrendChart()
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    if (this.trendChart) {
      this.trendChart.dispose()
    }
  },
  methods: {
    async loadExams() {
      try {
        const res = await examReq('get', { order_by: '-date' })
        this.examList = res.data.exams || []
        if (this.examList.length > 0) {
          // 优先选择激活的考试
          const activeExam = this.examList.find(exam => exam.active)
          this.selectedExam = activeExam ? activeExam.id : this.examList[0].id
          this.loadExamData()
        }
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    
    onExamChange() {
      this.loadExamData()
    },
    
    onTabChange(name) {
      this.activeTab = name
      if (name === 'trend') {
        this.$nextTick(() => {
          this.updateTrendChart()
        })
      }
    },
    
    async loadExamData() {
      if (!this.selectedExam) return
      
      this.loading = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 800))
        
        // 模拟数据
        this.passedCount = 45
        this.totalCount = 120
        this.examStartTime = '2024-01-16 14:00:00'
        this.examDuration = '01:23:45'
        this.lastPassTime = new Date(Date.now() - 5 * 60 * 1000) // 5分钟前
        
        this.passedStudents = [
          {
            student_id: '2021001',
            student_name: '张三',
            class_name: '计科21-1',
            pass_time: '14:25:30',
            duration: '25分30秒'
          },
          {
            student_id: '2021002',
            student_name: '李四',
            class_name: '计科21-1',
            pass_time: '14:32:15',
            duration: '32分15秒'
          },
          {
            student_id: '2021003',
            student_name: '王五',
            class_name: '计科21-2',
            pass_time: '14:18:45',
            duration: '18分45秒'
          }
        ]
        
        this.notPassedStudents = [
          {
            student_id: '2021004',
            student_name: '赵六',
            class_name: '计科21-2',
            current_progress: 'P2-第3题',
            elapsed_time: '1小时23分'
          },
          {
            student_id: '2021005',
            student_name: '钱七',
            class_name: '计科21-3',
            current_progress: 'P1-第2题',
            elapsed_time: '45分钟'
          }
        ]
        
        this.checkNoPassAlert()
        this.updateTrendData()
        
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      } finally {
        this.loading = false
      }
    },
    
    refreshData() {
      this.loadExamData()
    },
    
    toggleAutoRefresh(enabled) {
      if (enabled) {
        this.refreshTimer = setInterval(() => {
          this.loadExamData()
        }, 30000) // 30秒刷新一次
        this.$Message.success('已开启自动刷新')
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
        this.$Message.info('已关闭自动刷新')
      }
    },
    
    updateAlertThreshold() {
      this.checkNoPassAlert()
    },
    
    checkNoPassAlert() {
      if (this.lastPassTime) {
        const timeDiff = (Date.now() - this.lastPassTime.getTime()) / (1000 * 60) // 分钟
        this.noPassAlert = timeDiff > this.alertThreshold
      }
    },
    
    initTrendChart() {
      this.$nextTick(() => {
        if (this.$refs.trendChart) {
          this.trendChart = echarts.init(this.$refs.trendChart)
          this.updateTrendChart()
        }
      })
    },
    
    updateTrendChart() {
      if (!this.trendChart) return
      
      const option = {
        title: {
          text: '考试通过人数趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.trendData.map(item => item.time)
        },
        yAxis: {
          type: 'value',
          name: '通过人数'
        },
        series: [{
          data: this.trendData.map(item => item.count),
          type: 'line',
          smooth: true,
          areaStyle: {
            opacity: 0.3
          }
        }]
      }
      
      this.trendChart.setOption(option)
    },
    
    updateTrendData() {
      // 模拟趋势数据
      const now = new Date()
      this.trendData = []
      for (let i = 0; i < 10; i++) {
        const time = new Date(now.getTime() - (9 - i) * 10 * 60 * 1000)
        this.trendData.push({
          time: time.toLocaleTimeString().slice(0, 5),
          count: Math.floor(Math.random() * 20) + i * 5
        })
      }
    }
  }
}
</script>

<style scoped>
.ivu-statistic {
  text-align: center;
}

.ivu-card {
  margin-bottom: 16px;
}
</style>
