# Generated by Django 2.2.5 on 2019-09-11 13:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminUploadedFile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=100)),
                ('oss_token', models.CharField(blank=True, max_length=300)),
                ('uploaded_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                           to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Problem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                 to=settings.AUTH_USER_MODEL)),
                ('problem_data', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                   to='judge.AdminUploadedFile')),
            ],
        ),
        migrations.CreateModel(
            name='ProblemJudgeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin_id', models.IntegerField(blank=True, null=True)),
                ('edx_username', models.CharField(max_length=50)),
                ('student_comment', models.CharField(blank=True, max_length=400, null=True)),
                ('judger_identifier', models.CharField(blank=True, max_length=20, null=True)),
                ('judge_result',
                 models.IntegerField(choices=[(-1, 'Judging'), (0, 'Passed'), (1, 'Failed')], default=-1)),
                ('course_code', models.CharField(blank=True, max_length=100, null=True)),
                ('chapter_code', models.CharField(blank=True, max_length=100, null=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='TestCase',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('judge_parameter', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                 to=settings.AUTH_USER_MODEL)),
                ('judge_data', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                 to='judge.AdminUploadedFile')),
            ],
        ),
        migrations.CreateModel(
            name='UserSubmittedFile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('edx_username', models.CharField(max_length=50)),
                ('filename', models.CharField(max_length=100)),
                ('oss_token', models.CharField(blank=True, max_length=300)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='TestCaseJudgeRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('judge_result', models.IntegerField(
                    choices=[(-1, 'Judging'), (0, 'Accepted'), (1, 'Wrong Answer'), (2, 'Runtime Error'),
                             (3, 'Memory Limit Exceeded'), (4, 'Time Limit Exceeded'), (5, 'Function Limit Violation'),
                             (6, 'Compilation Error'), (7, 'Presentation Error'), (8, 'Unknown Error')])),
                ('raw_output', models.TextField(blank=True, null=True)),
                ('comment', models.TextField()),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('problem_judge_record',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='judge.ProblemJudgeRecord')),
                ('test_case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='judge.TestCase')),
            ],
        ),
        migrations.AddField(
            model_name='problemjudgerecord',
            name='attachment',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                    to='judge.UserSubmittedFile'),
        ),
        migrations.AddField(
            model_name='problemjudgerecord',
            name='problem',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='judge.Problem'),
        ),
        migrations.AddField(
            model_name='problem',
            name='test_cases',
            field=models.ManyToManyField(blank=True, to='judge.TestCase'),
        ),
        migrations.CreateModel(
            name='JudgeParameterTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parameter', models.TextField()),
                ('description', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL,
                                                 to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
