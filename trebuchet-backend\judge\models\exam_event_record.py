"""
declare ExamEventRecord model
"""
from django.db import models
from django.contrib.auth import get_user_model

from judge.models.permissions import VIEW_EXAM_PASS
from core.models.project_in_exam import ProjectInExam


class ExamEventRecord(models.Model):
    """
    Some event happened during an exam.
    Manually recorded with time for afterwards analysis.
    """
    project_in_exam = models.ForeignKey(ProjectInExam, on_delete=models.CASCADE)
    happened_at = models.DateTimeField(blank=True, null=True)
    title = models.TextField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        get_user_model(), on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return "ExamEventRecord-" + str(self.pk)

    class Meta:
        default_permissions = ()
        permissions = [
            (VIEW_EXAM_PASS, VIEW_EXAM_PASS)
        ]
